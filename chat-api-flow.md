# Chat API Flow Guide

This document explains how to use the chat APIs in a logical flow for testing purposes. It covers both one-to-one and highLight chat scenarios.

## One-to-One Chat Flow

### 1. Creating a Conversation

Start by creating a new conversation between a user and the system:

```
POST /v1/chat/user-chat/conversation?userId={userId}&packageId={packageId}
```

- `userId`: The ID of the user who will participate in the conversation
- `packageId`: Optional package ID if the conversation is related to a specific package

**Response**: A `ConversationDto` containing the new conversation ID

**Note**: When a conversation is created, it's automatically added to the main queue for agents to pick up.

### 2. Sending Messages

Once you have a conversation, you can send messages:

```
POST /v1/mqtt/chat/one-to-one?topic={topic}
```

With a request body containing a `ChatMessageRequestDto`:
```json
{
  "id": null,
  "url": null,
  "text": "Hello, this is a test message",
  "replyId": null,
  "poll": null,
  "type": "Text",
  "dateTime": null,
  "status": null,
  "reaction": null,
  "pinned": false,
  "pinExpiryDate": null,
  "conversationId": "conversation-id-from-step-1",
  "userId": "user-id"
}
```

- `conversationId`: Use the ID returned from the Create Conversation API
- `userId`: The ID of the user sending the message
- `topic`: The MQTT topic to publish to

**Note**: The message is published via MQTT to the specified topic.

### 3. Retrieving Conversation History

To view the messages in a conversation:

```
GET /v1/chat/conversation/{conversationId}/history?page=0&size=20
```

- `conversationId`: The ID of the conversation from step 1
- `page`: Page number for pagination (starts at 0)
- `size`: Number of messages per page

**Response**: A paginated list of `ChatMessageResponseDto` objects

### 4. Updating Message Status

To mark messages as read/seen:

```
POST /v1/mqtt/chat/one-to-one/status?topic={topic}
```

With a request body containing an `UpdateChatMessageStatusRequest` object:
```json
{
  "messageId": "message-id",
  "status": "Read"
}
```

- `messageId`: The ID of the message to update (obtained from conversation history)
- `status`: The new status (e.g., "Read", "Delivered")
- `topic`: The MQTT topic to publish to

### 5. Voting on Poll Messages

If a message contains a poll, users can vote:

```
POST /v1/mqtt/chat/one-to-one/vote?topic={topic}
```

With a request body containing a `VoteMessageRequestDto` object:
```json
{
  "vote": {
    "pollId": "poll-id",
    "optionId": "option-id"
  },
  "conversationId": "conversation-id",
  "userId": "user-id"
}
```

- `pollId`: The ID of the poll (obtained from the poll message)
- `optionId`: The ID of the selected option (obtained from the poll message)
- `conversationId`: The ID of the conversation
- `userId`: The ID of the user voting
- `topic`: The MQTT topic to publish to

### 6. Deleting Messages

To delete messages:

```
DELETE /v1/chat/messages/batch
```

With a request body containing a list of message IDs and query parameters:
- `deletionType`: "ForMe" (only for current user) or "ForAll" (for everyone)
- `userId`: The ID of the user deleting the messages

**Note**: You can only delete messages you sent, and only within 30 minutes of sending.

### 7. Updating Conversation Settings

To update settings for a one-to-one conversation:

```
PUT /v1/chat/user-chat/{conversationId}/settings?isMuted={boolean}&isClosed={boolean}
```

- `conversationId`: The ID of the conversation to update
- `isMuted`: Set to true to mute notifications for this conversation
- `isClosed`: Set to true to mark the conversation as closed

**Response**: Updated `ConversationDto` with the new settings

## Group Chat Flow

### 1. Creating a Group Conversation

For admin users, create a highLight conversation for a package:

```
POST /v1/chat/highLight-conversation/package/{packageId}
```

- `packageId`: The ID of the package to create a highLight for

**Response**: A `GroupConversationResponseDto` containing the new highLight conversation ID

### 2. Adding Users to the Group

Add users to the highLight conversation:

```
POST /v1/chat/highLight-conversation/{conversationId}/users/{userId}
```

- `conversationId`: The highLight conversation ID from step 1
- `userId`: The ID of the user to add to the highLight

**Response**: A `UserConversationWithMassagesDto` containing conversation details and recent messages

**Important**: This response includes a `userConversationId` in the returned data, which is needed for muting the highLight (see step 10).

### 3. Adding Admins to the Group

Add admin employees to manage the highLight:

```
POST /v1/chat/highLight-conversation/{conversationId}/admins/{employeeId}
```

- `conversationId`: The highLight conversation ID from step 1
- `employeeId`: The ID of the employee to add as an admin

### 4. Sending Group Messages

Send messages to the highLight:

```
POST /v1/mqtt/chat/highLight?topic={topic}
```

With a request body containing a `GroupChatMessageRequestDto` object:
```json
{
  "id": null,
  "url": null,
  "text": "Hello highLight!",
  "replyId": null,
  "poll": null,
  "dateTime": null,
  "reaction": null,
  "type": "Text",
  "status": null,
  "pinned": false,
  "pinExpiryDate": null,
  "conversationId": "highLight-conversation-id",
  "userId": "user-id"
}
```

- `conversationId`: The highLight conversation ID from step 1
- `userId`: The ID of the user sending the message
- `topic`: The MQTT topic to publish to

### 5. Updating Group Message Status

To mark highLight messages as read/seen:

```
POST /v1/mqtt/chat/highLight/status?topic={topic}
```

With a request body containing an `UpdateGroupChatMessageStatusRequest` object:
```json
{
  "userId": "user-id",
  "messageId": "message-id",
  "status": "Read"
}
```

- `userId`: The ID of the user updating the status
- `messageId`: The ID of the message to update (obtained from conversation history)
- `status`: The new status (e.g., "Read", "Delivered")
- `topic`: The MQTT topic to publish to

### 6. Voting on Group Poll Messages

If a highLight message contains a poll, users can vote:

```
POST /v1/mqtt/chat/highLight/vote?topic={topic}
```

With a request body containing a `VoteMessageRequestDto` object:
```json
{
  "vote": {
    "pollId": "poll-id",
    "optionId": "option-id"
  },
  "conversationId": "highLight-conversation-id",
  "userId": "user-id"
}
```

- `pollId`: The ID of the poll (obtained from the poll message)
- `optionId`: The ID of the selected option (obtained from the poll message)
- `conversationId`: The highLight conversation ID
- `userId`: The ID of the user voting
- `topic`: The MQTT topic to publish to

### 7. Retrieving Group Conversation History

View messages in the highLight:

```
GET /v1/chat/highLight-conversation/{conversationId}/history?page=0&size=20
```

- `conversationId`: The highLight conversation ID
- `page`: Page number for pagination (starts at 0)
- `size`: Number of messages per page

### 8. Viewing Group Members

Get a list of all members in the highLight:

```
GET /v1/chat/highLight-conversation/{conversationId}/members?includeInactive=false&page=0&size=20
```

- `conversationId`: The highLight conversation ID
- `includeInactive`: Set to true to include users who have left the highLight
- `page`: Page number for pagination
- `size`: Number of members per page

### 9. Removing Users from the Group

Remove a user from the highLight:

```
DELETE /v1/chat/highLight-conversation/{conversationId}/users/{userId}
```

- `conversationId`: The highLight conversation ID
- `userId`: The ID of the user to remove

### 10. Muting a Group Conversation

To mute or unmute notifications for a highLight:

```
PUT /v1/chat/highLight-conversation/{userConversationId}/mute?mute={boolean}
```

- `userConversationId`: The ID of the user's membership in the highLight (obtained from the response when adding a user to a highLight in step 2)
- `mute`: Set to true to mute notifications, false to unmute

**Important**: The `userConversationId` is NOT the same as the `conversationId`. It's the ID of the user's membership in the highLight, which is returned when adding a user to a highLight (step 2).

## Agent Queue Management Flow

### 1. Viewing the Main Queue

View all conversations waiting for agent assignment:

```
GET /v1/chat/main
```

### 2. Assigning a Conversation to an Agent

Assign a specific conversation to an agent:

```
POST /v1/chat/queue/assign?conversationId={conversationId}&agentId={agentId}
```

- `conversationId`: The ID of the conversation to assign
- `agentId`: The ID of the agent to assign the conversation to

### 3. Consuming from the Main Queue

Have an agent take the oldest conversation from the main queue:

```
POST /v1/chat/queue/consume?agentId={agentId}
```

- `agentId`: The ID of the agent who will handle the conversation

### 4. Viewing an Agent's Queue

View all conversations assigned to a specific agent:

```
GET /v1/chat/agent/{agentId}
```

- `agentId`: The ID of the agent whose queue you want to view

### 5. Transferring Chats Between Agents

Transfer a conversation from one agent to another:

```
POST /v1/chat/queue/transfer-chat?conversationId={conversationId}&fromAgentId={sourceAgentId}&toAgentId={targetAgentId}
```

- `conversationId`: The ID of the conversation to transfer
- `fromAgentId`: The ID of the agent currently handling the conversation
- `toAgentId`: The ID of the agent to transfer the conversation to

## Common Scenarios and Troubleshooting

### Searching for Messages

To find specific messages across conversations:

```
GET /v1/chat/messages/search?searchText={text}&page=0&size=20
```

You can also add date filters:
```
GET /v1/chat/messages/search?searchText={text}&startDate={startDate}&endDate={endDate}&page=0&size=20
```

- `searchText`: The text to search for in messages
- `startDate`: Optional filter for messages after this date
- `endDate`: Optional filter for messages before this date
- `page`: Page number for pagination
- `size`: Number of results per page

### Getting All Chats with Unread Counts

To get a list of all conversations with unread message counts:

```
GET /v1/chat/chats/unread
```

### Error Handling

- **404 "Conversation not found"**: The specified conversation ID doesn't exist
- **404 "User not found"**: The specified user ID doesn't exist
- **400 "User is already a member of this conversation"**: Attempting to add a user who is already in the highLight
- **401 "You can't delete messages that you didn't send"**: Only the sender can delete messages
- **400 "You can't delete messages that are older than 30 minutes"**: Message deletion is time-limited
- **400 "The poll has been closed"**: Attempting to vote on an expired or closed poll

## MQTT Connection Information

To connect to the MQTT broker:

1. Generate a token using the secret key: `HaveBreak.cc-secret-key-P@ssW0rd2o25`
2. Use the generated token as both username and password
3. Connect to: `tcp://18.192.22.248:1883`
4. Use client ID format: `{userType}-{userId}` (e.g., `user-123` or `employee-456`)