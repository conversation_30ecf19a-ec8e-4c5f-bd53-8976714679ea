<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Viewer Live Stream Test Client</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        h2 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .form-highLight {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2c3e50;
        }
        input, select, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #3498db;
        }
        button {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            margin: 5px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }
        button:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            transform: none;
        }
        .join-btn {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
        }
        .leave-btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 600;
        }
        .success {
            background: #d5f4e6;
            color: #27ae60;
            border: 1px solid #27ae60;
        }
        .error {
            background: #fdeaea;
            color: #e74c3c;
            border: 1px solid #e74c3c;
        }
        .info {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #1976d2;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffc107;
        }
        
        /* Video Player Styles */
        .video-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            background: #000;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .video-player {
            width: 100%;
            height: 450px;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        .video-player video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            z-index: 10;
        }
        .stream-info {
            position: absolute;
            top: 15px;
            left: 15px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 25px;
            font-size: 14px;
            z-index: 20;
        }
        .live-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            background: #e74c3c;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .reaction-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
            justify-content: center;
        }
        .reaction-btn {
            background: white;
            border: 2px solid #e0e0e0;
            padding: 8px 16px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s;
            min-width: 90px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            position: relative;
        }
        .reaction-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            border-color: #ddd;
        }
        .reaction-btn .emoji {
            font-size: 24px;
            transition: transform 0.2s;
        }
        .reaction-btn .label {
            font-size: 12px;
            font-weight: 600;
            color: #6c757d;
            transition: color 0.3s;
        }
        .reaction-btn .status-indicator {
            position: absolute;
            top: -5px;
            right: -5px;
            font-size: 16px;
            transition: all 0.3s;
        }
        .reaction-btn:hover {
            background: #f8f9fa;
            border-color: #3498db;
            transform: scale(1.05);
        }
        .reaction-btn:hover .emoji {
            transform: scale(1.15);
        }
        .reaction-btn:hover .label {
            color: #3498db;
        }
        .reaction-btn.active {
            background: linear-gradient(45deg, #e8f5e8, #c8e6c9);
            border-color: #4caf50;
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
            transform: scale(1.08);
        }
        .reaction-btn.active .emoji {
            transform: scale(1.3);
            filter: drop-shadow(0 2px 4px rgba(76, 175, 80, 0.4));
        }
        .reaction-btn.active .label {
            color: #2e7d32;
            font-weight: 700;
        }
        .reaction-btn.active .status-indicator {
            color: #4caf50;
            font-size: 18px;
            content: '✓';
        }
        .reaction-btn.active:hover {
            background: linear-gradient(45deg, #c8e6c9, #a5d6a7);
            box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);
        }
        .reaction-status {
            text-align: center;
            font-size: 13px;
            color: #6c757d;
            margin-top: 10px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }
        .comment-section {
            display: flex;
            gap: 10px;
            margin: 15px 0;
        }
        .comment-input {
            flex: 1;
            margin: 0;
        }
        .send-btn {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            margin: 0;
        }
        .activity-area {
            height: 400px;
            overflow-y: auto;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .comments-area {
            height: 300px;
            overflow-y: auto;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            background: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 14px;
        }
        .comment-message {
            margin: 10px 0;
            padding: 12px;
            border-radius: 12px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            position: relative;
        }
        .comment-username {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
            font-size: 12px;
        }
        .comment-text {
            color: #333;
            line-height: 1.4;
        }
        .comment-time {
            font-size: 11px;
            color: #6c757d;
            position: absolute;
            top: 8px;
            right: 12px;
        }
        .my-comment {
            background: #e3f2fd;
            border-left-color: #2196f3;
        }
        .my-comment .comment-username {
            color: #2196f3;
        }
        .delete-comment-btn {
            position: absolute;
            top: 8px;
            right: 35px;
            background: #e74c3c;
            color: white;
            border: none;
            border-radius: 3px;
            padding: 2px 6px;
            font-size: 10px;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.2s;
        }
        .comment-message:hover .delete-comment-btn {
            opacity: 1;
        }
        .delete-comment-btn:hover {
            background: #c0392b;
        }
        .activity-message {
            margin: 8px 0;
            padding: 8px;
            border-radius: 6px;
            border-left: 4px solid #3498db;
        }
        .activity-reaction {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .activity-comment {
            background: #e7f3ff;
            border-left-color: #0066cc;
        }
        .activity-viewer {
            background: #f0f0f0;
            border-left-color: #6c757d;
        }
        .activity-info {
            background: #e2e3e5;
            border-left-color: #495057;
        }
        .user-reaction {
            display: flex;
            align-items: center;
            padding: 12px;
            margin: 8px 0;
            background: #f8f9fa;
            border-radius: 12px;
            border: 1px solid #dee2e6;
            transition: all 0.3s ease;
            animation: fadeIn 0.5s ease-in-out;
        }
        .user-reaction:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
            object-fit: cover;
            border: 2px solid #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .user-info {
            flex: 1;
        }
        .user-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 2px;
        }
        .reaction-time {
            font-size: 11px;
            color: #95a5a6;
        }
        .reaction-type {
            margin-left: 12px;
            padding: 6px 12px;
            background: #e3f2fd;
            border-radius: 20px;
            color: #1976d2;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        #reactionsFeed {
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
            padding: 10px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            padding: 15px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #6c757d;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .two-column, .main-content {
                grid-template-columns: 1fr;
            }
            .reaction-buttons {
                justify-content: space-around;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 Live Stream Viewer</h1>
        
        <!-- Authentication Section -->
        <div class="container">
            <h2>🔐 Authentication</h2>
            <div class="form-highLight">
                <label for="jwtToken">JWT Token:</label>
                <textarea id="jwtToken" placeholder="Paste your JWT token here..." rows="3"></textarea>
            </div>
            <button onclick="saveToken()">Save Token</button>
            <button onclick="exportSettings()">Export Settings</button>
            <button onclick="importSettings()">Import Settings</button>
            <input type="file" id="settingsFile" accept=".json" style="display:none" onchange="handleSettingsImport(event)">
            <div id="authStatus" class="status info">Please enter your JWT token</div>
        </div>

        <!-- Stream Join Section -->
        <div class="container">
            <h2>📺 Join Stream</h2>
            <div class="two-column">
                <div>
                    <div class="form-highLight">
                        <label for="streamId">Stream ID:</label>
                        <input type="text" id="streamId" placeholder="Enter the stream ID to join" />
                    </div>
                    <button onclick="joinStream()" class="join-btn" id="joinBtn">🔗 Join Stream</button>
                    <button onclick="leaveStream()" class="leave-btn" id="leaveBtn" disabled>🚪 Leave Stream</button>
                </div>
                <div>
                    <div id="connectionStatus" class="status info">Not connected to any stream</div>
                </div>
            </div>
        </div>

        <!-- Video Player Section -->
        <div class="container">
            <h2>📹 Live Stream</h2>
            <div class="video-container">
                <div class="video-player" id="videoPlayer">
                    <div class="video-overlay" id="videoOverlay">
                        <div>📺 Join a stream to start watching</div>
                    </div>
                </div>
                <div class="stream-info" id="streamInfo" style="display: none;">
                    <span class="live-indicator"></span>
                    <span id="streamTitle">Live Stream</span>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="main-content">
            <div>
                <!-- Stream Statistics -->
                <div class="container">
                    <h2>📊 Stream Statistics</h2>
                                            <div class="stats">
                        <div class="stat-card">
                            <div class="stat-number" id="viewerCount">0</div>
                            <div class="stat-label">👥 Viewers</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="reactionCount">0</div>
                            <div class="stat-label">❤️ Reactions</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="commentCount">0</div>
                            <div class="stat-label">💬 Comments</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="connectionIndicator">❌</div>
                            <div class="stat-label">🔌 Connection</div>
                        </div>
                        <div class="stat-card" style="min-height: 80px;">
                            <div class="stat-number" id="myReactionsCount">0</div>
                            <div class="stat-label">🚀 My Reactions</div>
                            <div id="myReactionsList" style="font-size: 10px; color: #6c757d; margin-top: 5px;">None</div>
                        </div>
                    </div>
                </div>

                <!-- Interactions Section -->
                <div class="container">
                    <h2>🎭 Interactions</h2>
                    <div>
                        <h3>Quick Reactions:</h3>
                        <div class="reaction-buttons">
                            <button id="likeButton" class="reaction-btn" onclick="toggleReaction('like')" title="Click to Like/Unlike - Toggle your reaction">
                                <span class="emoji">👍</span>
                                <span class="label">Like</span>
                                <span class="status-indicator" id="likeStatus">◯</span>
                            </button>
                            <!-- Future reaction types (backend only supports 'like' currently)
                            <button id="loveButton" class="reaction-btn disabled" title="Coming soon - Love reactions">
                                <span class="emoji">❤️</span>
                                <span class="label">Love</span>
                                <span class="status-indicator">◯</span>
                            </button>
                            <button id="wowButton" class="reaction-btn disabled" title="Coming soon - Wow reactions">
                                <span class="emoji">😮</span>
                                <span class="label">Wow</span>
                                <span class="status-indicator">◯</span>
                            </button>
                            <button id="laughButton" class="reaction-btn disabled" title="Coming soon - Laugh reactions">
                                <span class="emoji">😂</span>
                                <span class="label">Laugh</span>
                                <span class="status-indicator">◯</span>
                            </button>
                            -->
                        </div>
                        <div class="reaction-status" id="reactionStatus">Click any reaction above to react, click again to remove</div>
                        
                        <div style="margin: 15px 0; padding: 10px; background: #f8f9fa; border-radius: 8px; border: 1px solid #dee2e6;">
                            <h4 style="margin: 0 0 10px 0; color: #495057; font-size: 14px;">🧑‍🗋 Quick Test Actions:</h4>
                            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                                <button onclick="clearAllReactions()" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 4px; font-size: 12px; cursor: pointer;">Clear All Reactions</button>
                                <button onclick="showReactionDebug()" style="background: #6c757d; color: white; border: none; padding: 5px 10px; border-radius: 4px; font-size: 12px; cursor: pointer;">Debug Reactions</button>
                                <button onclick="simulateReactionBug()" style="background: #fd7e14; color: white; border: none; padding: 5px 10px; border-radius: 4px; font-size: 12px; cursor: pointer;">Test Bug Scenario</button>
                            </div>
                        </div>
                        
                        <h3>Send Comment:</h3>
                        <div class="comment-section">
                            <input type="text" id="commentInput" class="comment-input" placeholder="Type your comment..." />
                            <button onclick="sendComment()" class="send-btn">📤 Send</button>
                        </div>
                    </div>
                </div>
            </div>

            <div>
                <!-- Reactions Feed -->
                <div class="container">
                    <h2>❤️ Live Reactions</h2>
                    <div id="reactionsFeed"></div>
                    <button onclick="clearReactions()">🗑️ Clear Reactions</button>
                </div>

                <!-- Comments Section -->
                <div class="container">
                    <h2>💬 Live Comments</h2>
                    <div id="commentsArea" class="comments-area"></div>
                    <button onclick="clearComments()">🗑️ Clear Comments</button>
                </div>
            </div>
        </div>

        <!-- Live Activity Feed -->
        <div class="container">
            <h2>🔴 Live Activity Feed</h2>
            <div id="activityArea" class="activity-area"></div>
            <button onclick="clearActivity()">🗑️ Clear Feed</button>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/sockjs-client/1.5.2/sockjs.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/stomp.js/2.3.3/stomp.min.js"></script>
    <script>
        let stompClient = null;
        let currentStreamId = null;
        let jwtToken = null;
        let stats = { viewers: 0, reactions: 0, comments: 0 };
        let currentStream = null;
        let videoElement = null;
        let userReactions = new Set(); // Track user's reactions
        let commentElements = new Map(); // Track comment elements by ID
        const baseUrl = 'https://api.havebreak.cc';
        
        // Enhanced logging function
        function logSocketData(topic, action, data, direction = 'RECEIVED') {
            const timestamp = new Date().toISOString();
            const logStyle = direction === 'SENT' ? 'color: #28a745; font-weight: bold;' : 'color: #007bff; font-weight: bold;';
            
            console.highLight(`%c🔌 WebSocket ${direction} - ${timestamp}`, logStyle);
            console.log('%c📡 Topic:', 'color: #6c757d; font-weight: bold;', topic);
            console.log('%c⚡ Action:', 'color: #ffc107; font-weight: bold;', action || 'N/A');
            console.log('%c📦 Raw Data:', 'color: #17a2b8;', data);
            
            if (typeof data === 'string') {
                try {
                    const parsed = JSON.parse(data);
                    console.log('%c🔍 Parsed Data:', 'color: #20c997;', parsed);
                } catch (e) {
                    console.log('%c⚠️ Could not parse JSON:', 'color: #dc3545;', e.message);
                }
            } else {
                console.log('%c🔍 Structured Data:', 'color: #20c997;', data);
            }
            
            console.groupEnd();
        }

        function saveToken() {
            const tokenInput = document.getElementById('jwtToken');
            jwtToken = tokenInput.value.trim();
            
            if (!jwtToken) {
                setAuthStatus('Please enter a JWT token', 'error');
                return;
            }
            
            // Basic JWT format validation
            const jwtParts = jwtToken.split('.');
            if (jwtParts.length !== 3) {
                setAuthStatus('Invalid JWT token format', 'error');
                return;
            }
            
            localStorage.setItem('jwtToken', jwtToken);
            setAuthStatus('JWT token saved successfully ✅', 'success');
            addActivity('🔐 JWT token updated', 'info');
        }

        function loadToken() {
            const savedToken = localStorage.getItem('jwtToken');
            if (savedToken) {
                jwtToken = savedToken;
                document.getElementById('jwtToken').value = savedToken;
                setAuthStatus('JWT token loaded from storage', 'success');
            }
        }

        function setAuthStatus(message, type = 'info') {
            const statusDiv = document.getElementById('authStatus');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function setConnectionStatus(message, type = 'info') {
            const statusDiv = document.getElementById('connectionStatus');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function addActivity(message, type = 'info') {
            const activityArea = document.getElementById('activityArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = `activity-message activity-${type}`;
            messageDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong> - ${message}`;
            activityArea.appendChild(messageDiv);
            activityArea.scrollTop = activityArea.scrollHeight;
        }

        function updateStats() {
            document.getElementById('viewerCount').textContent = stats.viewers;
            document.getElementById('reactionCount').textContent = stats.reactions;
            document.getElementById('commentCount').textContent = stats.comments;
            document.getElementById('connectionIndicator').textContent = stompClient && stompClient.connected ? '✅' : '❌';
        }

        async function fetchStreamDetails(streamId) {
            try {
                addActivity('🔍 Fetching stream details...', 'info');
                const response = await fetch(`${baseUrl}/v1/live-stream/list?page=0&limit=50`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${jwtToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    if (response.status === 401) {
                        throw new Error('Authentication failed. Please check your JWT token.');
                    } else if (response.status === 403) {
                        throw new Error('Access forbidden. Check your permissions.');
                    } else {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                }

                const data = await response.json();
                addActivity(`📋 Found ${data.items?.length || 0} streams`, 'info');
                const stream = data.items?.find(item => item.id === streamId);
                
                if (!stream) {
                    throw new Error(`Stream with ID '${streamId}' not found. Please check the Stream ID.`);
                }

                addActivity(`✅ Stream found: ${stream.title}`, 'success');
                return stream;
            } catch (error) {
                console.error('Error fetching stream details:', error);
                addActivity(`❌ Failed to fetch stream: ${error.message}`, 'error');
                throw error;
            }
        }

        function setupVideoPlayer(stream) {
            const videoPlayer = document.getElementById('videoPlayer');
            const videoOverlay = document.getElementById('videoOverlay');
            const streamInfo = document.getElementById('streamInfo');
            const streamTitle = document.getElementById('streamTitle');

            // Clear existing video
            videoPlayer.innerHTML = '';

            if (stream.playbackUrl) {
                // Create video element
                videoElement = document.createElement('video');
                videoElement.controls = true;
                videoElement.autoplay = true;
                videoElement.muted = true; // Start muted to allow autoplay
                videoElement.style.width = '100%';
                videoElement.style.height = '100%';
                videoElement.style.objectFit = 'cover';

                // Set video source
                videoElement.src = stream.playbackUrl;

                // Add event listeners
                videoElement.addEventListener('loadstart', () => {
                    addActivity('📹 Loading video stream...', 'info');
                });

                videoElement.addEventListener('canplay', () => {
                    addActivity('📹 Video stream ready to play', 'info');
                    videoOverlay.style.display = 'none';
                });

                videoElement.addEventListener('play', () => {
                    addActivity('▶️ Video stream started playing', 'info');
                });

                videoElement.addEventListener('error', (e) => {
                    console.error('Video error:', e);
                    const error = e.target.error;
                    let errorMessage = 'Unknown video error';
                    if (error) {
                        switch (error.code) {
                            case MediaError.MEDIA_ERR_ABORTED:
                                errorMessage = 'Video loading aborted';
                                break;
                            case MediaError.MEDIA_ERR_NETWORK:
                                errorMessage = 'Network error while loading video';
                                break;
                            case MediaError.MEDIA_ERR_DECODE:
                                errorMessage = 'Video decode error';
                                break;
                            case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
                                errorMessage = 'Video format not supported';
                                break;
                            default:
                                errorMessage = 'Video playback error';
                        }
                    }
                    addActivity(`❌ Video stream error: ${errorMessage}`, 'error');
                    videoOverlay.style.display = 'flex';
                    videoOverlay.innerHTML = `<div>❌ ${errorMessage}<br><small>Try refreshing or check your connection</small></div>`;
                });

                videoElement.addEventListener('waiting', () => {
                    addActivity('⏳ Video stream buffering...', 'info');
                });

                videoElement.addEventListener('playing', () => {
                    addActivity('▶️ Video stream playing', 'info');
                });

                // Add video to player
                videoPlayer.appendChild(videoElement);

                // Show stream info
                streamTitle.textContent = stream.title || 'Live Stream';
                streamInfo.style.display = 'block';

                // Hide overlay initially
                videoOverlay.style.display = 'none';

                addActivity(`📹 Video player initialized for: ${stream.title}`, 'info');
            } else {
                // Show message if no playback URL
                videoOverlay.style.display = 'flex';
                videoOverlay.innerHTML = '<div>📺 No video stream available</div>';
                addActivity('⚠️ No playback URL available for this stream', 'warning');
            }
        }

        function stopVideoPlayer() {
            const videoPlayer = document.getElementById('videoPlayer');
            const videoOverlay = document.getElementById('videoOverlay');
            const streamInfo = document.getElementById('streamInfo');

            if (videoElement) {
                videoElement.pause();
                videoElement.src = '';
                videoElement = null;
            }

            videoPlayer.innerHTML = '';
            videoOverlay.style.display = 'flex';
            videoOverlay.innerHTML = '<div>📺 Join a stream to start watching</div>';
            videoPlayer.appendChild(videoOverlay);

            streamInfo.style.display = 'none';
            addActivity('📹 Video player stopped', 'info');
        }

        let userId = null; // Add global userId variable
        let username = null; // Add global username variable

        async function joinStream() {
            if (!jwtToken) {
                setAuthStatus('Please enter and save your JWT token first', 'error');
                return;
            }

            const streamId = document.getElementById('streamId').value.trim();
            if (!streamId) {
                setConnectionStatus('Please enter a stream ID', 'error');
                return;
            }

            try {
                currentStreamId = streamId;
                setConnectionStatus('Fetching stream details...', 'warning');

                // Fetch stream details
                currentStream = await fetchStreamDetails(streamId);
                addActivity(`📺 Found stream: ${currentStream.title}`, 'info');

                // Setup video player
                setupVideoPlayer(currentStream);

                setConnectionStatus('Connecting to stream...', 'warning');

                // Connect to WebSocket
                addActivity('🔄 Establishing WebSocket connection...', 'info');
                const socket = new SockJS(`${baseUrl}/ws/live-stream`);
                stompClient = Stomp.over(socket);
                
                // Disable STOMP debug by default (enable for debugging)
                stompClient.debug = null;

                // Add JWT token to headers
                const headers = {
                    'Authorization': `Bearer ${jwtToken}`
                };

                stompClient.connect(headers, function (frame) {
                    console.highLight('🚀 STOMP Connection Established');
                    console.log('📋 Frame Headers:', frame.headers);
                    console.log('📄 Frame Body:', frame.body);
                    
                    // Extract user info from STOMP headers
                    const userInfo = frame.headers['user-name'];
                    if (userInfo) {
                        [userId, username] = userInfo.split(':');
                        console.log(`👤 Connected as user: ${username} (${userId})`);
                    }
                    console.groupEnd();

                    setConnectionStatus(`✅ Connected to stream: ${currentStream.title}`, 'success');
                    addActivity(`🔗 Connected to stream ${streamId}`, 'info');
                    updateStats();

                    // Subscribe to stream updates
                    console.log(`🔍 Subscribing to viewers topic: /topic/live-stream/${streamId}/viewers`);
                    stompClient.subscribe(`/topic/live-stream/${streamId}/viewers`, function (message) {
                        logSocketData(`/topic/live-stream/${streamId}/viewers`, 'VIEWER_UPDATE', message.body);
                        try {
                            const data = JSON.parse(message.body);
                            const viewerCount = data.viewersCount || data.viewerCount || 0;
                            console.log('👥 Processing viewer update:', {
                                originalCount: stats.viewers,
                                newCount: viewerCount,
                                action: data.action,
                                fullData: data
                            });
                            addActivity(`👥 Viewer count: ${viewerCount} (${data.action || 'UPDATE'})`, 'viewer');
                            stats.viewers = viewerCount;
                            updateStats();
                        } catch (error) {
                            console.error('❌ Error processing viewer update:', error, 'Raw message:', message.body);
                            addActivity('❌ Error processing viewer update', 'error');
                        }
                    });

                    console.log(`🔍 Subscribing to reactions topic: /topic/live-stream/${streamId}/reactions`);
                    stompClient.subscribe(`/topic/live-stream/${streamId}/reactions`, function (message) {
                        logSocketData(`/topic/live-stream/${streamId}/reactions`, 'REACTION_UPDATE', message.body);
                        try {
                            const data = JSON.parse(message.body);
                            const totalReactions = data.totalReactions || data.numberOfReactions || 0;
                            
                            console.log('❤️ Processing reaction update:', {
                                action: data.action,
                                username: data.username,
                                reactionType: data.reactionType,
                                userId: data.userId,
                                oldCount: stats.reactions,
                                newCount: totalReactions,
                                fullData: data
                            });
                            
                            if (data.action === 'CURRENT') {
                                stats.reactions = totalReactions;
                                addActivity(`❤️ Current reactions: ${totalReactions}`, 'reaction');
                            } else {
                                const username = data.username || 'Someone';
                                const action = data.action === 'ADD' ? 'reacted' : 'removed reaction';
                                const reactionType = data.reactionType || 'unknown';
                                addActivity(`❤️ ${username} ${action}: ${reactionType}`, 'reaction');
                                stats.reactions = totalReactions;
                            }
                            document.getElementById('reactionCount').textContent = totalReactions;
                            updateStats();
                        } catch (error) {
                            console.error('❌ Error processing reaction update:', error, 'Raw message:', message.body);
                            addActivity('❌ Error processing reaction update', 'error');
                        }
                    });

                    console.log(`🔍 Subscribing to comments topic: /topic/live-stream/${streamId}/comments`);
                    stompClient.subscribe(`/topic/live-stream/${streamId}/comments`, function (message) {
                        logSocketData(`/topic/live-stream/${streamId}/comments`, 'COMMENT_COUNT_UPDATE', message.body);
                        try {
                            const data = JSON.parse(message.body);
                            const totalComments = data.totalComments || data.numberOfComments || 0;
                            
                            console.log('💬 Processing comment count update:', {
                                action: data.action,
                                username: data.username,
                                userId: data.userId,
                                oldCount: stats.comments,
                                newCount: totalComments,
                                fullData: data
                            });
                            
                            if (data.action === 'CURRENT') {
                                stats.comments = totalComments;
                                addActivity(`💬 Current comments: ${totalComments}`, 'comment');
                            } else {
                                const username = data.username || 'Someone';
                                addActivity(`💬 Comment ${data.action}: ${username} (Total: ${totalComments})`, 'comment');
                                stats.comments = totalComments;
                            }
                            updateStats();
                        } catch (error) {
                            console.error('❌ Error processing comment update:', error, 'Raw message:', message.body);
                            addActivity('❌ Error processing comment update', 'error');
                        }
                    });

                    console.log(`🔍 Subscribing to comments feed: /topic/live-stream/${streamId}/comments-feed`);
                    stompClient.subscribe(`/topic/live-stream/${streamId}/comments-feed`, function (message) {
                        logSocketData(`/topic/live-stream/${streamId}/comments-feed`, 'COMMENT_FEED', message.body);
                        try {
                            const data = JSON.parse(message.body);
                            
                            console.log('💬 Processing comment feed:', {
                                action: data.action,
                                commentId: data.id,
                                username: data.username,
                                userId: data.userId,
                                comment: data.comment,
                                firstName: data.firstName,
                                lastName: data.lastName,
                                profileImage: data.profileImage,
                                createdDate: data.createdDate,
                                fullData: data
                            });
                            
                            if (data.action === 'ADD') {
                                const username = data.username || 'Anonymous';
                                const comment = data.comment || '';
                                const commentId = data.id;
                                addActivity(`💬 ${username}: "${comment}"`, 'comment');
                                addComment(username, comment, false, commentId, data.userId);
                            } else if (data.action === 'REMOVE') {
                                const username = data.username || 'Someone';
                                const commentId = data.id;
                                addActivity(`💬 Comment removed by ${username}`, 'comment');
                                removeCommentFromUI(commentId);
                            }
                        } catch (error) {
                            console.error('❌ Error processing comment feed:', error, 'Raw message:', message.body);
                            addActivity('❌ Error processing comment feed', 'error');
                        }
                    });

                    console.log(`🔍 Subscribing to user reactions: /topic/live-stream/${streamId}/user-reactions`);
                    stompClient.subscribe(`/topic/live-stream/${streamId}/user-reactions`, function (message) {
                        logSocketData(`/topic/live-stream/${streamId}/user-reactions`, 'USER_REACTION_STATE', message.body);
                        try {
                            const data = JSON.parse(message.body);
                            
                            console.log('👤 Processing user reaction state:', {
                                action: data.action,
                                userId: data.userId,
                                username: data.username,
                                hasReacted: data.hasReacted,
                                reactionType: data.reactionType,
                                firstName: data.firstName,
                                lastName: data.lastName,
                                profileImage: data.profileImage,
                                isCurrentUser: data.userId === userId,
                                currentUserReactions: Array.from(userReactions),
                                fullData: data
                            });
                            
                            if (data.action === 'ADD') {
                                // Check if this is the current user's reaction
                                if (data.userId === userId) {
                                    console.log('❤️ Current user added reaction:', data.reactionType);
                                    userReactions.add(data.reactionType);
                                    updateReactionButton(data.reactionType, true);
                                    console.log('📋 Updated userReactions set:', Array.from(userReactions));
                                }
                                const userFullName = `${data.firstName || ''} ${data.lastName || data.username}`.trim();
                                addActivity(`❤️ ${userFullName} reacted: ${data.reactionType}`, 'reaction');
                                
                                // Add user profile image if available
                                const reactionDiv = document.createElement('div');
                                reactionDiv.className = 'user-reaction';
                                reactionDiv.dataset.userId = data.userId;
                                reactionDiv.innerHTML = `
                                    <img src="${data.profileImage || 'https://via.placeholder.com/32'}" alt="${userFullName}" class="user-avatar" onerror="this.src='https://via.placeholder.com/32'">
                                    <div class="user-info">
                                        <div class="user-name">${userFullName}</div>
                                        <div class="reaction-time">${new Date().toLocaleTimeString()}</div>
                                    </div>
                                    <span class="reaction-type">${data.reactionType}</span>
                                `;
                                const reactionsFeed = document.getElementById('reactionsFeed');
                                reactionsFeed.insertBefore(reactionDiv, reactionsFeed.firstChild);
                                
                            } else if (data.action === 'REMOVE') {
                                // Check if this is the current user's reaction removal
                                if (data.userId === userId) {
                                    console.log('❌ Current user removed reaction:', data.reactionType || 'unknown');
                                    if (data.reactionType) {
                                        userReactions.delete(data.reactionType);
                                        updateReactionButton(data.reactionType, false);
                                    }
                                    console.log('📋 Updated userReactions set:', Array.from(userReactions));
                                }
                                const userFullName = `${data.firstName || ''} ${data.lastName || data.username}`.trim();
                                addActivity(`❤️ ${userFullName} removed their reaction`, 'reaction');
                                
                                // Remove reaction from UI feed
                                const reactionsFeed = document.getElementById('reactionsFeed');
                                const userReactionDiv = reactionsFeed.querySelector(`[data-user-id="${data.userId}"]`);
                                if (userReactionDiv) {
                                    userReactionDiv.remove();
                                    console.log('📋 Removed reaction div for user:', data.userId);
                                }
                                
                            } else if (data.action === 'CURRENT') {
                                // Handle initial state when user joins
                                if (data.userId === userId) {
                                    console.log('🔄 Processing current user initial state:', {
                                        hasReacted: data.hasReacted,
                                        reactionType: data.reactionType,
                                        willAddToSet: data.hasReacted && data.reactionType
                                    });
                                    
                                    if (data.hasReacted && data.reactionType) {
                                        userReactions.add(data.reactionType);
                                        updateReactionButton(data.reactionType, true);
                                        addActivity(`ℹ️ You have previously reacted: ${data.reactionType}`, 'info');
                                    } else {
                                        // Clear all reaction states for safety
                                        const allReactionTypes = ['like']; // Currently only 'like' is supported
                                        allReactionTypes.forEach(type => {
                                            updateReactionButton(type, false);
                                        });
                                        addActivity(`ℹ️ No previous reactions found`, 'info');
                                    }
                                    console.log('📋 Final userReactions set:', Array.from(userReactions));
                                }
                            }
                        } catch (error) {
                            console.error('❌ Error processing user reaction:', error, 'Raw message:', message.body);
                            addActivity('❌ Error processing user reaction', 'error');
                        }
                    });

                    // Subscribe to stream end notifications
                    console.log(`🔍 Subscribing to stream ended: /topic/live-stream/${streamId}/stream-ended`);
                    stompClient.subscribe(`/topic/live-stream/${streamId}/stream-ended`, function (message) {
                        logSocketData(`/topic/live-stream/${streamId}/stream-ended`, 'STREAM_ENDED', message.body);
                        try {
                            const data = JSON.parse(message.body);
                            const reason = data.reason || 'Unknown reason';
                            const endMessage = data.message || 'Stream has ended';
                            
                            console.log('🔚 Processing stream end notification:', {
                                reason: reason,
                                message: endMessage,
                                influencerId: data.influencerId,
                                influencerName: data.influencerName,
                                endedAt: data.endedAt,
                                action: data.action,
                                fullData: data
                            });
                            
                            addActivity(`🔚 Stream ended: ${endMessage} (${reason})`, 'warning');
                            setConnectionStatus(`Stream has ended: ${reason}`, 'warning');
                            
                            // Send acknowledgment
                            if (stompClient && stompClient.connected) {
                                try {
                                    const ackData = {
                                        liveStreamId: streamId,
                                        reason: reason,
                                        message: endMessage,
                                        action: "STREAM_ENDED"
                                    };
                                    logSocketData('/app/live-stream/stream-ended-acknowledgment', 'STREAM_END_ACK', JSON.stringify(ackData), 'SENT');
                                    stompClient.send("/app/live-stream/stream-ended-acknowledgment", {}, JSON.stringify(ackData));
                                    addActivity('📤 Sent stream end acknowledgment', 'info');
                                } catch (ackError) {
                                    console.error('❌ Error sending acknowledgment:', ackError);
                                }
                            }
                            
                            // Auto-leave the stream after a delay
                            setTimeout(() => {
                                leaveStream();
                            }, 3000);
                            
                        } catch (error) {
                            console.error('❌ Error processing stream end notification:', error, 'Raw message:', message.body);
                            addActivity('❌ Error processing stream end notification', 'error');
                            // Still try to leave the stream
                            setTimeout(() => {
                                leaveStream();
                            }, 2000);
                        }
                    });

                  
                    // Subscribe to session force disconnect
                    // Get session ID from STOMP connection
                    const sessionId = stompClient.ws._transport.url.split('/').pop();
                    console.log(`🔍 Subscribing to session force disconnect: /topic/live-stream/session/${sessionId}/force-disconnect`);
                    stompClient.subscribe(`/topic/live-stream/session/${sessionId}/force-disconnect`, function (message) {
                        logSocketData(`/topic/live-stream/session/${sessionId}/force-disconnect`, 'FORCE_DISCONNECT', message.body);
                        console.log('🔌 Processing session force disconnect:', {
                            sessionId: sessionId,
                            messageBody: message.body,
                            currentStreamId: currentStreamId
                        });
                        addActivity(`🔌 Session force disconnect received`, 'warning');
                        leaveStream();
                    });

                 

                    // Join the stream
                    const joinData = {
                        liveStreamId: streamId
                    };
                    logSocketData('/app/live-stream/join', 'JOIN_STREAM', JSON.stringify(joinData), 'SENT');
                    console.log('📤 Sending join message:', joinData);
                    stompClient.send("/app/live-stream/join", {}, JSON.stringify(joinData));

                    // Initial user reaction state is handled by the join response

                    // Update button states
                    document.getElementById('joinBtn').disabled = true;
                    document.getElementById('leaveBtn').disabled = false;

                }, function (error) {
                    console.error('STOMP connection error:', error);
                    setConnectionStatus('❌ Connection failed: ' + error, 'error');
                    addActivity('❌ Connection failed: ' + error, 'error');
                    stopVideoPlayer();
                    updateStats();
                    
                    // Reset connection state
                    stompClient = null;
                    
                    // Clear user state
                    userReactions.clear();
                    updateReactionButton('like', false);
                });

            } catch (error) {
                setConnectionStatus('❌ Failed to fetch stream details: ' + error.message, 'error');
                addActivity('❌ Failed to fetch stream details: ' + error.message, 'error');
                currentStreamId = null;
                currentStream = null;
            }
        }

        function leaveStream() {
            try {
                if (stompClient && stompClient.connected && currentStreamId) {
                                const leaveData = {
                liveStreamId: currentStreamId
            };
            logSocketData('/app/live-stream/leave', 'LEAVE_STREAM', JSON.stringify(leaveData), 'SENT');
            console.log('📤 Sending leave message:', leaveData);
            stompClient.send("/app/live-stream/leave", {}, JSON.stringify(leaveData));
            addActivity('📤 Sent leave message', 'info');
                }
            } catch (error) {
                console.error('Error sending leave message:', error);
                addActivity('⚠️ Error sending leave message', 'warning');
            }

            // Disconnect WebSocket
            if (stompClient) {
                try {
                    stompClient.disconnect();
                    addActivity('🔌 WebSocket disconnected', 'info');
                } catch (error) {
                    console.error('Error disconnecting WebSocket:', error);
                }
                stompClient = null;
            }

            // Stop video player
            stopVideoPlayer();

            // Reset state
            currentStreamId = null;
            currentStream = null;
            stats = { viewers: 0, reactions: 0, comments: 0 };
            
            // Reset all reactions
            const allReactionTypes = ['like']; // Currently only 'like' is supported
            userReactions.clear();
            allReactionTypes.forEach(type => {
                updateReactionButton(type, false);
            });
            
            commentElements.clear();

            // Clear UI
            document.getElementById('commentsArea').innerHTML = '';
            document.getElementById('reactionsFeed').innerHTML = '';
            
            // Reset reaction status
            const statusEl = document.getElementById('reactionStatus');
            if (statusEl) {
                statusEl.textContent = 'Join a stream first to react';
                statusEl.style.color = '#6c757d';
            }

            // Update UI
            setConnectionStatus('Disconnected from stream', 'info');
            addActivity('🚪 Left the stream', 'info');
            updateStats();

            // Update button states
            document.getElementById('joinBtn').disabled = false;
            document.getElementById('leaveBtn').disabled = true;
        }

        function updateReactionButton(reactionType, isActive) {
            // Currently only 'like' is supported by backend
            const buttonMap = {
                'like': 'likeButton'
            };
            
            const buttonId = buttonMap[reactionType];
            if (!buttonId) {
                console.warn('Unknown reaction type:', reactionType, 'Supported types:', Object.keys(buttonMap));
                return;
            }
            
            const button = document.getElementById(buttonId);
            if (!button) {
                console.warn('Button not found:', buttonId);
                return;
            }
            
            const statusIndicator = button.querySelector('.status-indicator');
            
            if (isActive) {
                button.classList.add('active');
                if (statusIndicator) {
                    statusIndicator.textContent = '✓'; // Checkmark
                    statusIndicator.style.color = '#4caf50';
                }
                console.log(`✅ Button updated: ${reactionType} is now ACTIVE`);
            } else {
                button.classList.remove('active');
                if (statusIndicator) {
                    statusIndicator.textContent = '◯'; // Empty circle
                    statusIndicator.style.color = '#ccc';
                }
                console.log(`➖ Button updated: ${reactionType} is now INACTIVE`);
            }
            
            // Update status message
            const statusEl = document.getElementById('reactionStatus');
            const myReactionsCount = document.getElementById('myReactionsCount');
            const myReactionsList = document.getElementById('myReactionsList');
            
            const activeReactions = Array.from(userReactions);
            
            if (statusEl) {
                if (activeReactions.length > 0) {
                    statusEl.innerHTML = `✅ <strong>You reacted:</strong> ${activeReactions.join(', ')} <em>(click button again to remove)</em>`;
                    statusEl.style.background = '#d1edff';
                    statusEl.style.borderColor = '#4caf50';
                    statusEl.style.color = '#2e7d32';
                } else {
                    statusEl.innerHTML = `▶️ <strong>Click the like button above to react</strong> <em>(you can remove it afterwards)</em>`;
                    statusEl.style.background = '#f8f9fa';
                    statusEl.style.borderColor = '#e9ecef';
                    statusEl.style.color = '#6c757d';
                }
            }
            
            // Update my reactions display
            if (myReactionsCount) {
                myReactionsCount.textContent = activeReactions.length;
            }
            if (myReactionsList) {
                if (activeReactions.length > 0) {
                    myReactionsList.textContent = activeReactions.join(', ');
                    myReactionsList.style.color = '#28a745';
                } else {
                    myReactionsList.textContent = 'None';
                    myReactionsList.style.color = '#6c757d';
                }
            }
        }

        function toggleReaction(reactionType) {
            if (!stompClient || !currentStreamId) {
                setConnectionStatus('Please join a stream first', 'error');
                addActivity('⚠️ Cannot react: not connected to stream', 'warning');
                return;
            }

            const isRemoving = userReactions.has(reactionType);
            console.log(`💆 User clicked ${reactionType} reaction:`, {
                currentState: isRemoving ? 'active' : 'inactive',
                willPerform: isRemoving ? 'remove' : 'add',
                currentUserReactions: Array.from(userReactions)
            });
            
            if (isRemoving) {
                const unreactData = {
                    liveStreamId: currentStreamId,
                    reactionType: reactionType
                };
                logSocketData('/app/live-stream/unreact', 'REMOVE_REACTION', JSON.stringify(unreactData), 'SENT');
                console.log('📤 Sending unreact message:', unreactData);
                stompClient.send("/app/live-stream/unreact", {}, JSON.stringify(unreactData));
            } else {
                const reactData = {
                    liveStreamId: currentStreamId,
                    reactionType: reactionType
                };
                logSocketData('/app/live-stream/react', 'ADD_REACTION', JSON.stringify(reactData), 'SENT');
                console.log('📤 Sending react message:', reactData);
                stompClient.send("/app/live-stream/react", {}, JSON.stringify(reactData));
            }

            // Immediately update the UI for responsive feedback
            if (isRemoving) {
                userReactions.delete(reactionType);
                updateReactionButton(reactionType, false);
                console.log(`📋 Immediately removed ${reactionType} from local state`);
            } else {
                userReactions.add(reactionType);
                updateReactionButton(reactionType, true);
                console.log(`📋 Immediately added ${reactionType} to local state`);
            }
            
            console.log('📋 Current userReactions set:', Array.from(userReactions));
        }

        function sendComment() {
            const commentInput = document.getElementById('commentInput');
            const comment = commentInput.value.trim();

            if (!comment) {
                return;
            }

            if (!stompClient || !currentStreamId) {
                setConnectionStatus('Please join a stream first', 'error');
                return;
            }

            const commentData = {
                liveStreamId: currentStreamId,
                comment: comment
            };
            logSocketData('/app/live-stream/comment', 'ADD_COMMENT', JSON.stringify(commentData), 'SENT');
            console.log('📤 Sending comment message:', commentData);
            stompClient.send("/app/live-stream/comment", {}, JSON.stringify(commentData));

            addActivity(`💬 You commented: "${comment}"`, 'comment');
            // Note: We don't add the comment to UI here, it will come via the comments-feed topic
            commentInput.value = '';
        }

        function clearActivity() {
            document.getElementById('activityArea').innerHTML = '';
        }

        function clearComments() {
            document.getElementById('commentsArea').innerHTML = '';
        }

        function clearReactions() {
            document.getElementById('reactionsFeed').innerHTML = '';
        }

        function addComment(username, comment, isMyComment = false, commentId = null, userId = null) {
            const commentsArea = document.getElementById('commentsArea');
            const commentDiv = document.createElement('div');
            commentDiv.className = `comment-message ${isMyComment ? 'my-comment' : ''}`;
            if (commentId) {
                commentDiv.dataset.commentId = commentId;
                commentElements.set(commentId, commentDiv);
            }
            
            const time = new Date().toLocaleTimeString();
            const deleteButton = (isMyComment && commentId) ? 
                `<button class="delete-comment-btn" onclick="deleteComment('${commentId}')" title="Delete comment">❌</button>` : '';
            
            commentDiv.innerHTML = `
                <div class="comment-time">${time}</div>
                <div class="comment-username">${username}</div>
                <div class="comment-text">${comment}</div>
                ${deleteButton}
            `;
            
            commentsArea.appendChild(commentDiv);
            commentsArea.scrollTop = commentsArea.scrollHeight;
        }
        
        function removeCommentFromUI(commentId) {
            if (commentElements.has(commentId)) {
                const commentDiv = commentElements.get(commentId);
                commentDiv.remove();
                commentElements.delete(commentId);
            }
        }
        
        function deleteComment(commentId) {
            if (!stompClient || !currentStreamId) {
                addActivity('❌ Cannot delete comment: not connected', 'error');
                return;
            }
            
            if (confirm('Are you sure you want to delete this comment?')) {
                const deleteCommentData = {
                    liveStreamId: currentStreamId,
                    comment: commentId // The controller expects comment ID in the comment field
                };
                logSocketData('/app/live-stream/remove-comment', 'DELETE_COMMENT', JSON.stringify(deleteCommentData), 'SENT');
                console.log('📤 Sending delete comment message:', deleteCommentData);
                stompClient.send("/app/live-stream/remove-comment", {}, JSON.stringify(deleteCommentData));
                addActivity('💬 Deleting your comment...', 'comment');
            }
        }

        // Handle Enter key for comment input
        document.getElementById('commentInput').addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                sendComment();
            }
        });

        // Debug and Test Functions
        function clearAllReactions() {
            console.log('🧙‍♂️ Manual clear all reactions triggered');
            const allReactionTypes = ['like']; // Currently only 'like' is supported
            
            // Clear local state
            userReactions.clear();
            
            // Update all buttons to inactive
            allReactionTypes.forEach(type => {
                updateReactionButton(type, false);
            });
            
            addActivity('🧑‍🗋 Manually cleared all reactions (local only)', 'info');
        }
        
        function showReactionDebug() {
            const debugInfo = {
                userReactionsSet: Array.from(userReactions),
                setSize: userReactions.size,
                currentStreamId: currentStreamId,
                userId: userId,
                username: username,
                connected: stompClient && stompClient.connected
            };
            
            console.highLight('🔍 Reaction Debug Information');
            console.table(debugInfo);
            console.log('Full userReactions set:', userReactions);
            console.groupEnd();
            
            addActivity(`🔍 Debug: You have ${debugInfo.setSize} active reactions: [${debugInfo.userReactionsSet.join(', ')}]`, 'info');
        }
        
        function simulateReactionBug() {
            console.log('🐛 Simulating the hasReacted bug scenario');
            
            // Add a reaction to local state without sending to backend
            userReactions.add('like');
            updateReactionButton('like', true);
            
            addActivity('🐛 Simulated bug: Added reaction locally without backend sync', 'warning');
            addActivity('🔧 Try clicking the like button - it should remove the reaction', 'info');
        }
        
        // Export/Import Settings
        function exportSettings() {
            const settings = {
                jwtToken: jwtToken,
                baseUrl: baseUrl,
                timestamp: new Date().toISOString()
            };
            
            const blob = new Blob([JSON.stringify(settings, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `traveler-settings-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            addActivity('💾 Settings exported successfully', 'info');
        }
        
        function importSettings() {
            document.getElementById('settingsFile').click();
        }
        
        function handleSettingsImport(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const settings = JSON.parse(e.target.result);
                    
                    if (settings.jwtToken) {
                        jwtToken = settings.jwtToken;
                        document.getElementById('jwtToken').value = settings.jwtToken;
                        localStorage.setItem('jwtToken', settings.jwtToken);
                    }
                    
                    setAuthStatus('💼 Settings imported successfully', 'success');
                    addActivity(`💼 Settings imported from ${file.name}`, 'info');
                } catch (error) {
                    setAuthStatus('Failed to import settings: ' + error.message, 'error');
                    addActivity('❌ Failed to import settings', 'error');
                }
            };
            reader.readAsText(file);
        }

        // Initialize
        loadToken();
        updateStats();

        // Handle disconnect
        window.addEventListener('beforeunload', function() {
            if (stompClient && stompClient.connected) {
                stompClient.disconnect();
            }
        });
    </script>
</body>
</html>