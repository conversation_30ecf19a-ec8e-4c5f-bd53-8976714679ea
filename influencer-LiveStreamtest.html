<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Influencer Live Stream Test Client</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        h2 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .form-highLight {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2c3e50;
        }
        input, select, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #3498db;
        }
        button {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            margin: 5px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }
        button:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            transform: none;
        }
        .start-btn {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
        }
        .stop-btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 600;
        }
        .success {
            background: #d5f4e6;
            color: #27ae60;
            border: 1px solid #27ae60;
        }
        .error {
            background: #fdeaea;
            color: #e74c3c;
            border: 1px solid #e74c3c;
        }
        .info {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #1976d2;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffc107;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 10px;
        }
        .stat-label {
            color: #6c757d;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .activity-area {
            height: 300px;
            overflow-y: auto;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .comments-area {
            height: 350px;
            overflow-y: auto;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            background: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 14px;
        }
        .comment-message {
            margin: 10px 0;
            padding: 12px;
            border-radius: 12px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            position: relative;
        }
        .comment-username {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
            font-size: 12px;
        }
        .comment-text {
            color: #333;
            line-height: 1.4;
        }
        .comment-time {
            font-size: 11px;
            color: #6c757d;
            position: absolute;
            top: 8px;
            right: 12px;
        }
        .delete-comment-btn {
            position: absolute;
            top: 8px;
            right: 35px;
            background: #e74c3c;
            color: white;
            border: none;
            border-radius: 3px;
            padding: 2px 6px;
            font-size: 10px;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.2s;
        }
        .comment-message:hover .delete-comment-btn {
            opacity: 1;
        }
        .delete-comment-btn:hover {
            background: #c0392b;
        }
        .activity-message {
            margin: 8px 0;
            padding: 8px;
            border-radius: 6px;
            border-left: 4px solid #3498db;
        }
        .activity-reaction {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .activity-comment {
            background: #e7f3ff;
            border-left-color: #0066cc;
        }
        .activity-viewer {
            background: #f0f0f0;
            border-left-color: #6c757d;
        }
        .activity-stream {
            background: #e2e3e5;
            border-left-color: #495057;
        }
        .activity-error {
            background: #fdeaea;
            border-left-color: #e74c3c;
        }
        .user-reaction {
            display: flex;
            align-items: center;
            padding: 12px;
            margin: 8px 0;
            background: #f8f9fa;
            border-radius: 12px;
            border: 1px solid #dee2e6;
            transition: all 0.3s ease;
            animation: fadeIn 0.5s ease-in-out;
        }
        .user-reaction:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
            object-fit: cover;
            border: 2px solid #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .user-info {
            flex: 1;
        }
        .user-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 2px;
        }
        .reaction-time {
            font-size: 11px;
            color: #95a5a6;
        }
        .reaction-type {
            margin-left: 12px;
            padding: 6px 12px;
            background: #e3f2fd;
            border-radius: 20px;
            color: #1976d2;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        #reactionsFeed {
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
            padding: 10px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .stream-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .info-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .info-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }
        .info-value {
            color: #007bff;
            font-family: 'Courier New', monospace;
            word-break: break-all;
        }
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .debug-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .debug-toggle {
            background: #6c757d;
            font-size: 14px;
            padding: 8px 15px;
        }
        .debug-log {
            height: 200px;
            overflow-y: auto;
            background: #000;
            color: #00ff00;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin-top: 10px;
        }
        @media (max-width: 768px) {
            .two-column {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 Influencer Live Stream Dashboard</h1>
        
        <!-- Authentication Section -->
        <div class="container">
            <h2>🔐 Authentication</h2>
            <div class="form-highLight">
                <label for="jwtToken">JWT Token:</label>
                <textarea id="jwtToken" placeholder="Paste your JWT token here..." rows="3"></textarea>
            </div>
            <button onclick="saveToken()">Save Token</button>
            <button onclick="testToken()">Test Token</button>
            <button onclick="exportSettings()">Export Settings</button>
            <button onclick="importSettings()">Import Settings</button>
            <input type="file" id="settingsFile" accept=".json" style="display:none" onchange="handleSettingsImport(event)">
            <div id="authStatus" class="status info">Please enter your JWT token</div>
        </div>

        <!-- Stream Control Section -->
        <div class="container">
            <h2>📺 Stream Control</h2>
            <div class="two-column">
                <div>
                    <div class="form-highLight">
                        <label for="streamTitle">Stream Title:</label>
                        <input type="text" id="streamTitle" placeholder="Enter your stream title" />
                    </div>
                    <div class="form-highLight">
                        <label for="packageId">Package ID (Optional):</label>
                        <input type="text" id="packageId" placeholder="Enter package ID (optional)" />
                    </div>
                    <button onclick="startStream()" class="start-btn" id="startBtn">🔴 Start Stream</button>
                    <button onclick="stopStream()" class="stop-btn" id="stopBtn" disabled>⏹️ Stop Stream</button>
                </div>
                <div>
                    <div id="streamStatus" class="status info">No active stream</div>
                </div>
            </div>
        </div>

        <!-- Stream Statistics -->
        <div class="container">
            <h2>📊 Live Statistics</h2>
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="viewerCount">0</div>
                    <div class="stat-label">👥 Viewers</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="reactionCount">0</div>
                    <div class="stat-label">❤️ Reactions</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="commentCount">0</div>
                    <div class="stat-label">💬 Comments</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="connectionStatus">❌</div>
                    <div class="stat-label">🔌 Connection</div>
                </div>
            </div>
        </div>

        <!-- Stream Information -->
        <div class="container">
            <h2>ℹ️ Stream Information</h2>
            <div class="stream-info">
                <div class="info-item">
                    <div class="info-label">Stream ID:</div>
                    <div class="info-value" id="currentStreamId">-</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Title:</div>
                    <div class="info-value" id="currentStreamTitle">-</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Package ID:</div>
                    <div class="info-value" id="currentPackageId">-</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Started:</div>
                    <div class="info-value" id="streamStartTime">-</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Playback URL:</div>
                    <div class="info-value" id="playbackUrl">-</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Stream Key:</div>
                    <div class="info-value" id="streamKey">-</div>
                </div>
            </div>
        </div>

        <!-- Debug Section -->
        <div class="container">
            <h2>🔧 Debug Information</h2>
            <button onclick="toggleDebug()" class="debug-toggle">Toggle Debug Logs</button>
            <div id="debugSection" class="debug-section" style="display: none;">
                <div id="debugLog" class="debug-log"></div>
                <button onclick="clearDebugLog()">Clear Debug Log</button>
            </div>
        </div>

        <!-- Reactions Feed -->
        <div class="container">
            <h2>❤️ Live Reactions</h2>
            <div id="reactionsFeed"></div>
            <button onclick="clearReactions()">🗑️ Clear Reactions</button>
        </div>

        <!-- Comments Section -->
        <div class="container">
            <h2>💬 Live Comments</h2>
            <div id="commentsArea" class="comments-area"></div>
            <button onclick="clearComments()">🗑️ Clear Comments</button>
        </div>

        <!-- Live Activity Monitor -->
        <div class="container">
            <h2>🔴 Live Activity Monitor</h2>
            <div id="activityArea" class="activity-area"></div>
            <button onclick="clearActivity()">🗑️ Clear Activity</button>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/sockjs-client/1.5.2/sockjs.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/stomp.js/2.3.3/stomp.min.js"></script>
    <script>
        let stompClient = null;
        let currentStreamId = null;
        let currentPackageId = null;
        let streamStartTime = null;
        let stats = { viewers: 0, reactions: 0, comments: 0 };
        let jwtToken = null;
        let debugEnabled = false;
        let commentElements = new Map(); // Track comment elements by ID
        const baseUrl = 'https://api.havebreak.cc';
        
        // Enhanced logging function for WebSocket data
        function logSocketData(topic, action, data, direction = 'RECEIVED') {
            const timestamp = new Date().toISOString();
            const logStyle = direction === 'SENT' ? 'color: #28a745; font-weight: bold;' : 'color: #007bff; font-weight: bold;';
            
            console.highLight(`%c🔌 WebSocket ${direction} - ${timestamp}`, logStyle);
            console.log('%c📡 Topic:', 'color: #6c757d; font-weight: bold;', topic);
            console.log('%c⚡ Action:', 'color: #ffc107; font-weight: bold;', action || 'N/A');
            console.log('%c📦 Raw Data:', 'color: #17a2b8;', data);
            
            if (typeof data === 'string') {
                try {
                    const parsed = JSON.parse(data);
                    console.log('%c🔍 Parsed Data:', 'color: #20c997;', parsed);
                } catch (e) {
                    console.log('%c⚠️ Could not parse JSON:', 'color: #dc3545;', e.message);
                }
            } else {
                console.log('%c🔍 Structured Data:', 'color: #20c997;', data);
            }
            
            console.groupEnd();
            
            // Also log to debug if enabled
            if (debugEnabled) {
                debugLog(`WebSocket ${direction}: ${topic} -> ${JSON.stringify(data)}`);
            }
        }

        function debugLog(message) {
            if (debugEnabled) {
                const debugLogDiv = document.getElementById('debugLog');
                const timestamp = new Date().toLocaleTimeString();
                debugLogDiv.innerHTML += `[${timestamp}] ${message}\n`;
                debugLogDiv.scrollTop = debugLogDiv.scrollHeight;
            }
            console.log('Debug:', message);
        }

        function toggleDebug() {
            debugEnabled = !debugEnabled;
            const debugSection = document.getElementById('debugSection');
            debugSection.style.display = debugEnabled ? 'block' : 'none';
            debugLog('Debug logging ' + (debugEnabled ? 'enabled' : 'disabled'));
        }

        function clearDebugLog() {
            document.getElementById('debugLog').innerHTML = '';
        }

        function saveToken() {
            const tokenInput = document.getElementById('jwtToken');
            jwtToken = tokenInput.value.trim();
            
            if (!jwtToken) {
                setAuthStatus('Please enter a JWT token', 'error');
                return;
            }
            
            // Basic JWT format validation
            const jwtParts = jwtToken.split('.');
            if (jwtParts.length !== 3) {
                setAuthStatus('Invalid JWT token format', 'error');
                debugLog('Invalid JWT token format - expected 3 parts');
                return;
            }
            
            localStorage.setItem('jwtToken', jwtToken);
            setAuthStatus('JWT token saved successfully ✅', 'success');
            debugLog('JWT token saved to localStorage');
        }

        function testToken() {
            if (!jwtToken) {
                setAuthStatus('Please enter and save a JWT token first', 'error');
                return;
            }

            debugLog('Testing JWT token validity...');
            
            fetch(`${baseUrl}/v1/live-stream/list`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${jwtToken}`
                }
            })
            .then(response => {
                debugLog(`Token test response: ${response.status}`);
                if (response.ok) {
                    setAuthStatus('JWT token is valid ✅', 'success');
                } else {
                    setAuthStatus(`JWT token invalid: ${response.status}`, 'error');
                }
            })
            .catch(error => {
                debugLog(`Token test error: ${error.message}`);
                setAuthStatus(`Token test failed: ${error.message}`, 'error');
            });
        }

        function loadToken() 
        {
            const savedToken = localStorage.getItem('jwtToken');
            if (savedToken) {
                jwtToken = savedToken;
                document.getElementById('jwtToken').value = savedToken;
                setAuthStatus('JWT token loaded from storage', 'success');
                debugLog('JWT token loaded from localStorage');
            }
        }

        function setAuthStatus(message, type = 'info') 
        {
            const statusDiv = document.getElementById('authStatus');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function setStreamStatus(message, type = 'info') 
        {
            const statusDiv = document.getElementById('streamStatus');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function addActivity(message, type = 'info') 
        {
            const activityArea = document.getElementById('activityArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = `activity-message activity-${type}`;
            messageDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong> - ${message}`;
            activityArea.appendChild(messageDiv);
            activityArea.scrollTop = activityArea.scrollHeight;
        }

        function addComment(username, comment, commentId = null, userId = null) 
        {
            const commentsArea = document.getElementById('commentsArea');
            const commentDiv = document.createElement('div');
            commentDiv.className = 'comment-message';
            if (commentId) {
                commentDiv.dataset.commentId = commentId;
                commentElements.set(commentId, commentDiv);
            }
            
            const time = new Date().toLocaleTimeString();
            // Streamers can delete any comment
            const deleteButton = commentId ? 
                `<button class="delete-comment-btn" onclick="deleteComment('${commentId}')" title="Delete comment">❌</button>` : '';
            
            commentDiv.innerHTML = `
                <div class="comment-time">${time}</div>
                <div class="comment-username">${username}</div>
                <div class="comment-text">${comment}</div>
                ${deleteButton}
            `;
            
            commentsArea.appendChild(commentDiv);
            commentsArea.scrollTop = commentsArea.scrollHeight;
        }
        
        function removeCommentFromUI(commentId) {
            if (commentElements.has(commentId)) {
                const commentDiv = commentElements.get(commentId);
                commentDiv.remove();
                commentElements.delete(commentId);
                debugLog(`Removed comment ${commentId} from UI`);
            }
        }
        
        function deleteComment(commentId) {
            if (!stompClient || !currentStreamId) {
                addActivity('❌ Cannot delete comment: not connected', 'error');
                return;
            }
            
            if (confirm('Are you sure you want to delete this comment?')) {
                debugLog(`Deleting comment: ${commentId}`);
                // Note: We would need to send this through a REST API call or WebSocket message
                // For now, we'll just remove it from the UI as streamers typically have moderation rights
                addActivity(`💬 Streamer deleted comment: ${commentId}`, 'comment');
                removeCommentFromUI(commentId);
            }
        }

        function updateStats() 
        {
            document.getElementById('viewerCount').textContent = stats.viewers;
            document.getElementById('reactionCount').textContent = stats.reactions;
            document.getElementById('commentCount').textContent = stats.comments;
            document.getElementById('connectionStatus').textContent = stompClient && stompClient.connected ? '✅' : '❌';
        }

        function updateStreamInfo() 
        {
            document.getElementById('currentStreamId').textContent = currentStreamId || '-';
            document.getElementById('currentStreamTitle').textContent = document.getElementById('streamTitle').value || '-';
            document.getElementById('currentPackageId').textContent = currentPackageId || '-';
            document.getElementById('streamStartTime').textContent = streamStartTime || '-';
        }

        let reconnectAttempts = 0;
        const MAX_RECONNECT_ATTEMPTS = 3;
        
        function connectWebSocket() 
        {
            if (!jwtToken) 
            {
                addActivity('❌ JWT token required for WebSocket connection', 'error');
                return;
            }
            
            // Reset connection status
            document.getElementById('connectionStatus').textContent = '🔄';

            if (stompClient && stompClient.connected)
            {
                debugLog('WebSocket already connected');
                return;
            }

            addActivity('🔄 Connecting to WebSocket...', 'info');
            debugLog('Initiating WebSocket connection...');
            
            try 
            {
                const socket = new SockJS(`${baseUrl}/ws/live-stream`);
                stompClient = Stomp.over(socket);

                // Enable debug logging
                stompClient.debug = function(str) 
                {
                    debugLog('STOMP: ' + str);
                };

                // Set up connection handlers
                socket.onopen = function() 
                {
                    debugLog('SockJS connection opened');
                };

                socket.onclose = function(event) 
                {
                    debugLog(`SockJS connection closed: ${event.code} - ${event.reason}`);
                    addActivity('🔌 WebSocket connection closed', 'warning');
                    updateStats();
                };

                socket.onerror = function(error)
                {
                    debugLog(`SockJS error: ${error}`);
                    addActivity('❌ WebSocket error occurred', 'error');
                };

                // Add JWT token to headers
                const headers = 
                {
                    'Authorization': `Bearer ${jwtToken}`
                };

                debugLog('Connecting with JWT token...');

                stompClient.connect(headers, function (frame) 
                {
                    console.highLight('🚀 STOMP Connection Established');
                    console.log('📋 Frame Headers:', frame.headers);
                    console.log('📄 Frame Body:', frame.body);
                    console.groupEnd();
                    
                    debugLog('STOMP connection successful');
                    addActivity('✅ WebSocket connected successfully', 'success');
                    updateStats();
                    if (currentStreamId) 
                    {
                        debugLog(`Setting up subscriptions for stream: ${currentStreamId}`);
                        // Subscribe to stream updates
                        debugLog(`Subscribing to viewers topic for stream ${currentStreamId}`);
                        console.log(`🔍 Subscribing to viewers topic: /topic/live-stream/${currentStreamId}/viewers`);
                        stompClient.subscribe(`/topic/live-stream/${currentStreamId}/viewers`, function (message) 
                        {
                            logSocketData(`/topic/live-stream/${currentStreamId}/viewers`, 'VIEWER_UPDATE', message.body);
                            try {
                                debugLog(`Received viewer update: ${message.body}`);
                                const data = JSON.parse(message.body);
                                const viewerCount = data.viewersCount || data.viewerCount || 0;
                                const action = data.action || 'UPDATE';
                                
                                console.log('👥 Processing viewer update:', {
                                    originalCount: stats.viewers,
                                    newCount: viewerCount,
                                    action: action,
                                    fullData: data
                                });
                                
                                addActivity(`👥 Viewer update: ${viewerCount} viewers (${action})`, 'viewer');
                                stats.viewers = viewerCount;
                                document.getElementById('viewerCount').textContent = viewerCount;
                                updateStats();
                            } catch (error) {
                                debugLog(`Error processing viewer update: ${error.message}`);
                                console.error('❌ Error processing viewer update:', error, 'Raw message:', message.body);
                                addActivity('❌ Error processing viewer update', 'error');
                            }
                        });

                        debugLog(`Subscribing to reactions topic for stream ${currentStreamId}`);
                        console.log(`🔍 Subscribing to reactions topic: /topic/live-stream/${currentStreamId}/reactions`);
                        stompClient.subscribe(`/topic/live-stream/${currentStreamId}/reactions`, function (message) 
                        {
                            logSocketData(`/topic/live-stream/${currentStreamId}/reactions`, 'REACTION_UPDATE', message.body);
                            try {
                                debugLog(`Received reaction update: ${message.body}`);
                                const data = JSON.parse(message.body);
                                const totalReactions = data.totalReactions || data.numberOfReactions || 0;
                                
                                console.log('❤️ Processing reaction update:', {
                                    action: data.action,
                                    username: data.username,
                                    reactionType: data.reactionType,
                                    userId: data.userId,
                                    oldCount: stats.reactions,
                                    newCount: totalReactions,
                                    fullData: data
                                });
                                
                                if (data.action === 'CURRENT') {
                                    stats.reactions = totalReactions;
                                    addActivity(`❤️ Current reactions: ${totalReactions}`, 'reaction');
                                    debugLog(`Current reactions: ${totalReactions}`);
                                } else {
                                    const username = data.username || 'Anonymous';
                                    const action = data.action === 'ADD' ? 'reacted' : 'removed reaction';
                                    const reactionType = data.reactionType || 'unknown';
                                    addActivity(`❤️ ${username} ${action}: ${reactionType}`, 'reaction');
                                    stats.reactions = totalReactions;
                                    debugLog(`Reaction update - User: ${username}, Action: ${data.action}, Type: ${reactionType}`);
                                }
                                
                                document.getElementById('reactionCount').textContent = totalReactions;
                                updateStats();
                                debugLog(`Updated reaction count to: ${totalReactions}`);
                            } catch (error) {
                                debugLog(`Error processing reaction update: ${error.message}`);
                                console.error('❌ Error processing reaction update:', error, 'Raw message:', message.body);
                                addActivity('❌ Error processing reaction update', 'error');
                            }
                        });

                        debugLog(`Subscribing to comments topic for stream ${currentStreamId}`);
                        console.log(`🔍 Subscribing to comments topic: /topic/live-stream/${currentStreamId}/comments`);
                        stompClient.subscribe(`/topic/live-stream/${currentStreamId}/comments`, function (message) 
                        {
                            logSocketData(`/topic/live-stream/${currentStreamId}/comments`, 'COMMENT_COUNT_UPDATE', message.body);
                            try {
                                debugLog(`Received comment update: ${message.body}`);
                                const data = JSON.parse(message.body);
                                const totalComments = data.totalComments || data.numberOfComments || 0;
                                
                                console.log('💬 Processing comment count update:', {
                                    action: data.action,
                                    username: data.username,
                                    userId: data.userId,
                                    oldCount: stats.comments,
                                    newCount: totalComments,
                                    fullData: data
                                });
                                
                                if (data.action === 'CURRENT') 
                                {
                                    stats.comments = totalComments;
                                    addActivity(`💬 Current comments: ${totalComments}`, 'comment');
                                    debugLog(`Current comments: ${totalComments}`);
                                } 
                                else 
                                {
                                    const username = data.username || 'Anonymous';
                                    addActivity(`💬 Comment ${data.action}: ${username} (Total: ${totalComments})`, 'comment');
                                    stats.comments = totalComments;
                                    debugLog(`Comment update - User: ${username}, Action: ${data.action}, Total: ${totalComments}`);
                                }
                                
                                document.getElementById('commentCount').textContent = totalComments;
                                updateStats();
                            } catch (error) {
                                debugLog(`Error processing comment update: ${error.message}`);
                                console.error('❌ Error processing comment update:', error, 'Raw message:', message.body);
                                addActivity('❌ Error processing comment update', 'error');
                            }
                        });

                        console.log(`🔍 Subscribing to comments feed: /topic/live-stream/${currentStreamId}/comments-feed`);
                        stompClient.subscribe(`/topic/live-stream/${currentStreamId}/comments-feed`, function (message)
                        {
                            logSocketData(`/topic/live-stream/${currentStreamId}/comments-feed`, 'COMMENT_FEED', message.body);
                            try {
                                debugLog(`Received comment feed: ${message.body}`);
                                const data = JSON.parse(message.body);
                                
                                console.log('💬 Processing comment feed:', {
                                    action: data.action,
                                    commentId: data.id,
                                    username: data.username,
                                    userId: data.userId,
                                    comment: data.comment,
                                    firstName: data.firstName,
                                    lastName: data.lastName,
                                    profileImage: data.profileImage,
                                    createdDate: data.createdDate,
                                    fullData: data
                                });
                                
                                if (data.action === 'ADD') 
                                {
                                    const username = data.username || 'Anonymous';
                                    const comment = data.comment || '';
                                    const commentId = data.id;
                                    debugLog(`New comment received - User: ${username}, Comment: "${comment}"`);
                                    addActivity(`💬 New comment from ${username}: "${comment}"`, 'comment');
                                    addComment(username, comment, commentId, data.userId);
                                } 
                                else if (data.action === 'REMOVE') 
                                {
                                    const username = data.username || 'Someone';
                                    const commentId = data.id;
                                    debugLog(`Comment removed - User: ${username}`);
                                    addActivity(`💬 Comment removed by ${username}`, 'comment');
                                    removeCommentFromUI(commentId);
                                }
                                debugLog(`Comment feed processed successfully`);
                            } catch (error) {
                                debugLog(`Error processing comment feed: ${error.message}`);
                                console.error('❌ Error processing comment feed:', error, 'Raw message:', message.body);
                                addActivity('❌ Error processing comment feed', 'error');
                            }
                        });

                        console.log(`🔍 Subscribing to user reactions: /topic/live-stream/${currentStreamId}/user-reactions`);
                        stompClient.subscribe(`/topic/live-stream/${currentStreamId}/user-reactions`, function (message) 
                        {
                            logSocketData(`/topic/live-stream/${currentStreamId}/user-reactions`, 'USER_REACTION_STATE', message.body);
                            try {
                                debugLog(`Received user reaction: ${message.body}`);
                                const data = JSON.parse(message.body);
                                
                                console.log('👤 Processing user reaction state:', {
                                    action: data.action,
                                    userId: data.userId,
                                    username: data.username,
                                    hasReacted: data.hasReacted,
                                    reactionType: data.reactionType,
                                    firstName: data.firstName,
                                    lastName: data.lastName,
                                    profileImage: data.profileImage,
                                    fullData: data
                                });
                                
                                if (data.action === 'ADD') {
                                    const userFullName = `${data.firstName || ''} ${data.lastName || data.username}`.trim();
                                    addActivity(`❤️ ${userFullName} reacted: ${data.reactionType}`, 'reaction');
                                    
                                    // Add user profile image if available
                                    const reactionDiv = document.createElement('div');
                                    reactionDiv.className = 'user-reaction';
                                    reactionDiv.dataset.userId = data.userId;
                                    reactionDiv.innerHTML = `
                                        <img src="${data.profileImage || 'https://via.placeholder.com/32'}" alt="${userFullName}" class="user-avatar" onerror="this.src='https://via.placeholder.com/32'">
                                        <div class="user-info">
                                            <div class="user-name">${userFullName}</div>
                                            <div class="reaction-time">${new Date().toLocaleTimeString()}</div>
                                        </div>
                                        <span class="reaction-type">${data.reactionType}</span>
                                    `;
                                    const reactionsFeed = document.getElementById('reactionsFeed');
                                    reactionsFeed.insertBefore(reactionDiv, reactionsFeed.firstChild);
                                    
                                } else if (data.action === 'REMOVE') {
                                    const userFullName = `${data.firstName || ''} ${data.lastName || data.username}`.trim();
                                    addActivity(`❤️ ${userFullName} removed their reaction`, 'reaction');
                                    
                                    // Remove reaction from UI feed
                                    const reactionsFeed = document.getElementById('reactionsFeed');
                                    const userReactionDiv = reactionsFeed.querySelector(`[data-user-id="${data.userId}"]`);
                                    if (userReactionDiv) {
                                        userReactionDiv.remove();
                                        console.log('📋 Removed reaction div for user:', data.userId);
                                    }
                                } else if (data.action === 'CURRENT') {
                                    // Handle initial state - streamer doesn't need to react, just log
                                    debugLog(`Current user reaction state received for user ${data.userId}`);
                                    console.log('🔄 Processing current user state for streamer dashboard');
                                }
                            } catch (error) {
                                debugLog(`Error processing user reaction: ${error.message}`);
                                console.error('❌ Error processing user reaction:', error, 'Raw message:', message.body);
                                addActivity('❌ Error processing user reaction', 'error');
                            }
                        });

                        // Subscribe to stream end notifications
                        console.log(`🔍 Subscribing to stream ended: /topic/live-stream/${currentStreamId}/stream-ended`);
                        stompClient.subscribe(`/topic/live-stream/${currentStreamId}/stream-ended`, function (message) {
                            logSocketData(`/topic/live-stream/${currentStreamId}/stream-ended`, 'STREAM_ENDED', message.body);
                            try {
                                const data = JSON.parse(message.body);
                                const reason = data.reason || 'Unknown reason';
                                const endMessage = data.message || 'Stream has ended';
                                
                                console.log('🔚 Processing stream end notification:', {
                                    reason: reason,
                                    message: endMessage,
                                    influencerId: data.influencerId,
                                    influencerName: data.influencerName,
                                    endedAt: data.endedAt,
                                    action: data.action,
                                    fullData: data
                                });
                                
                                addActivity(`🔚 Stream ended: ${endMessage} (${reason})`, 'warning');
                                setStreamStatus(`Stream has ended: ${reason}`, 'warning');
                                debugLog(`Stream end notification received - Reason: ${reason}`);
                                
                                // Reset stream state
                                currentStreamId = null;
                                currentPackageId = null;
                                streamStartTime = null;
                                stats = { viewers: 0, reactions: 0, comments: 0 };
                                
                                // Clear UI
                                document.getElementById('commentsArea').innerHTML = '';
                                document.getElementById('reactionsFeed').innerHTML = '';
                                commentElements.clear();
                                
                                // Reset stream info display
                                document.getElementById('playbackUrl').textContent = '-';
                                document.getElementById('streamKey').textContent = '-';
                                
                                // Enable/disable buttons
                                document.getElementById('startBtn').disabled = false;
                                document.getElementById('stopBtn').disabled = true;
                                
                                updateStreamInfo();
                                updateStats();
                                
                                // Disconnect WebSocket after a brief delay
                                setTimeout(() => {
                                    if (stompClient && stompClient.connected) {
                                        debugLog('Auto-disconnecting WebSocket after stream end');
                                        stompClient.disconnect();
                                    }
                                }, 2000);
                                
                            } catch (error) {
                                debugLog(`Error processing stream end notification: ${error.message}`);
                                console.error('❌ Error processing stream end notification:', error, 'Raw message:', message.body);
                                addActivity('❌ Error processing stream end notification', 'error');
                            }
                        });

                        // Subscribe to user disconnect notifications
                      
                        // Subscribe to session force disconnect
                        // Get session ID from STOMP connection
                        const sessionId = stompClient.ws._transport.url.split('/').pop();
                        console.log(`🔍 Subscribing to session force disconnect: /topic/live-stream/session/${sessionId}/force-disconnect`);
                        stompClient.subscribe(`/topic/live-stream/session/${sessionId}/force-disconnect`, function (message) {
                            logSocketData(`/topic/live-stream/session/${sessionId}/force-disconnect`, 'FORCE_DISCONNECT', message.body);
                            console.log('🔌 Processing session force disconnect:', {
                                sessionId: sessionId,
                                messageBody: message.body,
                                currentStreamId: currentStreamId
                            });
                            addActivity(`🔌 Session force disconnect received`, 'warning');
                            debugLog('Session force disconnect received');
                            if (stompClient && stompClient.connected) {
                                stompClient.disconnect();
                            }
                        });

                    
                        // Join as streamer
                        debugLog('Sending streamer-join message...');
                        const streamerJoinData = {
                            liveStreamId: currentStreamId
                        };
                        logSocketData('/app/live-stream/streamer-join', 'STREAMER_JOIN', JSON.stringify(streamerJoinData), 'SENT');
                        console.log('📤 Sending streamer join message:', streamerJoinData);
                        stompClient.send("/app/live-stream/streamer-join", {}, JSON.stringify(streamerJoinData));
                    }

                }, function (error) 
                {
                    debugLog(`STOMP connection error: ${error}`);
                    addActivity('❌ WebSocket connection failed: ' + error, 'error');
                    
                    // Reset connection state
                    stompClient = null;
                    updateStats();
                    
                    // Set connection status
                    document.getElementById('connectionStatus').textContent = '❌';
                });

            } 
            catch (error) 
            {
                debugLog(`WebSocket setup error: ${error.message}`);
                addActivity('❌ WebSocket setup failed: ' + error.message, 'error');
                
                // Reset connection state
                stompClient = null;
                updateStats();
            }
        }

        function startStream() 
        {
            if (!jwtToken) 
            {
                setAuthStatus('Please enter and save your JWT token first', 'error');
                return;
            }

            const streamTitle = document.getElementById('streamTitle').value.trim();
            const packageId = document.getElementById('packageId').value.trim();

            if (!streamTitle) 
            {
                setStreamStatus('Please enter a stream title', 'error');
                return;
            }

            setStreamStatus('Starting stream...', 'warning');
            debugLog('Starting stream with title: ' + streamTitle);
            
            const requestBody = { title: streamTitle };
            if (packageId) 
            {
                requestBody.packageId = packageId;
            }

            // Call REST API to start stream
            fetch(`${baseUrl}/v1/live-stream/start`, 
            {
                method: 'POST',
                headers: 
                {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${jwtToken}`
                },
                body: JSON.stringify(requestBody)
            })
            .then(response => 
            {
                debugLog(`Stream start response: ${response.status}`);
                if (!response.ok) 
                {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => 
            {
                debugLog(`Stream started successfully: ${JSON.stringify(data)}`);
                currentStreamId = data.id;
                currentPackageId = packageId || null;
                streamStartTime = new Date().toLocaleTimeString();
                stats = { viewers: 0, reactions: 0, comments: 0 };
                
                setStreamStatus(`✅ Stream started successfully! Stream ID: ${currentStreamId}`, 'success');
                addActivity(`🎬 Stream "${streamTitle}" started with ID: ${currentStreamId}`, 'stream');
                
                // Update stream info display
                document.getElementById('playbackUrl').textContent = data.playbackUrl || '-';
                document.getElementById('streamKey').textContent = data.streamKey || '-';
                
                // Enable/disable buttons
                document.getElementById('startBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;
                
                updateStreamInfo();
                updateStats();
                
                // Connect to WebSocket after stream is started
                debugLog('Starting WebSocket connection with delay...');
                setTimeout(() => 
                {
                    debugLog('Delay complete, connecting WebSocket...');
                    connectWebSocket();
                    
                    // Add reconnection check
                    setTimeout(() => {
                        if (!stompClient || !stompClient.connected) {
                            debugLog('WebSocket not connected after initial attempt, retrying...');
                            connectWebSocket();
                        } else {
                            debugLog('WebSocket connection verified');
                        }
                    }, 2000);
                }, 2000); // Increased delay to 2 seconds for stream initialization
            })
            .catch(error => 
            {
                debugLog(`Stream start error: ${error.message}`);
                setStreamStatus('❌ Failed to start stream: ' + error.message, 'error');
                addActivity('❌ Failed to start stream: ' + error.message, 'error');
            });
        }

        function stopStream() 
        {
            if (!currentStreamId) 
            {
                setStreamStatus('No active stream to stop', 'error');
                debugLog('Stop stream called but no active stream');
                return;
            }

            setStreamStatus('Stopping stream...', 'warning');
            debugLog('Stopping stream: ' + currentStreamId);
            addActivity('⏹️ Initiating stream stop...', 'stream');
            
            // Call REST API to stop stream
            fetch(`${baseUrl}/v1/live-stream/stopLiveStream`, 
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${jwtToken}`
                },
                body: JSON.stringify({ streamId: currentStreamId })
            })
            .then(response => {
                debugLog(`Stream stop response: ${response.status}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                debugLog('Stream stopped successfully');
                addActivity(`⏹️ Stream ${currentStreamId} stopped successfully`, 'stream');
                setStreamStatus('✅ Stream stopped successfully', 'success');
                
                // Reset state
                currentStreamId = null;
                currentPackageId = null;
                streamStartTime = null;
                stats = { viewers: 0, reactions: 0, comments: 0 };
                
                // Enable/disable buttons
                document.getElementById('startBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;
                
                // Clear stream info
                document.getElementById('playbackUrl').textContent = '-';
                document.getElementById('streamKey').textContent = '-';
                
                updateStreamInfo();
                updateStats();
                
                // Disconnect WebSocket
                if (stompClient && stompClient.connected) {
                    debugLog('Disconnecting WebSocket...');
                    try {
                        stompClient.disconnect();
                        addActivity('🔌 WebSocket disconnected', 'info');
                    } catch (error) {
                        debugLog(`Error disconnecting WebSocket: ${error.message}`);
                    }
                }
                stompClient = null;
            })
            .catch(error => {
                debugLog(`Stream stop error: ${error.message}`);
                setStreamStatus('❌ Failed to stop stream: ' + error.message, 'error');
                addActivity('❌ Failed to stop stream: ' + error.message, 'error');
            });
        }

        function clearActivity() 
        {
            document.getElementById('activityArea').innerHTML = '';
        }

        function clearComments() 
        {
            document.getElementById('commentsArea').innerHTML = '';
        }

        function clearReactions() 
        {
            document.getElementById('reactionsFeed').innerHTML = '';
        }

        // Export/Import Settings
        function exportSettings() {
            const settings = {
                jwtToken: jwtToken,
                baseUrl: baseUrl,
                streamTitle: document.getElementById('streamTitle').value,
                packageId: document.getElementById('packageId').value,
                debugEnabled: debugEnabled,
                timestamp: new Date().toISOString()
            };
            
            const blob = new Blob([JSON.stringify(settings, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `influencer-settings-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            addActivity('💾 Settings exported successfully', 'info');
            debugLog('Settings exported to file');
        }
        
        function importSettings() {
            document.getElementById('settingsFile').click();
        }
        
        function handleSettingsImport(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const settings = JSON.parse(e.target.result);
                    
                    if (settings.jwtToken) {
                        jwtToken = settings.jwtToken;
                        document.getElementById('jwtToken').value = settings.jwtToken;
                        localStorage.setItem('jwtToken', settings.jwtToken);
                    }
                    
                    if (settings.streamTitle) {
                        document.getElementById('streamTitle').value = settings.streamTitle;
                    }
                    
                    if (settings.packageId) {
                        document.getElementById('packageId').value = settings.packageId;
                    }
                    
                    if (typeof settings.debugEnabled === 'boolean') {
                        debugEnabled = settings.debugEnabled;
                        document.getElementById('debugSection').style.display = debugEnabled ? 'block' : 'none';
                    }
                    
                    setAuthStatus('💼 Settings imported successfully', 'success');
                    addActivity(`💼 Settings imported from ${file.name}`, 'info');
                    debugLog(`Settings imported from ${file.name}`);
                } catch (error) {
                    setAuthStatus('Failed to import settings: ' + error.message, 'error');
                    addActivity('❌ Failed to import settings', 'error');
                    debugLog(`Failed to import settings: ${error.message}`);
                }
            };
            reader.readAsText(file);
        }

        // Initialize
        loadToken();
        updateStreamInfo();
        updateStats();

        // Handle disconnect
        window.addEventListener('beforeunload', function() {
            if (stompClient && stompClient.connected) {
                stompClient.disconnect();
            }
        });
    </script>
</body>
</html>