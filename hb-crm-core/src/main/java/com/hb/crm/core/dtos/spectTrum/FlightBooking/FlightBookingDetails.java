package com.hb.crm.core.dtos.spectTrum.FlightBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Detailed flight booking information
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FlightBookingDetails {
    
    /**
     * Travel and technology reference
     */
    private String ttReference;
    
    /**
     * Passenger name record
     */
    private String pnr;
    
    /**
     * Flight code identifier
     */
    private String flightCode;
    
    /**
     * Origin location code
     */
    private String origin;
    
    /**
     * Origin airport details
     */
    private BookingAirport originAirport;
    
    /**
     * Destination location code
     */
    private String destination;
    
    /**
     * Destination airport details
     */
    private BookingAirport destinationAirport;
    
    /**
     * Ticket time limit
     */
    private LocalDateTime tktTimeLimit;
    
    /**
     * Ticket time limit set by airline
     */
    private String tktTimeLimitByAirline;
    
    /**
     * Current booking status
     */
    private String status;
    
    /**
     * Validating carrier code
     */
    private String validatingCarrier;
    
    /**
     * Type of fare
     */
    private String fareType;
    
    /**
     * Travel date
     */
    private LocalDateTime travelDate;
    
    /**
     * Fare expiration date
     */
    private LocalDateTime fareExpireDate;
    
    /**
     * Source of the booking
     */
    private String source;
    
    /**
     * Source of the supplier
     */
    private String supplierSource;
    
    /**
     * Supplier reference identifier
     */
    private String supplierReferenceId;
    
    /**
     * Currency code
     */
    private String currency;
    
    /**
     * Total price
     */
    private BigDecimal total;
    
    /**
     * Whether this is a pre-combined booking
     */
    private Boolean preCombine;
    
    /**
     * Whether the booking is refundable
     */
    private Boolean isRefundable;
    
    /**
     * List of fare rules
     */
    private List<BookingFareRule> fareRules;
    
    /**
     * List of detailed fare rules
     */
    private List<BookingDetailedFareRule> detailedFareRules;
    
    /**
     * List of flight itineraries
     */
    private List<BookingItinerary> itineraries;
    
    /**
     * List of passengers
     */
    private List<BookingPassenger> passengers;
}
