package com.hb.crm.core.dtos.spectTrum.HotelConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.hb.crm.core.dtos.spectTrum.HotelResponse.Occupancy;
import lombok.Data;

import java.util.List;

/**
 * Request DTO for Hotel Confirm Price API from Spectrum Travel API
 * 
 * This DTO represents the request structure for confirming hotel pricing
 * for selected property and room options.
 * 
 * API Endpoint: POST /api/v1/beta/confirmprice?agencyId={agencyId}
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelConfirmPriceRequest {
    
    /**
     * Property reference identifier for price confirmation
     */
    private String propertyReferenceId;
    
    /**
     * List of rooms with selected options for price confirmation
     */
    private List<HotelConfirmPriceRoom> rooms;
    
    /**
     * Indicates if this is a package booking
     */
    private boolean isPackage;
    
    /**
     * Currency code for pricing
     */
    private String currency;
    
    /**
     * Customer nationality
     */
    private String custNationality;
    
    /**
     * Check-in date in string format (YYYY-MM-DD)
     */
    private String checkinDate;
    
    /**
     * Check-out date in string format (YYYY-MM-DD)
     */
    private String checkoutDate;
    
    /**
     * Whether the booking should be refundable
     */
    private boolean isRefundable;
    
    /**
     * List of occupancy information including adults and children
     */
    private List<Occupancy> occupancies;
}