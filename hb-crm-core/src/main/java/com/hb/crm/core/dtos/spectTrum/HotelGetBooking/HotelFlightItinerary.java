package com.hb.crm.core.dtos.spectTrum.HotelGetBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * Flight itinerary for hotel package booking
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelFlightItinerary {
    
    /**
     * Segment group identifier
     */
    private String segmentGroupId;
    
    /**
     * Stop over segments
     */
    private String stopOverSegments;
    
    /**
     * Flight segments
     */
    private List<HotelFlightSegment> segments;
}
