package com.hb.crm.core.dtos.spectTrum.HotelGetBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Hotel deposit policy information
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelDepositPolicy {
    
    /**
     * Number of nights for deposit
     */
    private String nights;
    
    /**
     * Deposit amount
     */
    private String amount;
    
    /**
     * Deposit percentage
     */
    private String percent;
    
    /**
     * Whether remainder applies
     */
    private Boolean remainder;
    
    /**
     * When deposit is due
     */
    private String due;
}
