package com.hb.crm.core.dtos.spectTrum.HotelAvailabilityResponse;

import com.hb.crm.core.dtos.spectTrum.HotelResponse.Amenity;
import com.hb.crm.core.dtos.spectTrum.HotelResponse.Image;
import lombok.Data;

import java.util.List;

/**
 * Hotel room information and availability
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
public class Room {
    
    /**
     * Room ID
     */
    private String id;
    
    /**
     * Room name
     */
    private String name;
    
    /**
     * Source of the room data
     */
    private String source;
    
    /**
     * Room descriptions
     */
    private List<String> descriptions;
    
    /**
     * Room amenities
     */
    private List<Amenity> amenities;
    
    /**
     * Room images
     */
    private List<Image> images;
    
    /**
     * Maximum allowed occupancy
     */
    private MaxAllowed maxAllowed;
    
    /**
     * Room comments
     */
    private String roomComments;
    
    /**
     * Number of available rooms
     */
    private int availableRooms;
    
    /**
     * Booking options for this room
     */
    private List<RoomOption> options;
    
    /**
     * List of guest names
     */
    private List<String> nameList;
    
    /**
     * Whether check rate is supported
     */
    private boolean supportsCheckRate;
    
    /**
     * Whether room is combinable with others
     */
    private boolean isCombinable;
}
