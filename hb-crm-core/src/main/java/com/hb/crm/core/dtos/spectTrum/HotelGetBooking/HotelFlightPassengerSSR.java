package com.hb.crm.core.dtos.spectTrum.HotelGetBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Flight passenger special service request for hotel package booking
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelFlightPassengerSSR {
    
    /**
     * Segment group identifier
     */
    private String segmentGroupId;
    
    /**
     * SSR type identifier
     */
    private Integer ssrTypeId;
    
    /**
     * SSR type
     */
    private String ssrType;
    
    /**
     * SSR value
     */
    private BigDecimal ssrValue;
    
    /**
     * Unit type identifier
     */
    private Integer unitTypeId;
    
    /**
     * Unit type
     */
    private String unitType;
    
    /**
     * Quantity
     */
    private Integer quantity;
    
    /**
     * Currency
     */
    private String currency;
    
    /**
     * Price
     */
    private BigDecimal price;
    
    /**
     * Description
     */
    private String description;
}
