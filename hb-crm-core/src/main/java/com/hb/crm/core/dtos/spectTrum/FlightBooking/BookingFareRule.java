package com.hb.crm.core.dtos.spectTrum.FlightBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Fare rule information for booking
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BookingFareRule {
    
    /**
     * Fare rule category
     */
    private String category;
    
    /**
     * Currency code for penalty amount
     */
    private String currency;
    
    /**
     * Maximum penalty amount
     */
    private BigDecimal maxPenaltyAmount;
    
    /**
     * Whether the rule is not applicable
     */
    private Boolean notApplicable;
}
