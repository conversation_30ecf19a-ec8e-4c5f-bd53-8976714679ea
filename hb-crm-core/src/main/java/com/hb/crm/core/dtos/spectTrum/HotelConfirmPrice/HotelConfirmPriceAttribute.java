package com.hb.crm.core.dtos.spectTrum.HotelConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Hotel attribute information
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelConfirmPriceAttribute {
    
    /**
     * Attribute identifier
     */
    private String id;
    
    /**
     * Attribute type
     */
    private String type;
    
    /**
     * Attribute name
     */
    private String name;
    
    /**
     * Attribute value
     */
    private String value;
}
