package com.hb.crm.core.services;

import com.hb.crm.core.beans.LiveStream.LiveStream;
import com.hb.crm.core.beans.LiveStream.LiveStreamReaction;
import com.hb.crm.core.beans.LiveStream.LiveStreamComment;
import com.hb.crm.core.dtos.PageDto;
import com.hb.crm.core.dtos.LiveStream.LiveStreamCreateRequestDto;
import com.hb.crm.core.dtos.LiveStream.LiveStreamDto;
import com.hb.crm.core.dtos.LiveStream.LiveStreamReactionDto;
import com.hb.crm.core.dtos.LiveStream.LiveStreamCommentDto;
import com.hb.crm.core.dtos.LiveStream.LiveStreamStartResponseDto;
import com.hb.crm.core.Enums.EntityName;
import com.hb.crm.core.Enums.EntityType;
import com.hb.crm.core.Enums.LiveStreamStatus;
import com.hb.crm.core.Enums.ReactionType;
import com.hb.crm.core.Enums.NotificationType;
import com.hb.crm.core.Enums.NotificationEntityType;
import com.hb.crm.core.Enums.UserType;
import com.hb.crm.core.beans.Package;
import com.hb.crm.core.beans.User;
import com.hb.crm.core.repositories.LiveStream.LiveStreamReactionRepository;
import com.hb.crm.core.repositories.LiveStream.LiveStreamCommentRepository;
import com.hb.crm.core.repositories.LiveStream.LiveStreamRepository;
import com.hb.crm.core.searchBeans.ReactionSearch;
import com.hb.crm.core.searchRepositories.ReactionSearchRepository;
import com.hb.crm.core.repositories.UserRepository;
import com.hb.crm.core.services.interfaces.IVSStreamService;
import com.hb.crm.core.services.interfaces.LiveStreamService;
import com.hb.crm.core.services.interfaces.NotificationService;
import com.hb.crm.core.repositories.PackageRepository;
import lombok.RequiredArgsConstructor;

import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.domain.Sort;



@Service
@RequiredArgsConstructor
public class LiveStreamServiceImpl implements LiveStreamService
{

    private final PackageRepository packageRepository;
    private final LiveStreamRepository liveStreamRepository;
    private final IVSStreamService ivsStreamService;
    private final MongoTemplate mongoTemplate;
    private final LiveStreamReactionRepository liveStreamReactionRepository;
    private final LiveStreamCommentRepository liveStreamCommentRepository;
    private final NotificationService notificationService;
    private final UserRepository userRepository;
    private final ReactionSearchRepository reactionSearchRepository;
    private final LiveStreamSessionManager liveStreamSessionManager;

    @Override
    public LiveStream startLiveStream(LiveStreamCreateRequestDto request, User influencer) 
    {
        // Check if influencer already has an active live stream
        Query activeStreamQuery = new Query();
        activeStreamQuery.addCriteria(Criteria.where("infulancer.$id").is(new ObjectId(influencer.getId()))
                                               .and("status").is(LiveStreamStatus.LIVE));
        
        List<LiveStream> activeLiveStreams = mongoTemplate.find(activeStreamQuery, LiveStream.class);
      
        if (!activeLiveStreams.isEmpty()) 
        {
            throw new IllegalArgumentException("Influencer already has an active live stream. Please end the current stream before starting a new one.");
        }
        
        // Validate package (if provided)
        Package pkg = null;
        if (request.getPackageId() != null && !request.getPackageId().isEmpty())
        {
            pkg = packageRepository.findById(request.getPackageId()).orElseThrow(() -> new IllegalArgumentException("Package not found"));
            if (!pkg.getInfulancer().getId().equals(influencer.getId()))
            {
                throw new IllegalArgumentException("Package does not belong to influencer");
            }
        }

        // Create IVS Channel
        LiveStreamStartResponseDto result = ivsStreamService.createChannelForInfluencer(influencer.getId());

        
        // Save LiveStream
        LiveStream liveStream = new LiveStream();
        liveStream.setInfulancer(influencer);
        liveStream.setTitle(request.getTitle());
        liveStream.setPackageRef(pkg);
        liveStream.setChannelArn(result.getChannelArn());
        liveStream.setStreamKey(result.getStreamKey());
        liveStream.setIngestEndpoint(result.getIngestEndpoint());
        liveStream.setPlaybackUrl(result.getPlaybackUrl());
        liveStream.setStatus(LiveStreamStatus.LIVE);
        liveStream.setCreatedAt(LocalDateTime.now());

        LiveStream saved = liveStreamRepository.save(liveStream);
        
        // Send notifications to users
        try 
        {
            //sendLiveStreamNotifications(saved, influencer, pkg);
            sendLiveStreamNotificationsAsync(saved, influencer, pkg);
        } catch (Exception e) {
            // Log error but don't fail the live stream creation
            System.out.println("Failed to send live stream notifications: " + e.getMessage());
        }
        
        return saved;
    }
    
   @Override
    public LiveStream stopLiveStream(LiveStream stream) 
    {         
        try 
        {
            ivsStreamService.stopStream(stream.getChannelArn());
        } 
        catch (Exception e) 
        {
        }
        stream.setStatus(LiveStreamStatus.COMPLETED);
        LiveStream updated = liveStreamRepository.save(stream);        
        try 
        {
            liveStreamSessionManager.closeAllConnectionsForStream(stream.getId());
        } 
        catch (Exception e) 
        {
            e.printStackTrace();
        }
        
        try 
        {
            ivsStreamService.convertToMp4Async(stream.getId(), stream.getChannelArn());
        } 
        catch (Exception e) 
        {
        }
        
        return updated;
    }
    @Override
    public LiveStream cancelLiveStream(LiveStream stream) 
    {         
        try 
        {
            ivsStreamService.stopStream(stream.getChannelArn());
        } 
        catch (Exception e) 
        {
        }
        stream.setStatus(LiveStreamStatus.CANCELED);
        LiveStream updated = liveStreamRepository.save(stream);        
        try 
        {
            liveStreamSessionManager.closeAllConnectionsForStream(stream.getId());
        } 
        catch (Exception e) 
        {
            e.printStackTrace();
        }
        
        try 
        {
            ivsStreamService.convertToMp4Async(stream.getId(), stream.getChannelArn());
        } 
        catch (Exception e) 
        {
        }
        
        return updated;
    }
    @Override
    public PageDto<LiveStream> getLiveStreams(String influencerId, String packageId, int page, int limit) 
    {
        final org.springframework.data.domain.Pageable pageable = PageRequest.of(page, limit, Sort.by(Sort.Direction.DESC, "createdAt"));
        
        Query query = new Query();
        List<Criteria> criteria = new ArrayList<>();

        if (influencerId != null && !influencerId.isEmpty()) 
        {
            criteria.add(Criteria.where("infulancer.$id").is(new ObjectId(influencerId)));
        }
        
        if (packageId != null && !packageId.isEmpty()) 
        {
            criteria.add(Criteria.where("packageRef.$id").is(new ObjectId(packageId)));
        }
    
        if (!criteria.isEmpty()) 
        {
            query.addCriteria(new Criteria().andOperator(criteria.toArray(new Criteria[0])));
        }
        
        long total = mongoTemplate.count(query, LiveStream.class);
        
        query.with(pageable);
        
        List<LiveStream> liveStreams = mongoTemplate.find(query, LiveStream.class);
       
        PageDto<LiveStream> pageDto = new PageDto<>();
        pageDto.setItems(liveStreams);
        pageDto.setTotalNoOfItems(total);
                
        return pageDto;
    }
 
    
    @Override
    public LiveStreamReaction reactToLiveStream(String liveStreamId, User user, ReactionType reactionType) 
    {
        LiveStream liveStream = liveStreamRepository.findById(liveStreamId)
                .orElseThrow(() -> new IllegalArgumentException("Live stream not found"));

        if(liveStream.getStatus() != LiveStreamStatus.LIVE)
        {
            throw new IllegalArgumentException("Live stream is not live");
        }

        Optional<LiveStreamReaction> existingReaction = liveStreamReactionRepository
                .findByLiveStreamIdAndUserId(new ObjectId(liveStreamId), new ObjectId(user.getId()));

        LiveStreamReaction reaction;
        if (existingReaction.isPresent()) 
        {
            reaction = existingReaction.get();
            reaction.setReactionType(reactionType);
            reaction.setUpdateDate(new java.util.Date());
        } 
        else 
        {
            reaction = new LiveStreamReaction(user, liveStream);
            reaction.setReactionType(reactionType);
            reaction.setCreateDate(new java.util.Date());
            
            liveStream.setNumberOfReactions(liveStream.getNumberOfReactions() + 1);
            liveStreamRepository.save(liveStream);
        }

        return liveStreamReactionRepository.save(reaction);
    }

    @Override
    public void removeReactionFromLiveStream(String liveStreamId, User user) 
    {
        LiveStream liveStream = liveStreamRepository.findById(liveStreamId)
                .orElseThrow(() -> new IllegalArgumentException("Live stream not found"));

        if(liveStream.getStatus() != LiveStreamStatus.LIVE)
        {
            throw new IllegalArgumentException("Live stream is not live");
        }

        Optional<LiveStreamReaction> reaction = liveStreamReactionRepository
                .findByLiveStreamIdAndUserId(new ObjectId(liveStreamId), new ObjectId(user.getId()));

        if (reaction.isPresent()) {
            liveStreamReactionRepository.delete(reaction.get());
            
            liveStream.setNumberOfReactions(Math.max(0, liveStream.getNumberOfReactions() - 1));
            liveStreamRepository.save(liveStream);
        }
    }

    @Override
    public PageDto<LiveStreamReactionDto> getLiveStreamReactions(String liveStreamId, int page, int size) 
    {
        final org.springframework.data.domain.Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createDate"));
        
        Page<LiveStreamReaction> reactionsPage = liveStreamReactionRepository.findByLiveStreamId(new ObjectId(liveStreamId), pageable);
        
        List<LiveStreamReactionDto> reactionDtos = reactionsPage.getContent().stream()
            .map(reaction -> new LiveStreamReactionDto(
                reaction.getId(),
                reaction.getUser().getId(),
                reaction.getUser().getUsername(),
                liveStreamId,
                reaction.getReactionType(),
                reaction.getCreateDate(),
                reaction.getUpdateDate()
            ))
            .collect(Collectors.toList());
        
        PageDto<LiveStreamReactionDto> pageDto = new PageDto<>();
        pageDto.setItems(reactionDtos);
        pageDto.setTotalNoOfItems(reactionsPage.getTotalElements());
        pageDto.setPageNumber(page);
        pageDto.setItemsPerPage(size);
        
        return pageDto;
    }
    
    @Override
    public LiveStreamComment addCommentToLiveStream(String liveStreamId, User user, String comment) 
    {
        LiveStream liveStream = liveStreamRepository.findById(liveStreamId)
                .orElseThrow(() -> new IllegalArgumentException("Live stream not found"));
        if(liveStream.getStatus() != LiveStreamStatus.LIVE)
        {
            throw new IllegalArgumentException("Live stream is not live");
        }
        LiveStreamComment liveStreamComment = new LiveStreamComment(comment, liveStream, user);
        
        liveStream.setNumberOfComments(liveStream.getNumberOfComments() + 1);
        liveStreamRepository.save(liveStream);
        
        return liveStreamCommentRepository.save(liveStreamComment);
    }

    @Override
    public void removeCommentFromLiveStream(String commentId, User user) 
    {
        Optional<LiveStreamComment> commentOpt = liveStreamCommentRepository.findById(commentId);
        if (!commentOpt.isPresent())
        {
            throw new IllegalArgumentException("Comment not found");
        }
       
        LiveStreamComment comment = commentOpt.get();
        
        if (!comment.getUser().getId().equals(user.getId())) 
        {
            throw new IllegalArgumentException("User can only delete their own comments");
        }
        
        LiveStream liveStream = comment.getLiveStream();
        if(liveStream.getStatus() != LiveStreamStatus.LIVE)
        {
            throw new IllegalArgumentException("Live stream is not live");
        }
        liveStream.setNumberOfComments(Math.max(0, liveStream.getNumberOfComments() - 1));
        liveStreamRepository.save(liveStream);
        
        liveStreamCommentRepository.delete(comment);
    }

    @Override
    public PageDto<LiveStreamCommentDto> getLiveStreamComments(String liveStreamId, int page, int size) 
    {
        final org.springframework.data.domain.Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdDate"));
        
        Page<LiveStreamComment> commentsPage = liveStreamCommentRepository.findByLiveStreamId(new ObjectId(liveStreamId), pageable);
        
        List<LiveStreamCommentDto> commentDtos = commentsPage.getContent().stream()
            .map(comment -> new LiveStreamCommentDto(
                comment.getId(),
                comment.getComment(),
                comment.getUser().getId(),
                comment.getUser().getUsername(),
                liveStreamId,
                comment.getCreatedDate(),
                comment.getUpdatedDate(),
                comment.getUser().getProfileImage(),
                comment.getUser().getFirstName(),
                comment.getUser().getLastName()
            ))
            .collect(Collectors.toList());
        
        PageDto<LiveStreamCommentDto> pageDto = new PageDto<>();
        pageDto.setItems(commentDtos);
        pageDto.setTotalNoOfItems(commentsPage.getTotalElements());
        pageDto.setPageNumber(page);
        pageDto.setItemsPerPage(size);
        
        return pageDto;
    }

    @Override
    public LiveStreamDto getLiveStreamById(String liveStreamId)
    {
        LiveStream liveStream = liveStreamRepository.findById(liveStreamId)
                .orElseThrow(() -> new IllegalArgumentException("Live stream not found"));
        
        LiveStreamDto liveStreamDto = convertToDto(liveStream);
      
        return liveStreamDto;
    }
    
    //////////////////
    /// Helper methods
    //////////////////
    
    @Async
    public void sendLiveStreamNotificationsAsync(LiveStream liveStream, User influencer, Package pkg) 
    {
        try 
        {
            sendLiveStreamNotifications(liveStream, influencer, pkg);
        } 
        catch (Exception e) 
        {
            System.out.println("Error sending live stream notifications asynchronously: " + e.getMessage());
        }
    }
    
    private void sendLiveStreamNotifications(LiveStream liveStream, User influencer, Package pkg) {
        List<User> usersToNotify = getUsersToNotifyForLiveStream(influencer, pkg);
        
        List<Object> entities = new ArrayList<>();
        entities.add(liveStream);
        entities.add(influencer);
        if (pkg != null) 
        {
            entities.add(pkg);
        }
        
        for (User user : usersToNotify) 
        {
            notificationService.sendAndStoreNotification(
                liveStream.getId(),                     // entityId
                NotificationType.LiveStarted,          // type
                user,                                   // user to notify
                entities,                               // entities for template variables
                influencer.getProfileImage(),           // image
                null,                                   // icon (will use template default)
                NotificationEntityType.LIVE_STREAM,           // entityType
                influencer,                             // navigatedUser
                null,                                   // slug
                influencer.getUsername(),               // username
                UserType.Influencer                     // navigatedUserType
            );
        }
    }
    
    private List<User> getUsersToNotifyForLiveStream(User influencer, Package pkg) 
    {
        // Get followers of the influencer
        List<User> followers = findFollowers(influencer.getId());
        
        List<User> favoriteUsers = new ArrayList<>();
        
        // Get users who have marked the package as favorite (if package exists)
        if (pkg != null) 
        {
            List<ReactionSearch> favouriteReactions = reactionSearchRepository.findByEntityNameAndEntityTypeAndEntityId(
                EntityName.Package,
                EntityType.Favourite,
                pkg.getId()  
            );

            // Extract the user's ids
            List<String> usersIds = favouriteReactions
                    .stream()
                    .map(ReactionSearch::getUserId)
                    .filter(Objects::nonNull)  
                    .toList();

            if (!usersIds.isEmpty()) {
                favoriteUsers = userRepository.findAllById(usersIds);
            }
        }
        
        List<User> allUsers = new ArrayList<>();
        allUsers.addAll(followers);
        allUsers.addAll(favoriteUsers);
        
        return allUsers.stream()
                .filter(Objects::nonNull)  
                .collect(Collectors.toMap(
                    User::getId,          
                    user -> user,          
                    (existing, duplicate) -> existing  
                ))
                .values()
                .stream()
                .collect(Collectors.toList());
    }
    
    private List<User> findFollowers(String influencerId)
    {
        // Get followed influencers
        var followedInfluencersReactions = reactionSearchRepository.findByEntityNameAndEntityTypeAndEntityId(
                EntityName.User,
                EntityType.Follow,
                influencerId
        );

        var followedInfluencersIds = followedInfluencersReactions
                .stream()
                .map(ReactionSearch::getUserId)
                .filter(Objects::nonNull)
                .toList();

        return userRepository.findByIdIn(followedInfluencersIds);
    }
    
    private LiveStreamDto convertToDto(LiveStream liveStream) 
    {
        LiveStreamDto dto = new LiveStreamDto();
        dto.setId(liveStream.getId());
        dto.setTitle(liveStream.getTitle());
        dto.setChannelArn(liveStream.getChannelArn());
        dto.setStreamKey(liveStream.getStreamKey());
        dto.setIngestEndpoint(liveStream.getIngestEndpoint());
        dto.setPlaybackUrl(liveStream.getPlaybackUrl());
        dto.setStatus(liveStream.getStatus());
        dto.setCreatedAt(liveStream.getCreatedAt());
        dto.setEndedAt(liveStream.getEndedAt());
        dto.setNumberOfReactions(liveStream.getNumberOfReactions());
        dto.setNumberOfComments(liveStream.getNumberOfComments());
        dto.setViewersCount(liveStream.getViewersCount());
        dto.setMp4ConversionStatus(liveStream.getMp4ConversionStatus());
        dto.setMp4Key(liveStream.getMp4Key());
        
        if (liveStream.getInfulancer() != null) 
        {
            dto.setInfulancerId(liveStream.getInfulancer().getId());
            dto.setInfulancerName(liveStream.getInfulancer().getUsername());
            dto.setInfulancerFirstName(liveStream.getInfulancer().getFirstName());
            dto.setInfulancerLastName(liveStream.getInfulancer().getLastName());
            dto.setInfulancerImage(liveStream.getInfulancer().getProfileImage());
        }
        
        if (liveStream.getPackageRef() != null)
        {
            dto.setPackageId(liveStream.getPackageRef().getId());
            dto.setPackageName(liveStream.getPackageRef().getName());
            dto.setPackageSlug(liveStream.getPackageRef().getSlug());
        }
        
        return dto;
    }
}