package com.hb.crm.core.dtos.spectTrum.FlightSearch;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.hb.crm.core.beans.Flight.Flight;

import java.util.ArrayList;
@JsonIgnoreProperties(ignoreUnknown = true)

public class ResonseData {

    private ArrayList<Flight> flightOffers;

    public ArrayList<Flight> getFlightOffers() {
        return flightOffers;
    }

    public void setFlightOffers(ArrayList<Flight> flightOffers) {
        this.flightOffers = flightOffers;
    }


}
