package com.hb.crm.core.dtos.spectTrum.HotelPreBook;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * Response DTO for Hotel Pre-Book Summary API from Spectrum Travel API
 * 
 * This DTO represents the response structure for hotel pre-booking summary
 * containing booking reference and status information.
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelPreBookResponse {
    
    /**
     * Indicates if the pre-book request was successful
     */
    private Boolean success;
    
    /**
     * Status code of the pre-book response
     */
    private Integer statusCode;
    
    /**
     * Response message
     */
    private String message;
    
    /**
     * Main pre-book data containing booking reference and status
     */
    private HotelPreBookData data;
    
    /**
     * List of errors if the pre-book failed or had issues
     */
    private List<String> errors;
}
