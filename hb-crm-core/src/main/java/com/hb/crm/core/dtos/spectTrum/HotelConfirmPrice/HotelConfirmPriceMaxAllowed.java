package com.hb.crm.core.dtos.spectTrum.HotelConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Maximum allowed occupancy for hotel room
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelConfirmPriceMaxAllowed {
    
    /**
     * Maximum number of adults
     */
    private Integer adult;
    
    /**
     * Maximum number of children
     */
    private Integer children;
    
    /**
     * Maximum total occupancy
     */
    private Integer total;
}
