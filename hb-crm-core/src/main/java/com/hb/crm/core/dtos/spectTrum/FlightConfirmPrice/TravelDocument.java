package com.hb.crm.core.dtos.spectTrum.FlightConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Travel document requirements
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TravelDocument {
    
    private String documentType;
    private String birthPlace;
    private String issuanceLocation;
    private String issuanceDate;
    private String number;
    private String expiryDate;
    private String issuanceCountry;
    private String validityCountry;
    private String nationality;
    private Boolean holder;
    private String travelerType;
}
