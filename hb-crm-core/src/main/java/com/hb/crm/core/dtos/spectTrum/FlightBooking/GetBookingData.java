package com.hb.crm.core.dtos.spectTrum.FlightBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Booking data containing trip and flight details
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetBookingData {
    
    /**
     * Unique trip identifier
     */
    private String tripId;
    
    /**
     * Current booking status
     */
    private String bookingStatus;
    
    /**
     * Detailed flight booking information
     */
    private FlightBookingDetails flight;
}
