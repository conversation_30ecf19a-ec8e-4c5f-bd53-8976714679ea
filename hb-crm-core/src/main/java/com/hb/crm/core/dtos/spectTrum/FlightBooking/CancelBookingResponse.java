package com.hb.crm.core.dtos.spectTrum.FlightBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * Response DTO for Cancel Booking API from Spectrum Travel API
 * 
 * This DTO represents the response structure for cancelling a flight booking.
 * 
 * API Endpoint: DELETE /api/v1/beta/cancelbooking/{tripId}/{bookingReferenceId}?agencyId={agencyId}
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CancelBookingResponse {
    
    /**
     * Indicates if the cancellation request was successful
     */
    private Boolean success;
    
    /**
     * Status code of the cancellation response
     */
    private Integer statusCode;
    
    /**
     * Response message providing cancellation status information
     */
    private String message;
    
    /**
     * Main cancellation data containing cancellation details
     */
    private CancelBookingData data;
    
    /**
     * List of errors if the cancellation failed or had issues
     */
    private List<String> errors;
}
