package com.hb.crm.core.dtos.spectTrum.HotelPreBook;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * Request DTO for Hotel Pre-Book Summary API from Spectrum Travel API
 * 
 * This DTO represents the request structure for checking booking summary
 * before creating the actual hotel booking confirmation.
 * 
 * API Endpoint: POST /api/v1/beta/prebook/{agencyId}
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelPreBookRequest {
    
    /**
     * Property reference identifier
     */
    private String propertyReferenceId;
    
    /**
     * Contact email address
     */
    private String email;
    
    /**
     * Contact phone information
     */
    private HotelPreBookPhone phone;
    
    /**
     * Special hotel instructions
     */
    private String hotelInstructions;
    
    /**
     * Tax number for business bookings
     */
    private String taxNumber;
    
    /**
     * Additional notes for the booking
     */
    private String notes;
    
    /**
     * List of rooms to be booked with guest details
     */
    private List<HotelPreBookRoom> rooms;
}
