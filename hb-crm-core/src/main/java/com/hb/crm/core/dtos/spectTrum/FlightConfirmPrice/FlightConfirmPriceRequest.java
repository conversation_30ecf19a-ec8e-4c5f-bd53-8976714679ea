package com.hb.crm.core.dtos.spectTrum.FlightConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Main request wrapper for Flight Confirm Price API
 * POST: /api/v1/beta/confirmprice
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FlightConfirmPriceRequest {
    
    private FlightConfirmPriceData data;
    private String upgradeCabin;
    private String include = "bags,other-services,detailed-fare-rules";
    private Boolean forceClass = false;
}
