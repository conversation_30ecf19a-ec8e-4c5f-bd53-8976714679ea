package com.hb.crm.core.dtos.spectTrum.HotelConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Hotel amenity information
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelConfirmPriceAmenity {
    
    /**
     * Amenity identifier
     */
    private String id;
    
    /**
     * Amenity description
     */
    private String description;
    
    /**
     * Amenity status
     */
    private String status;
}
