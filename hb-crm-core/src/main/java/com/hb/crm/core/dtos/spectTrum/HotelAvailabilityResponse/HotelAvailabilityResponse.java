package com.hb.crm.core.dtos.spectTrum.HotelAvailabilityResponse;

import lombok.Data;

import java.util.List;

/**
 * Main response wrapper for hotel availability API
 * 
 * This represents the top-level response structure from the Spectrum Travel API
 * for hotel availability checks.
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
public class HotelAvailabilityResponse {
    
    /**
     * Indicates if the request was successful
     */
    private boolean success;
    
    /**
     * HTTP status code of the response
     */
    private int statusCode;
    
    /**
     * Response message
     */
    private String message;
    
    /**
     * Main data object containing hotel availability details
     */
    private HotelAvailabilityData data;
    
    /**
     * List of any errors encountered
     */
    private List<String> errors;
}
