package com.hb.crm.core.dtos.spectTrum.HotelConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Hotel address information
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelConfirmPriceAddress {
    
    /**
     * Address line 1
     */
    private String line1;
    
    /**
     * Address line 2
     */
    private String line2;
    
    /**
     * City name
     */
    private String city;
    
    /**
     * State or province name
     */
    private String stateProvinceName;
    
    /**
     * Postal code
     */
    private String postalCode;
    
    /**
     * Country code
     */
    private String countryCode;
}
