package com.hb.crm.core.dtos.spectTrum.HotelPreBook;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * Room information for hotel pre-book request
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelPreBookRoom {
    
    /**
     * Option identifier for the room
     */
    private String optionId;
    
    /**
     * Room identifier
     */
    private String roomId;
    
    /**
     * Whether smoking room is requested
     */
    private Boolean smoking;
    
    /**
     * Whether loyalty ID applies
     */
    private Boolean loyaltyId;
    
    /**
     * Special request for the room
     */
    private String specialRequest;
    
    /**
     * List of guests for this room
     */
    private List<HotelPreBookGuest> guests;
}
