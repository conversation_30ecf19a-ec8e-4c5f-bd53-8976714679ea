package com.hb.crm.core.dtos.spectTrum.HotelGetBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Hotel cancellation policy information
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelCancellationPolicy {
    
    /**
     * Currency code
     */
    private String currency;
    
    /**
     * Cancellation start date/time
     */
    private String start;
    
    /**
     * Cancellation end date/time
     */
    private String end;
    
    /**
     * Cancellation amount
     */
    private String amount;
    
    /**
     * Number of nights penalty
     */
    private String nights;
    
    /**
     * Cancellation percentage
     */
    private String percent;
    
    /**
     * Cancellation policy description
     */
    private String description;
}
