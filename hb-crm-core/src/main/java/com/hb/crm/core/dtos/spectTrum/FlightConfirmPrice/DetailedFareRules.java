package com.hb.crm.core.dtos.spectTrum.FlightConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import java.util.Map;

/**
 * Detailed fare rules with dynamic properties
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DetailedFareRules {
    
    // Using Map to handle dynamic properties like additionalProp1, additionalProp2, etc.
    private Map<String, Object> fareRules;
}
