package com.hb.crm.core.dtos.spectTrum.FlightSearch;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class confirmPriceData {

    private ResonseData data;
    private String confirmPriceReferenceId;
    private  String confirmPriceReferenceExpiry;

    public String getConfirmPriceReferenceExpiry() {
        return confirmPriceReferenceExpiry;
    }

    public void setConfirmPriceReferenceExpiry(String confirmPriceReferenceExpiry) {
        this.confirmPriceReferenceExpiry = confirmPriceReferenceExpiry;
    }

    public ResonseData getData() {
        return data;
    }

    public String getConfirmPriceReferenceId() {
        return confirmPriceReferenceId;
    }

    public void setConfirmPriceReferenceId(String confirmPriceReferenceId) {
        this.confirmPriceReferenceId = confirmPriceReferenceId;
    }

    public void setData(ResonseData data) {
        this.data = data;
    }
}
