package com.hb.crm.core.dtos.spectTrum.FlightBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Airport information for booking
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BookingAirport {
    
    /**
     * Airport code
     */
    private String code;
    
    /**
     * Airport name/value
     */
    private String value;
    
    /**
     * City name
     */
    private String city;
    
    /**
     * State or province name
     */
    private String stateProvinceName;
    
    /**
     * Country name
     */
    private String country;
    
    /**
     * Country code
     */
    private String countryCode;
    
    /**
     * IATA code
     */
    private String iata;
    
    /**
     * ICAO code
     */
    private String icao;
    
    /**
     * Latitude coordinate
     */
    private Double latitude;
    
    /**
     * Longitude coordinate
     */
    private Double longitude;
}
