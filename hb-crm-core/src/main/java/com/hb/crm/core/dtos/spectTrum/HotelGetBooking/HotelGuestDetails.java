package com.hb.crm.core.dtos.spectTrum.HotelGetBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Hotel guest details information
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelGuestDetails {
    
    /**
     * Guest first name
     */
    private String firstName;
    
    /**
     * Guest last name
     */
    private String lastName;
    
    /**
     * Guest date of birth
     */
    private LocalDateTime dob;
    
    /**
     * Guest gender
     */
    private String gender;
    
    /**
     * Guest type
     */
    private String guestType;
    
    /**
     * Whether guest has children
     */
    private Boolean hasChildren;
    
    /**
     * Children's ages
     */
    private String childrensAge;
    
    /**
     * Guest reference documents
     */
    private List<HotelGuestDocument> guestRefDocuments;
}
