package com.hb.crm.core.dtos.spectTrum.HotelBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * Response DTO for Hotel Create Booking API from Spectrum Travel API
 * 
 * This DTO represents the response structure for the final hotel booking creation
 * containing booking confirmation details and status.
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelBookingResponse {
    
    /**
     * Indicates if the booking creation was successful
     */
    private Boolean success;
    
    /**
     * Status code of the booking response
     */
    private Integer statusCode;
    
    /**
     * Response message
     */
    private String message;
    
    /**
     * Main booking data containing confirmation details
     */
    private HotelBookingData data;
    
    /**
     * List of errors if the booking failed or had issues
     */
    private List<String> errors;
}
