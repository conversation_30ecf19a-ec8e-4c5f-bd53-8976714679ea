package com.hb.crm.core.dtos.spectTrum.FlightBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * Itinerary information for booking
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BookingItinerary {
    
    /**
     * Segment group identifier
     */
    private String segmentGroupId;
    
    /**
     * Stop over segments information
     */
    private String stopOverSegments;
    
    /**
     * List of flight segments
     */
    private List<BookingSegment> segments;
}
