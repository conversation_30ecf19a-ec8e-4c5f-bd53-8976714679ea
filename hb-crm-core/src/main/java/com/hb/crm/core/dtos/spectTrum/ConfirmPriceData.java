package com.hb.crm.core.dtos.spectTrum;

import com.hb.crm.core.beans.Flight.*;

import java.util.ArrayList;
import java.util.List;

public class ConfirmPriceData {
    private  String origin;
    private  String destination;
    private Boolean isRoundTrip=true;
     private  String type="flight-offers-pricing";

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

     private ArrayList<Currency> currencies;
    private  ArrayList<Location> locations;

    private  ArrayList <Aircraft> aircrafts;
    private  ArrayList <Carrier> carriers;

    public ArrayList<Location> getLocations() {
        return locations;
    }

    public void setLocations(ArrayList<Location> locations) {
        this.locations = locations;
    }



    public ArrayList<Currency> getCurrencies() {
        return currencies;
    }

    public void setCurrencies(ArrayList<Currency> currencies) {
        this.currencies = currencies;
    }

    private List<Flight> flightOffers;

    public List<Flight> getFlightOffers() {
        return flightOffers;
    }

    public void setFlightOffers(List<Flight> flightOffers) {
        this.flightOffers = flightOffers;
    }

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public Boolean getRoundTrip() {
        return isRoundTrip;
    }

    public void setRoundTrip(Boolean roundTrip) {
        isRoundTrip = roundTrip;
    }



    public ArrayList<Carrier> getCarriers() {
        return carriers;
    }

    public ArrayList<Aircraft> getAircrafts() {
        return aircrafts;
    }

    public void setAircrafts(ArrayList<Aircraft> aircrafts) {
        this.aircrafts = aircrafts;
    }

    public void setCarriers(ArrayList<Carrier> carriers) {
        this.carriers = carriers;
    }
}
