package com.hb.crm.core.dtos.spectTrum.HotelPreBook;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Hotel pre-book data containing booking reference and status information
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelPreBookData {
    
    /**
     * Trip identifier for the pre-booking
     */
    private String tripId;
    
    /**
     * Pre-booking status
     */
    private String preBookingStatus;
    
    /**
     * Booking reference identifier
     */
    private String bookingReferenceId;
    
    /**
     * Whether saved card is enabled for payment
     */
    private Boolean isSavedCardEnabled;
    
    /**
     * Whether payment is at property
     */
    private Boolean payAtProperty;
    
    /**
     * Source of the booking
     */
    private String source;
}
