package com.hb.crm.core.dtos.spectTrum.HotelAvailabilityResponse;

import lombok.Data;

/**
 * Deposit policies for room booking
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
public class DepositPolicies {
    
    /**
     * Number of nights for deposit
     */
    private String nights;
    
    /**
     * Whether remainder payment is required
     */
    private boolean remainder;
    
    /**
     * Percentage for deposit
     */
    private String percent;
    
    /**
     * Fixed amount for deposit
     */
    private String amount;
    
    /**
     * When deposit is due
     */
    private String due;
}
