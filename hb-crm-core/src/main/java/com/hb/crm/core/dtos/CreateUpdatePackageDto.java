package com.hb.crm.core.dtos;

import com.hb.crm.core.beans.FlightPackage.Airport;
import com.hb.crm.core.beans.Tag;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class CreateUpdatePackageDto {

    private String id;
    private String name;
    private String description;
    private List<RefranceModelDto> moods;
    private List<CreateMediaDto> medias;
    private List<Tag> tags;
    private boolean privateDate = false;

    private LocalDateTime start;
    private LocalDateTime end;
     private int capacity;

    private Airport fromAirport;
    private Airport toAirport;
    private boolean fromAirportInside;
    private boolean toAirportInside;




}
