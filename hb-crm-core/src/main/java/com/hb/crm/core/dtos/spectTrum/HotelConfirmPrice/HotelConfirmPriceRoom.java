package com.hb.crm.core.dtos.spectTrum.HotelConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * Room selection for hotel confirm price request
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelConfirmPriceRoom {
    
    /**
     * Room identifier
     */
    private String roomId;
    
    /**
     * List of selected room options
     */
    private List<String> options;
}
