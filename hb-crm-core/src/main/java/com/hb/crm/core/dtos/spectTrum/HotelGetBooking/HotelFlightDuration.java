package com.hb.crm.core.dtos.spectTrum.HotelGetBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Flight duration information for hotel package booking
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelFlightDuration {
    
    /**
     * Number of days
     */
    private Integer days;
    
    /**
     * Number of hours
     */
    private Integer hours;
    
    /**
     * Number of minutes
     */
    private Integer minutes;
}
