package com.hb.crm.core.dtos.spectTrum.HotelBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Request DTO for Hotel Create Booking API from Spectrum Travel API
 * 
 * This DTO represents the request structure for creating the final hotel booking
 * after pre-booking summary has been completed successfully.
 * 
 * API Endpoint: POST /api/v1/beta/booking
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelBookingRequest {
    
    /**
     * Agency identifier
     */
    private Integer agencyId;
    
    /**
     * Trip identifier from pre-booking response
     */
    private String tripId;
    
    /**
     * Booking reference identifier from pre-booking response
     */
    private String bookingReferenceId;
}
