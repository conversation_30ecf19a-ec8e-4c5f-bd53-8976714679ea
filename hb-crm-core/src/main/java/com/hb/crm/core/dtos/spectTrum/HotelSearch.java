package com.hb.crm.core.dtos.spectTrum;

import com.hb.crm.core.dtos.spectTrum.HotelResponse.Occupancy;
import lombok.Data;

import java.util.List;

@Data
public class HotelSearch {
    private String checkinDate;
    private String checkoutDate;
    private String longitude; // Changed to String to match API spec
    private String latitude; // Changed to String to match API spec
    private String searchRadius;
    private boolean refundable;
    private boolean isPackage;
    private String currency;
    private List<Occupancy> occupancies; // Updated to use proper Occupancy DTO
}
