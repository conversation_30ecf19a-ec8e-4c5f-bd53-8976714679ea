package com.hb.crm.core.dtos.spectTrum.HotelConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Hotel room option total pricing information
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelConfirmPriceTotalPrice {
    
    /**
     * Currency code
     */
    private String currency;
    
    /**
     * Gross price amount
     */
    private BigDecimal grossPrice;
    
    /**
     * Tax and fees amount
     */
    private BigDecimal taxAndFee;
    
    /**
     * Commission amount
     */
    private BigDecimal commission;
    
    /**
     * Amount due at property
     */
    private HotelConfirmPriceDueAtProperty dueAtProperty;
}
