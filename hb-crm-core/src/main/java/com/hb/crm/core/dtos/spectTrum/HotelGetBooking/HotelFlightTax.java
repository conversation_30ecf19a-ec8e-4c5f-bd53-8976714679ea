package com.hb.crm.core.dtos.spectTrum.HotelGetBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Flight tax information for hotel package booking
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelFlightTax {
    
    /**
     * Currency
     */
    private String currency;
    
    /**
     * Tax amount
     */
    private BigDecimal tax;
    
    /**
     * Tax code
     */
    private String code;
}
