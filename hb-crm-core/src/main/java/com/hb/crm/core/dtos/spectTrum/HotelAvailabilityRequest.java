package com.hb.crm.core.dtos.spectTrum;

import com.hb.crm.core.dtos.spectTrum.HotelResponse.Occupancy;
import lombok.Data;

import java.util.List;

/**
 * Request DTO for hotel availability check via Spectrum Travel API
 * 
 * This DTO represents the request structure for checking hotel availability
 * including property details, dates, and occupancy information.
 * 
 * API Endpoint: POST /api/v1/beta/availability/agencyId={agencyId}
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
public class HotelAvailabilityRequest 
{
    
    /**
     * The unique identifier of the property/hotel
     */
    private String propertyId;
    
    /**
     * Check-in date in string format (typically YYYY-MM-DD)
     */
    private String checkinDate;
    
    /**
     * Check-out date in string format (typically YYYY-MM-DD)
     */
    private String checkoutDate;
    
    /**
     * Currency code for pricing (e.g., "USD", "EUR")
     */
    private String currency;
    
    /**
     * Whether the booking should be refundable
     */
    private boolean isRefundable;
    
    /**
     * Whether this is a package booking
     */
    private boolean isPackage;
    
    /**
     * List of occupancy information including adults and children
     */
    private List<Occupancy> occupancies;
}
