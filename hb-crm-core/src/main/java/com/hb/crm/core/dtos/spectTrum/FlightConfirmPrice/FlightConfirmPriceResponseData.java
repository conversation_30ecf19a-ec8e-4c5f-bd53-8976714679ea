package com.hb.crm.core.dtos.spectTrum.FlightConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Data section of Flight Confirm Price response
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FlightConfirmPriceResponseData {
    
    private String confirmPriceReferenceId;
    private LocalDateTime confirmPriceReferenceExpiry;
    private Boolean isPaymentRequired;
    private ConfirmedFlightData data;
    private IncludedServices included;
    private List<String> errors;
}
