package com.hb.crm.core.services.interfaces;

import com.hb.crm.core.Enums.CrmNotificationChannelTypes;
import com.hb.crm.core.beans.Notification.CrmNotification;

import com.hb.crm.core.dtos.notification.CreateCrmNotificationDto;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;



@Service
public interface CrmNotificationService {


    /**
     * Create new notification entry
     *
     * @param notification
     * @return the new notification
     *
     * @see CrmNotification
     */
    public CrmNotification createNotification(CreateCrmNotificationDto notification);

    /**
     * Sends a notification through the appropriate channel and persists it.
     *
     * @param notification the notification to be sent
     * @return the sent notification with updated timestamps
     * @throws IllegalStateException if the notification channel is not supported
     */
    public CrmNotification sendNotification(CrmNotification notification);


    /**
     * Retrieves a single notification by its unique identifier.
     *
     * @param id the unique identifier of the notification
     * @return the found notification
     * @throws RuntimeException if the notification is not found
     */
    public CrmNotification getNotificationItem(String id);

    /**
     * Updates the read status of a notification.
     *
     * @param read the new read status (true for read, false for unread)
     * @param id the unique identifier of the notification to update
     * @return the updated notification
     * @throws RuntimeException if the notification is not found
     */
    public CrmNotification setReadStatus(boolean read, String id);


    /**
     * Filters notifications for administrative purposes with pagination support.
     *
     * @param searchText text to search in notification title or body (case-insensitive)
     * @param fromDate start date for filtering by send date (inclusive)
     * @param toDate end date for filtering by send date (inclusive)
     * @param read read status filter (true for read, false for unread, null for all)
     * @param type notification channel type filter
     * @param pageNumber the page number (0-based)
     * @param pageSize the number of items per page
     * @return paginated list of filtered notifications
     * @throws RuntimeException if the notification channel type is invalid
     */
    public List<CrmNotification> filterAdminNotifications(String searchText,
                                                          LocalDateTime fromDate,
                                                          LocalDateTime toDate,
                                                          Boolean read,
                                                          CrmNotificationChannelTypes type,
                                                          int pageNumber,
                                                          int pageSize);


    /**
     * Filters and retrieves paginated notifications based on multiple criteria for client application.
     *
     * <p>This method allows complex filtering of notifications by combining multiple optional filters including:
     * <ul>
     *   <li>Text search in title or body (case-insensitive)</li>
     *   <li>Date range filtering</li>
     *   <li>Read/unread status</li>
     *   <li>Specific user</li>
     *   <li>Notification channel type</li>
     * </ul>
     * <p>
     * Results are always sorted by send date in descending order (newest first) and paginated.
     *
     * @param searchText Optional text to search in notification title or body (case-insensitive regex match).
     *                   If provided, searches both title and body fields.
     * @param fromDate   Optional start date for filtering notifications (inclusive).
     *                   If provided without toDate, filters notifications sent after this date.
     * @param toDate     Optional end date for filtering notifications (inclusive).
     *                   If provided without fromDate, filters notifications sent before this date.
     * @param read       Optional read status filter (true for read, false for unread).
     *                   If null, read status is not considered in filtering.
     * @param user       Optional user filter. If provided, only returns notifications for this specific user.
     * @param type       Optional notification channel type filter. If provided, validates the channel exists
     *                   and filters by channel type.
     * @param pageNumber Zero-based page number for pagination (must be >= 0).
     * @param pageSize   Number of notifications per page (must be >= 1).
     * @return List of notifications matching all provided criteria, sorted by sendDate (newest first)
     * and paginated according to pageNumber and pageSize.
     * @throws RuntimeException if an invalid notification channel type is provided (when type is non-null
     *                          and no matching channel exists in the repository).
     * @see CrmNotification
     * @see CrmNotificationChannelTypes
     */
    public List<CrmNotification> filterUserNotification(String searchText,
                                                        LocalDateTime fromDate,
                                                        LocalDateTime toDate,
                                                        Boolean read,
                                                        String username,
                                                        CrmNotificationChannelTypes type,
                                                        int pageNumber,
                                                        int pageSize);

    public List<CrmNotification> seed();


    /**
     * Delete notification by id
     * @param id
     * @return
     */
    public void delete(String id);

}
