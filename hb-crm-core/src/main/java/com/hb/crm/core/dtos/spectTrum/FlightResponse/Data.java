package com.hb.crm.core.dtos.spectTrum.FlightResponse;

import java.util.ArrayList;
@lombok.Data
public class Data {
    private String searchIdentifier;
    private String type;
    private String id;
    private boolean instantTicketingRequired;
    private boolean nonHomogeneous;
    private boolean oneWay;
    private boolean paymentCardRequired;
    private String lastTicketingDate;
    private String flightReferenceId;
    private ArrayList<Itinerary> itineraries;
    private Price price;
    private PricingOptions pricingOptions;
    private ArrayList<String> validatingAirlineCodes;
    private ArrayList<TravelerPricing> travelerPricings;
    private FareRules fareRules;
    private ArrayList<Object> travelDocuments;
    private boolean brandedFare;
}
