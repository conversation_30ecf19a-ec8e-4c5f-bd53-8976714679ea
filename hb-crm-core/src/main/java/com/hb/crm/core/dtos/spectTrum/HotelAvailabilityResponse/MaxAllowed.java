package com.hb.crm.core.dtos.spectTrum.HotelAvailabilityResponse;

import lombok.Data;

/**
 * Maximum allowed occupancy for a room
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
public class MaxAllowed {
    
    /**
     * Maximum number of adults allowed
     */
    private int adult;
    
    /**
     * Maximum number of children allowed
     */
    private int children;
    
    /**
     * Maximum total occupancy
     */
    private int total;
}
