package com.hb.crm.core.dtos.spectTrum.HotelGetBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Detailed hotel booking information
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelBookingDetails {
    
    /**
     * Trip identifier
     */
    private String tripId;
    
    /**
     * Supplier reference number
     */
    private String supplierRefNumber;
    
    /**
     * Hotel name
     */
    private String hotelName;
    
    /**
     * Hotel latitude coordinate
     */
    private Double latitude;
    
    /**
     * Hotel longitude coordinate
     */
    private Double longitude;
    
    /**
     * Hotel rating
     */
    private Integer rating;
    
    /**
     * Travel and technology reference number
     */
    private String ttRefNumber;
    
    /**
     * Additional supplier reference
     */
    private String additionalSupplierReference;
    
    /**
     * Check-in date
     */
    private String checkInDate;
    
    /**
     * Check-in time
     */
    private String checkInTime;
    
    /**
     * Check-out date
     */
    private String checkOutDate;
    
    /**
     * Check-out time
     */
    private String checkOutTime;
    
    /**
     * Check-in instructions
     */
    private List<String> checkInInstructions;
    
    /**
     * Number of rooms booked
     */
    private Integer numberOfRooms;
    
    /**
     * Number of nights
     */
    private Integer numberOfNights;
    
    /**
     * Customer profile identifier
     */
    private String customerProfileId;
    
    /**
     * Content reference identifier
     */
    private String contentRefId;
    
    /**
     * Property identifier
     */
    private String propertyId;
    
    /**
     * Booking creation date
     */
    private LocalDateTime createdOn;
    
    /**
     * Booking status
     */
    private String status;
    
    /**
     * Booking modification date
     */
    private LocalDateTime modifiedOn;
    
    /**
     * Booking created by
     */
    private String createdBy;
    
    /**
     * Booking modified by
     */
    private String modifiedBy;
    
    /**
     * Hotel address line 1
     */
    private String address1;
    
    /**
     * Hotel address line 2
     */
    private String address2;
    
    /**
     * Hotel address line 3
     */
    private String address3;
    
    /**
     * City name
     */
    private String cityName;
    
    /**
     * Postal code
     */
    private String postalCode;
    
    /**
     * Country name
     */
    private String countryName;
    
    /**
     * City code
     */
    private String cityCode;
    
    /**
     * State name
     */
    private String stateName;
    
    /**
     * Country code
     */
    private String countryCode;
    
    /**
     * Fax number
     */
    private String fax;
    
    /**
     * Phone number
     */
    private String phoneNumber;
    
    /**
     * Mobile number
     */
    private String mobileNumber;
    
    /**
     * Hotel phone number
     */
    private String hotelPhoneNumber;
    
    /**
     * Email address
     */
    private String emailId;
    
    /**
     * Detailed room information
     */
    private List<HotelRoomDetails> roomDetails;
    
    /**
     * Hotel images
     */
    private List<HotelBookingImage> images;
    
    /**
     * Whether to hide supplier terms and conditions
     */
    private Boolean hideSupplierTermsAndConditions;
    
    /**
     * Supplier terms and conditions URI
     */
    private String supplierTermsAndConditionsUri;
    
    /**
     * Whether supports multi-room cancellation
     */
    private Boolean supportsMuliRoomCancel;
    
    /**
     * Payment remarks
     */
    private String paymentRemarks;
    
    /**
     * Currency code
     */
    private String currency;
    
    /**
     * Gross price amount
     */
    private BigDecimal grossPrice;
    
    /**
     * Tax and fees amount
     */
    private BigDecimal taxAndFee;
    
    /**
     * Commission amount
     */
    private BigDecimal commission;
}
