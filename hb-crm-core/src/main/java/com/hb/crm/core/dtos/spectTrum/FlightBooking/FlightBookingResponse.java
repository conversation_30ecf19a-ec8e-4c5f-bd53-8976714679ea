package com.hb.crm.core.dtos.spectTrum.FlightBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * Response DTO for Flight Booking API from Spectrum Travel API
 * 
 * This DTO represents the response structure after successfully creating a flight booking.
 * Contains booking confirmation details, trip information, and any errors.
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FlightBookingResponse {
    
    /**
     * Indicates if the booking request was successful
     */
    private Boolean success;
    
    /**
     * Status code of the booking response
     */
    private Integer statusCode;
    
    /**
     * Response message providing booking status information
     */
    private String message;
    
    /**
     * Main booking data containing flight and traveler information
     */
    private FlightBookingResponseData data;
    
    /**
     * List of errors if the booking failed or had issues
     */
    private List<String> errors;
}
