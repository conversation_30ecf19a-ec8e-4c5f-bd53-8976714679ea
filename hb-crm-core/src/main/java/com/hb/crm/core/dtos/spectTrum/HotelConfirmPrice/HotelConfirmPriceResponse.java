package com.hb.crm.core.dtos.spectTrum.HotelConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * Response DTO for Hotel Confirm Price API from Spectrum Travel API
 * 
 * This DTO represents the response structure for hotel price confirmation
 * containing detailed property information and confirmed pricing.
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelConfirmPriceResponse {
    
    /**
     * Indicates if the price confirmation was successful
     */
    private Boolean success;
    
    /**
     * Status code of the confirmation response
     */
    private Integer statusCode;
    
    /**
     * Response message
     */
    private String message;
    
    /**
     * Main hotel confirmation data containing property and pricing details
     */
    private HotelConfirmPriceData data;
    
    /**
     * List of errors if the confirmation failed or had issues
     */
    private List<String> errors;
}
