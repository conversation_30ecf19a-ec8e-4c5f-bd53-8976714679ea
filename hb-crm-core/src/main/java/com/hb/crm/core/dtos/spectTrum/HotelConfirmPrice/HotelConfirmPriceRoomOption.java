package com.hb.crm.core.dtos.spectTrum.HotelConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * Hotel room option with pricing and policies
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelConfirmPriceRoomOption {
    
    /**
     * Option identifier
     */
    private String id;
    
    /**
     * Option description
     */
    private String description;
    
    /**
     * Option amenities
     */
    private List<String> amenities;
    
    /**
     * Whether the option is refundable
     */
    private Boolean refundable;
    
    /**
     * Whether this is a package option
     */
    @JsonProperty("package")
    private Boolean packageOption;
    
    /**
     * Cancellation penalties
     */
    private List<HotelConfirmPriceCancelPenalty> cancelPenalties;
    
    /**
     * Total pricing information
     */
    private HotelConfirmPriceTotalPrice totalPrice;
    
    /**
     * Occupancy-based pricing
     */
    private List<HotelConfirmPriceOccupancyPricing> occupancyPricing;
    
    /**
     * Available payment options
     */
    private List<String> paymentOptions;
    
    /**
     * Deposit policies
     */
    private HotelConfirmPriceDepositPolicies depositPolicies;
    
    /**
     * Option comments
     */
    private String optionComment;
    
    /**
     * Whether payment is at property
     */
    private Boolean payAtProperty;
    
    /**
     * Number of available rooms
     */
    private Integer availableRooms;
}
