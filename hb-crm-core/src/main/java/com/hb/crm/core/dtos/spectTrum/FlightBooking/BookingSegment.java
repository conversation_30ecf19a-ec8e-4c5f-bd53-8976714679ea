package com.hb.crm.core.dtos.spectTrum.FlightBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Flight segment information for booking
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BookingSegment {
    
    /**
     * Whether this is a return segment
     */
    private Boolean isReturn;
    
    /**
     * Origin location code
     */
    private String origin;
    
    /**
     * Origin airport details
     */
    private BookingAirport originAirport;
    
    /**
     * Destination location code
     */
    private String destination;
    
    /**
     * Destination airport details
     */
    private BookingAirport destinationAirport;
    
    /**
     * Departure date and time
     */
    private LocalDateTime departureDateTime;
    
    /**
     * Arrival date and time
     */
    private LocalDateTime arrivalDateTime;
    
    /**
     * Flight number
     */
    private String flightNumber;
    
    /**
     * Departure terminal
     */
    private String departureTerminal;
    
    /**
     * Arrival terminal
     */
    private String arrivalTerminal;
    
    /**
     * Operating airline code
     */
    private String operatingAirline;
    
    /**
     * Marketing airline code
     */
    private String marketingAirline;
    
    /**
     * Aircraft equipment type
     */
    private String equipmentType;
    
    /**
     * Airline passenger name record
     */
    private String airlinePNR;
    
    /**
     * Booking class
     */
    private String bookingClass;
    
    /**
     * Cabin class
     */
    private String cabinClass;
    
    /**
     * Segment status
     */
    private String status;
    
    /**
     * Journey duration in minutes
     */
    private Integer journeyDuration;
    
    /**
     * Flight duration details
     */
    private BookingDuration duration;
    
    /**
     * Layover duration details
     */
    private BookingDuration layoverDuration;
    
    /**
     * Number of stops
     */
    private Integer stopQuantity;
}
