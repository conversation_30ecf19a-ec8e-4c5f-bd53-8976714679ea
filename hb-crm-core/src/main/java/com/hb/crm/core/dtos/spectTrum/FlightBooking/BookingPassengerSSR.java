package com.hb.crm.core.dtos.spectTrum.FlightBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Special Service Request information for passenger
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BookingPassengerSSR {
    
    /**
     * Segment group identifier
     */
    private String segmentGroupId;
    
    /**
     * SSR type identifier
     */
    private Integer ssrTypeId;
    
    /**
     * SSR type name
     */
    private String ssrType;
    
    /**
     * SSR value
     */
    private BigDecimal ssrValue;
    
    /**
     * Unit type identifier
     */
    private Integer unitTypeId;
    
    /**
     * Unit type name
     */
    private String unitType;
    
    /**
     * Quantity requested
     */
    private Integer quantity;
    
    /**
     * Currency code
     */
    private String currency;
    
    /**
     * Price for the service
     */
    private BigDecimal price;
    
    /**
     * Description of the service
     */
    private String description;
}
