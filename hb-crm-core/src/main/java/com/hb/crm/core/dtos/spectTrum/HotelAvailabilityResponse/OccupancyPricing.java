package com.hb.crm.core.dtos.spectTrum.HotelAvailabilityResponse;

import lombok.Data;

import java.util.List;

/**
 * Occupancy pricing breakdown
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
public class OccupancyPricing {
    
    /**
     * Occupancy details this pricing is for
     */
    private OccupancyFor forDetails;
    
    /**
     * Nightly rate breakdown
     */
    private List<NightlyRate> nightly;
    
    /**
     * Pay at property information
     */
    private PayAtProperty payAtProperty;
    
    /**
     * Total pricing breakdown
     */
    private OccupancyTotal total;
    
    /**
     * Nightly average rate
     */
    private double nightlyAverage;
}
