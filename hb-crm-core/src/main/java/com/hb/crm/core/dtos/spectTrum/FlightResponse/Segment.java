package com.hb.crm.core.dtos.spectTrum.FlightResponse;

import lombok.Data;

import java.util.ArrayList;
@Data
public class Segment {
    private String duration;
    private SegmentDurationInTime segmentDurationInTime;
    private Departure departure;
    private Arrival arrival;
    private String carrierCode;
    private String number;
    private Aircraft aircraft;
    private Operating operating;
    private String id;
    private int numberOfStops;
    private boolean blacklistedInEU;
    private ArrayList<Object> co2Emissions;
}
