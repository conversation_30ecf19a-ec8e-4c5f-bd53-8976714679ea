package com.hb.crm.core.dtos.spectTrum;

public class confermPriceRequst {
    private ConfirmPriceData data;
    private  String upgradeCabin;

    private   String include="bags,other-services,detailed-fare-rules";
    private   boolean forceClass=false;

    public ConfirmPriceData getData() {
        return data;
    }

    public void setData(ConfirmPriceData data) {
        this.data = data;
    }

    public String getInclude() {
        return include;
    }

    public void setInclude(String include) {
        this.include = include;
    }

    public boolean isForceClass() {
        return forceClass;
    }

    public void setForceClass(boolean forceClass) {
        this.forceClass = forceClass;
    }

    public String getUpgradeCabin() {
        return upgradeCabin;
    }

    public void setUpgradeCabin(String upgradeCabin) {
        this.upgradeCabin = upgradeCabin;
    }
}
