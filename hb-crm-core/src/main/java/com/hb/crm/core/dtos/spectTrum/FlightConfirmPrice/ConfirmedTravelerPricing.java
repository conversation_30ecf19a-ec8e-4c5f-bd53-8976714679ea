package com.hb.crm.core.dtos.spectTrum.FlightConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.hb.crm.core.beans.Flight.FareDetailsBySegment;
import lombok.Data;
import java.util.List;

/**
 * Confirmed traveler pricing with updated fare details
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConfirmedTravelerPricing {
    
    private String travelerId;
    private String fareOption;
    private String travelerType;
    private String associatedAdultId;
    private ConfirmedTravelerPrice price;
    private List<FareDetailsBySegment> fareDetailsBySegment;
}
