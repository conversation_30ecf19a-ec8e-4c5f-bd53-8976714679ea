package com.hb.crm.core.dtos.spectTrum.FlightBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.hb.crm.core.beans.Travelers.Traveler;
import lombok.Data;

import java.util.List;

/**
 * Request DTO for Flight Booking via Spectrum Travel API
 * 
 * This DTO represents the request structure for creating a flight booking
 * after price confirmation has been completed.
 * 
 * API Endpoint: POST /api/v1/beta/book?agencyId={agencyId}
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FlightBookingRequest {
    
    /**
     * The confirm price reference ID obtained from the confirm price API
     * This ID locks the price and flight availability for a limited time
     */
    private String confirmPriceReferenceId;
    
    /**
     * List of travelers with complete information including:
     * - Personal details (name, date of birth, gender)
     * - Contact information (email, phone, address)
     * - Travel documents (passport details, etc.)
     */
    private List<Traveler> travelers;
}
