package com.hb.crm.core.dtos.spectTrum.FlightBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * Flight booking response data containing the actual booking details
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FlightBookingResponseData {
    
    /**
     * The nested booking data with detailed flight and traveler information
     */
    private FlightBookingData data;
    
    /**
     * List of errors specific to the booking data
     */
    private List<String> errors;
}
