package com.hb.crm.core.dtos.spectTrum.HotelAvailabilityResponse;

import com.hb.crm.core.dtos.spectTrum.HotelResponse.Amenity;
import com.hb.crm.core.dtos.spectTrum.HotelResponse.Image;
import lombok.Data;

import java.util.List;

/**
 * Main data object for hotel availability response
 * 
 * Contains all hotel information, rooms, and availability details
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
public class HotelAvailabilityData {
    
    /**
     * Property/hotel ID
     */
    private String propertyId;
    
    /**
     * Whether price has changed since search
     */
    private boolean isPriceChanged;
    
    /**
     * Whether multiple rate booking is supported
     */
    private boolean supportsMulipleRateBooking;
    
    /**
     * Whether check rate is supported
     */
    private boolean supportsCheckRate;
    
    /**
     * Property reference ID
     */
    private String propertyReferenceId;
    
    /**
     * Hotel name
     */
    private String name;
    
    /**
     * Hotel ratings
     */
    private Ratings ratings;
    
    /**
     * Hotel category
     */
    private String category;
    
    /**
     * Check-in information and policies
     */
    private CheckIn checkin;
    
    /**
     * Check-out information
     */
    private CheckOut checkout;
    
    /**
     * Hotel policies
     */
    private List<String> policies;
    
    /**
     * Hotel attributes
     */
    private List<Attribute> attributes;
    
    /**
     * Hotel amenities
     */
    private List<Amenity> amenities;
    
    /**
     * Available rooms and options
     */
    private List<Room> rooms;
    
    /**
     * Hotel descriptions
     */
    private List<Description> descriptions;
    
    /**
     * Nearby airports
     */
    private List<String> airports;
    
    /**
     * Hotel images
     */
    private List<Image> images;
    
    /**
     * Hotel address
     */
    private Address address;
    
    /**
     * Sales type
     */
    private String salesType;
    
    /**
     * Data source
     */
    private String source;
    
    /**
     * Whether all passenger details are required
     */
    private boolean requiresAllPaxesDetails;
    
    /**
     * Free cancellation indicator
     */
    private boolean freeCancellationInd;
    
    /**
     * Pay at property indicator
     */
    private boolean payAtPropertyInd;
}
