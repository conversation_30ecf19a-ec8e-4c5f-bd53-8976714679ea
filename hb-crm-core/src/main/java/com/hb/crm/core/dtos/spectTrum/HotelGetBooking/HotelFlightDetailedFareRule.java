package com.hb.crm.core.dtos.spectTrum.HotelGetBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Detailed fare rule for flight in hotel package booking
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelFlightDetailedFareRule {
    
    /**
     * Fare rule category
     */
    private String category;
    
    /**
     * Fare rule description
     */
    private String description;
}
