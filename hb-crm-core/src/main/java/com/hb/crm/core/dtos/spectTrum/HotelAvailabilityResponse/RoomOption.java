package com.hb.crm.core.dtos.spectTrum.HotelAvailabilityResponse;

import lombok.Data;

import java.util.List;

/**
 * Room booking option with pricing and policies
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
public class RoomOption {
    
    /**
     * Option ID
     */
    private String id;
    
    /**
     * Option description
     */
    private String description;
    
    /**
     * List of amenities for this option
     */
    private List<String> amenities;
    
    /**
     * Whether this option is refundable
     */
    private boolean refundable;
    
    /**
     * Whether this is a package option
     */
    private boolean packageOption;
    
    /**
     * Cancellation penalties
     */
    private List<CancelPenalty> cancelPenalties;
    
    /**
     * Total cost breakdown
     */
    private TotalCost totalCost;
    
    /**
     * Commission breakdown
     */
    private CommissionBreakup commissionBreakup;
    
    /**
     * Occupancy pricing details
     */
    private List<OccupancyPricing> occupancyPricing;
    
    /**
     * Payment options available
     */
    private List<String> paymentOptions;
    
    /**
     * Deposit policies
     */
    private DepositPolicies depositPolicies;
    
    /**
     * Option comment
     */
    private String optionComment;
    
    /**
     * Whether payment can be made at property
     */
    private boolean payAtProperty;
    
    /**
     * Number of available rooms for this option
     */
    private int availableRooms;
    
    /**
     * Pay later option details
     */
    private PayLater payLater;
}
