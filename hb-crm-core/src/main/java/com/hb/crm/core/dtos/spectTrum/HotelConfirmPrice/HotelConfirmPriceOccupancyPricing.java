package com.hb.crm.core.dtos.spectTrum.HotelConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Hotel occupancy-based pricing information
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelConfirmPriceOccupancyPricing {
    
    /**
     * Occupancy details for which this pricing applies
     */
    @JsonProperty("for")
    private HotelConfirmPriceOccupancyFor forOccupancy;
}
