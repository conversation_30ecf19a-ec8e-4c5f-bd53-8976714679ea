package com.hb.crm.core.dtos.LiveStream;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

@Data
public class LiveStreamForceStopRequestDto {
    
    @NotBlank(message = "Stream ID is required")
    private String streamId;
    
    @NotBlank(message = "Admin ID is required")
    private String adminId;
    
    @NotBlank(message = "Admin Token is required")
    private String adminToken;
    
    @Size(max = 500, message = "Reason must not exceed 500 characters")
    private String reason;
}
