package com.hb.crm.core.dtos.spectTrum.HotelConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * Hotel image information
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelConfirmPriceImage {
    
    /**
     * Image caption
     */
    private String caption;
    
    /**
     * Image links
     */
    private List<HotelConfirmPriceLink> links;
}
