package com.hb.crm.core.dtos.spectTrum.HotelGetBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Detailed hotel room booking information
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelRoomDetails {
    
    /**
     * Room images
     */
    private List<HotelBookingImage> images;
    
    /**
     * Room identifier
     */
    private String roomId;
    
    /**
     * Board name (meal plan)
     */
    private String boardName;
    
    /**
     * Room status
     */
    private String status;
    
    /**
     * Room name
     */
    private String roomName;
    
    /**
     * Special requests for the room
     */
    private String specialRequest;
    
    /**
     * Pricing type
     */
    private String pricingType;
    
    /**
     * Number of adults in the room
     */
    private Integer numberOfAdults;
    
    /**
     * Number of children in the room
     */
    private Integer numberOfChilds;
    
    /**
     * Cancellation policies
     */
    private List<HotelCancellationPolicy> cancellationPolicies;
    
    /**
     * Deposit policies
     */
    private List<HotelDepositPolicy> depositePolicies;
    
    /**
     * Whether the booking is refundable
     */
    private Boolean refundable;
    
    /**
     * Guest details for this room
     */
    private List<HotelGuestDetails> guestDetails;
    
    /**
     * Whether loyalty ID applies
     */
    private Boolean loyaltyId;
    
    /**
     * Amount due at property
     */
    private HotelDueAtProperty dueAtProperty;
    
    /**
     * Room comments
     */
    private String roomComments;
    
    /**
     * Cancellation price
     */
    private BigDecimal cancellationPrice;
    
    /**
     * Refundable amount
     */
    private BigDecimal refundableAmount;
    
    /**
     * Currency code
     */
    private String currency;
    
    /**
     * Gross price
     */
    private BigDecimal grossPrice;
    
    /**
     * Tax and fees
     */
    private BigDecimal taxAndFee;
    
    /**
     * Commission amount
     */
    private BigDecimal commission;
}
