package com.hb.crm.core.dtos.spectTrum.HotelAvailabilityResponse;

import lombok.Data;

/**
 * Cancellation penalty information
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
public class CancelPenalty {
    
    /**
     * Currency for the penalty
     */
    private String currency;
    
    /**
     * Start date/time for penalty period
     */
    private String start;
    
    /**
     * End date/time for penalty period
     */
    private String end;
    
    /**
     * Fixed penalty amount
     */
    private String amount;
    
    /**
     * Number of nights penalty
     */
    private String nights;
    
    /**
     * Percentage penalty
     */
    private String percent;
    
    /**
     * Description of the penalty
     */
    private String description;
}
