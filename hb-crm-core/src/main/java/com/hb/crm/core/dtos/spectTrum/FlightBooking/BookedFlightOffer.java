package com.hb.crm.core.dtos.spectTrum.FlightBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.hb.crm.core.beans.Flight.FareRules;
import com.hb.crm.core.beans.Flight.Itinerary;
import com.hb.crm.core.beans.Flight.Price;
import com.hb.crm.core.beans.Flight.PricingOptions;
import com.hb.crm.core.beans.Flight.TravelerPricing;
import com.hb.crm.core.dtos.spectTrum.FlightConfirmPrice.DetailedFareRules;
import lombok.Data;

import java.util.List;

/**
 * Booked flight offer with detailed booking information
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BookedFlightOffer {
    
    /**
     * Supplier identifier for this flight offer
     */
    private String supplierId;
    
    /**
     * Search identifier used to find this flight
     */
    private String searchIdentifier;
    
    /**
     * Type of flight offer
     */
    private String type;
    
    /**
     * Unique identifier for this flight offer
     */
    private String id;
    
    /**
     * Source of the flight offer
     */
    private String source;
    
    /**
     * Whether instant ticketing is required
     */
    private Boolean instantTicketingRequired;
    
    /**
     * Whether this is an international flight
     */
    private Boolean isInternational;
    
    /**
     * Whether the flight segments are non-homogeneous
     */
    private Boolean nonHomogeneous;
    
    /**
     * Whether this is a one-way flight
     */
    private Boolean oneWay;
    
    /**
     * Whether a payment card is required
     */
    private Boolean paymentCardRequired;
    
    /**
     * Last date for ticketing this flight
     */
    private String lastTicketingDate;
    
    /**
     * Date when the fare expires
     */
    private String fareExpiryDate;
    
    /**
     * Flight reference identifier
     */
    private String flightReferenceId;
    
    /**
     * Source of the supplier
     */
    private String supplierSource;
    
    /**
     * List of flight itineraries
     */
    private List<Itinerary> itineraries;
    
    /**
     * Pricing information for the flight
     */
    private Price price;
    
    /**
     * Pricing options and restrictions
     */
    private PricingOptions pricingOptions;
    
    /**
     * List of validating airline codes
     */
    private List<String> validatingAirlineCodes;
    
    /**
     * Pricing details for each traveler
     */
    private List<TravelerPricing> travelerPricings;
    
    /**
     * Basic fare rules
     */
    private FareRules fareRules;
    
    /**
     * Detailed fare rules with complete information
     */
    private DetailedFareRules detailedFareRules;
    
    /**
     * Travel and technology reference
     */
    private String ttReference;
}
