package com.hb.crm.core.dtos.spectTrum.FlightBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Cancellation data containing cancellation status and details
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CancelBookingData {
    
    /**
     * Status of the cancellation operation
     * true if cancellation was successful, false otherwise
     */
    private Boolean status;
    
    /**
     * Error code if the cancellation failed
     */
    private String errorCode;
    
    /**
     * Detailed message about the cancellation status
     */
    private String message;
}
