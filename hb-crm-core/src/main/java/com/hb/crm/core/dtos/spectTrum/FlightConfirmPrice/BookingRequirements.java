package com.hb.crm.core.dtos.spectTrum.FlightConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import java.util.List;

/**
 * Booking requirements for flight confirmation
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BookingRequirements {
    
    private Boolean invoiceAddressRequired;
    private Boolean mailingAddressRequired;
    private Boolean emailAddressRequired;
    private Boolean phoneCountryCodeRequired;
    private Boolean mobilePhoneNumberRequired;
    private Boolean phoneNumberRequired;
    private Boolean postalCodeRequired;
    private List<TravelerRequirements> travelerRequirements;
}
