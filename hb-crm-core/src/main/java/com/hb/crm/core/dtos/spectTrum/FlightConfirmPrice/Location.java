package com.hb.crm.core.dtos.spectTrum.FlightConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Location information for airports, cities, etc.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Location {
    
    private String code;
    private String value;
    private String city;
    private String stateProvinceName;
    private String country;
    private String countryCode;
    private String iata;
    private String icao;
    private Double latitude;
    private Double longitude;
}
