package com.hb.crm.core.dtos.spectTrum.FlightBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * Response DTO for Get Booking API from Spectrum Travel API
 * 
 * This DTO represents the response structure for retrieving booking details by tripId.
 * 
 * API Endpoint: GET /api/v1/beta/book/{tripId}?agencyId={agencyId}
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetBookingResponse {
    
    /**
     * Indicates if the request was successful
     */
    private Boolean success;
    
    /**
     * Status code of the response
     */
    private Integer statusCode;
    
    /**
     * Response message
     */
    private String message;
    
    /**
     * Main booking data containing detailed booking information
     */
    private GetBookingData data;
    
    /**
     * List of errors if the request failed or had issues
     */
    private List<String> errors;
}
