package com.hb.crm.core.dtos.spectTrum.FlightBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Passenger fare information for booking
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BookingPassengerFare {
    
    /**
     * Type of fare
     */
    private String fareType;
    
    /**
     * Currency code
     */
    private String currency;
    
    /**
     * Base fare amount
     */
    private BigDecimal baseFare;
    
    /**
     * Base currency code
     */
    private String basecurrency;
    
    /**
     * Total tax amount
     */
    private BigDecimal tax;
    
    /**
     * List of individual taxes
     */
    private List<BookingTax> taxes;
    
    /**
     * Fare basis code
     */
    private String fareBasisCode;
    
    /**
     * Total fare amount
     */
    private BigDecimal total;
}
