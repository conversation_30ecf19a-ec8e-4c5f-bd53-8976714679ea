package com.hb.crm.core.dtos.spectTrum.FlightBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Detailed fare rule information for booking
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BookingDetailedFareRule {
    
    /**
     * Fare rule category
     */
    private String category;
    
    /**
     * Detailed description of the fare rule
     */
    private String description;
}
