package com.hb.crm.core.dtos.spectTrum.FlightBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Passenger information for booking
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BookingPassenger {
    
    /**
     * Passenger title (Mr., Ms., etc.)
     */
    private String title;
    
    /**
     * First name
     */
    private String firstName;
    
    /**
     * Middle name
     */
    private String middleName;
    
    /**
     * Last name
     */
    private String lastName;
    
    /**
     * Gender
     */
    private String gender;
    
    /**
     * Email address
     */
    private String email;
    
    /**
     * Date of birth
     */
    private LocalDateTime dob;
    
    /**
     * Passenger type (Adult, Child, Infant)
     */
    private String paxType;
    
    /**
     * Mobile phone number
     */
    private String mobile;
    
    /**
     * Passport number
     */
    private String passportNumber;
    
    /**
     * Passport nationality
     */
    private String passportNationality;
    
    /**
     * Passport date of issuance
     */
    private String passportDOI;
    
    /**
     * Passport date of expiry
     */
    private String passportDOE;
    
    /**
     * Frequent flyer number
     */
    private String ffNumber;
    
    /**
     * List of frequent flyer numbers
     */
    private List<String> ffNumbers;
    
    /**
     * List of passenger fares
     */
    private List<BookingPassengerFare> fares;
    
    /**
     * List of special service requests
     */
    private List<BookingPassengerSSR> ssrs;
    
    /**
     * List of passenger tickets
     */
    private List<BookingPassengerTicket> tickets;
}
