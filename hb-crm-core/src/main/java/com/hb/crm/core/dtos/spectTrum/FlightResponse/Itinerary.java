package com.hb.crm.core.dtos.spectTrum.FlightResponse;

import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
@Data
public class Itinerary {
    private int segmentGroupId;
    private String flightFrom;
    private String flightTo;
    private String duration;
    private ArrayList<Segment> segments;
    private DepartureAirport departureAirport;
    private ArrivalAirport arrivalAirport;
    private ArrayList<LayoverAirport> layoverAirports;
    private Date departure;
    private Date arrival;
    private int stops;
    private ArrayList<String> marketingCarrierCode;
    private ArrayList<String> operatingCarrierCode;
    private DurationTime durationTime;
    private ArrayList<LayoverTime> layoverTime;
    private TotalLayoverTime totalLayoverTime;
}
