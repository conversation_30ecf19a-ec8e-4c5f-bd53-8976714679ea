package com.hb.crm.core.dtos.spectTrum.FlightBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Duration information for booking
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BookingDuration {
    
    /**
     * Number of days
     */
    private Integer days;
    
    /**
     * Number of hours
     */
    private Integer hours;
    
    /**
     * Number of minutes
     */
    private Integer minutes;
}
