package com.hb.crm.core.dtos.spectTrum.HotelResponse;

import lombok.Data;
import java.util.List;

@Data
public class HotelData {
    private String id;
    private boolean supportsMulipleRateBooking;
    private String name;
    private String latitude;
    private String longitude;
    private String source;
    private List<String> paytype;
    private boolean refundable;
    private boolean packageHotel; // mapped to "package" field in JSON
    private String marketText;
    private List<String> addressLine;
    private String city;
    private String stateProvince;
    private String countryCode;
    private String postalCode;
    private String rating;
    private List<String> description;
    private List<Image> images;
    private List<Amenity> amenities;
    private NightlyPrice nightlyPrice;
    private int ranking;
    private TotalPrice totalPrice;
}
