package com.hb.crm.core.dtos.spectTrum.HotelGetBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Hotel guest document information
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelGuestDocument {
    
    /**
     * Document type
     */
    private String documentType;
    
    /**
     * Document reference number
     */
    private Integer documentRefNumber;
    
    /**
     * Document expiry date
     */
    private LocalDateTime expiryDate;
}
