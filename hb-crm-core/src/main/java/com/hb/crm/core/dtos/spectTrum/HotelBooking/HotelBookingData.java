package com.hb.crm.core.dtos.spectTrum.HotelBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Hotel booking data containing confirmation details
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelBookingData {
    
    /**
     * Trip identifier for the confirmed booking
     */
    private String tripId;
    
    /**
     * Booking reference identifier for the confirmed booking
     */
    private String bookingReferenceId;
    
    /**
     * Current booking status
     */
    private String bookingStatus;
}
