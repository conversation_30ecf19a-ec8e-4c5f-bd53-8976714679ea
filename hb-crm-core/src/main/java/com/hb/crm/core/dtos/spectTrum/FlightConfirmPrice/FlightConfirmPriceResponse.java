package com.hb.crm.core.dtos.spectTrum.FlightConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import java.util.List;

/**
 * Main response wrapper for Flight Confirm Price API
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FlightConfirmPriceResponse {
    
    private Boolean success;
    private Integer statusCode;
    private String message;
    private FlightConfirmPriceResponseData data;
    private List<String> errors;
}
