package com.hb.crm.core.dtos.spectTrum.HotelGetBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Flight passenger fare information for hotel package booking
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelFlightPassengerFare {
    
    /**
     * Fare type
     */
    private String fareType;
    
    /**
     * Currency
     */
    private String currency;
    
    /**
     * Base fare
     */
    private BigDecimal baseFare;
    
    /**
     * Base currency
     */
    private String basecurrency;
    
    /**
     * Tax amount
     */
    private BigDecimal tax;
    
    /**
     * List of taxes
     */
    private List<HotelFlightTax> taxes;
    
    /**
     * Fare basis code
     */
    private String fareBasisCode;
    
    /**
     * Total fare amount
     */
    private BigDecimal total;
}
