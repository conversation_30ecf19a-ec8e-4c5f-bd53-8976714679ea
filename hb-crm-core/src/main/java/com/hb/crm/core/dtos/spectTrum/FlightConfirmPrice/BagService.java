package com.hb.crm.core.dtos.spectTrum.FlightConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import java.util.List;

/**
 * Bag service information
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BagService {
    
    private Integer quantity;
    private Integer weight;
    private String weightUnit;
    private String name;
    private ServicePrice price;
    private Boolean bookableByItinerary;
    private List<String> segmentIds;
    private List<String> travelerIds;
}
