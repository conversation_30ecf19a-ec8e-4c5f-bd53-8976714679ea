package com.hb.crm.core.dtos.spectTrum.HotelPreBook;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Guest information for hotel pre-book room
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelPreBookGuest {
    
    /**
     * Guest title (Mr., Ms., etc.)
     */
    private String title;
    
    /**
     * Guest first name
     */
    private String firstName;
    
    /**
     * Guest last name
     */
    private String lastName;
    
    /**
     * Guest age
     */
    private Integer age;
    
    /**
     * Whether the guest is a child
     */
    private Boolean isChild;
    
    /**
     * Whether the guest is the booking holder
     */
    private Boolean isBookingHolder;
}
