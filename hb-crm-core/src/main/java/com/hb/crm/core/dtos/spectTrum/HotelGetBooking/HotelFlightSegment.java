package com.hb.crm.core.dtos.spectTrum.HotelGetBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Flight segment for hotel package booking
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelFlightSegment {
    
    /**
     * Whether this is a return segment
     */
    private Boolean isReturn;
    
    /**
     * Origin location
     */
    private String origin;
    
    /**
     * Origin airport details
     */
    private HotelFlightAirport originAirport;
    
    /**
     * Destination location
     */
    private String destination;
    
    /**
     * Destination airport details
     */
    private HotelFlightAirport destinationAirport;
    
    /**
     * Departure date and time
     */
    private LocalDateTime departureDateTime;
    
    /**
     * Arrival date and time
     */
    private LocalDateTime arrivalDateTime;
    
    /**
     * Flight number
     */
    private String flightNumber;
    
    /**
     * Departure terminal
     */
    private String departureTerminal;
    
    /**
     * Arrival terminal
     */
    private String arrivalTerminal;
    
    /**
     * Operating airline
     */
    private String operatingAirline;
    
    /**
     * Marketing airline
     */
    private String marketingAirline;
    
    /**
     * Equipment type
     */
    private String equipmentType;
    
    /**
     * Airline PNR
     */
    private String airlinePNR;
    
    /**
     * Booking class
     */
    private String bookingClass;
    
    /**
     * Cabin class
     */
    private String cabinClass;
    
    /**
     * Segment status
     */
    private String status;
    
    /**
     * Journey duration in minutes
     */
    private Integer journeyDuration;
    
    /**
     * Flight duration
     */
    private HotelFlightDuration duration;
    
    /**
     * Layover duration
     */
    private HotelFlightDuration layoverDuration;
    
    /**
     * Number of stops
     */
    private Integer stopQuantity;
}
