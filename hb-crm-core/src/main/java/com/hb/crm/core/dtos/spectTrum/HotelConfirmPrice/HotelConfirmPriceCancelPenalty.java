package com.hb.crm.core.dtos.spectTrum.HotelConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Hotel cancellation penalty information
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelConfirmPriceCancelPenalty {
    
    /**
     * Currency code for the penalty
     */
    private String currency;
    
    /**
     * Penalty start date/time
     */
    private String start;
    
    /**
     * Penalty end date/time
     */
    private String end;
    
    /**
     * Penalty amount
     */
    private String amount;
    
    /**
     * Number of nights penalty
     */
    private String nights;
    
    /**
     * Penalty percentage
     */
    private String percent;
    
    /**
     * Penalty description
     */
    private String description;
}
