package com.hb.crm.core.dtos.spectTrum.FlightConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Requirements for individual travelers
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TravelerRequirements {
    
    private String travelerId;
    private Boolean genderRequired;
    private Boolean documentRequired;
    private Boolean documentIssuanceCityRequired;
    private Boolean dateOfBirthRequired;
    private Boolean redressRequiredIfAny;
    private Boolean airFranceDiscountRequired;
    private Boolean spanishResidentDiscountRequired;
    private Boolean residenceRequired;
}
