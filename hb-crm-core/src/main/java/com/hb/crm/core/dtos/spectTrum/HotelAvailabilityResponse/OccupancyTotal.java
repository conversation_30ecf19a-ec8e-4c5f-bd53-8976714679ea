package com.hb.crm.core.dtos.spectTrum.HotelAvailabilityResponse;

import lombok.Data;

/**
 * Total pricing information for occupancy
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
public class OccupancyTotal {
    
    /**
     * Total including tax
     */
    private double includingTax;
    
    /**
     * Total excluding tax
     */
    private double excludingTax;
    
    /**
     * Tax amount
     */
    private double tax;
    
    /**
     * Commission amount
     */
    private double commission;
    
    /**
     * Markup amount
     */
    private double markup;
    
    /**
     * Offer price
     */
    private double offerPrice;
    
    /**
     * Currency
     */
    private String currency;
    
    /**
     * Pay at property price
     */
    private PayAtPropertyPrice payAtPropertyPrice;
    
    /**
     * Tenant commission
     */
    private double tenantCommission;
    
    /**
     * Tenant markup
     */
    private double tenantMarkup;
    
    /**
     * Partners commission
     */
    private double partnersCommission;
    
    /**
     * Partners markup
     */
    private double partnersMarkup;
    
    /**
     * Agency commission
     */
    private double agencyCommission;
    
    /**
     * Agency service fee
     */
    private double agencyServiceFee;
    
    /**
     * Supplier fee
     */
    private double supplierFee;
}
