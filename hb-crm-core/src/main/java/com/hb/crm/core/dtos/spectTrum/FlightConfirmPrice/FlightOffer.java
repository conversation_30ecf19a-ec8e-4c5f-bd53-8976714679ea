package com.hb.crm.core.dtos.spectTrum.FlightConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.hb.crm.core.beans.Flight.FareRules;
import com.hb.crm.core.beans.Flight.Itinerary;
import com.hb.crm.core.beans.Flight.Price;
import com.hb.crm.core.beans.Flight.PricingOptions;
import com.hb.crm.core.beans.Flight.TravelerPricing;

import lombok.Data;
import java.util.List;

/**
 * Flight offer details for confirm price request
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FlightOffer {
    
    private String searchIdentifier;
    private String type;
    private String id;
    private Boolean instantTicketingRequired;
    private Boolean nonHomogeneous;
    private Boolean oneWay;
    private String lastTicketingDate;
    private String flightReferenceId;
    private Boolean isInternational;
    private List<Itinerary> itineraries;
    private Price price;
    private PricingOptions pricingOptions;
    private List<String> validatingAirlineCodes;
    private List<TravelerPricing> travelerPricings;
    private FareRules fareRules;
    private List<TravelDocument> travelDocuments;
    private DetailedFareRules detailedFareRules;
}
