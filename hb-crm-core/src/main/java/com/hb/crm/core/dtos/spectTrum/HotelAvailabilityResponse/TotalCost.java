package com.hb.crm.core.dtos.spectTrum.HotelAvailabilityResponse;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Total cost breakdown for room option
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TotalCost {
    
    /**
     * Total excluding tax
     */
    private double totalExcludingTax;
    
    /**
     * Total including tax
     */
    private double totalIncludingTax;
    
    /**
     * Tax amount
     */
    private double tax;
    
    /**
     * Sales tax amount
     */
    private double salesTax;
    
    /**
     * Number of nights
     */
    private int numberOfNights;
    
    /**
     * Number of rooms
     */
    private int numberOfRooms;
    
    /**
     * Nightly average price
     */
    private double nightlyAverage;
    
    /**
     * Total commission
     */
    private double totalCommission;
    
    /**
     * Total markup
     */
    private double totalMarkup;
    
    /**
     * Total agency service fee
     */
    private double totalAgencyServiceFee;
    
    /**
     * Credit card charge
     */
    private double creditCardCharge;
    
    /**
     * Discounted price
     */
    private double discountedPrice;
    
    /**
     * Total offer price
     */
    private double totalOfferPrice;
    
    /**
     * Total including credit card charge
     */
    private double totalIncludingCreditCardCharge;
    
    /**
     * Currency
     */
    private String currency;
    
    /**
     * Pay at property information
     */
    private PayAtProperty payAtProperty;
    
    /**
     * Pay at property price information
     */
    private PayAtPropertyPrice payAtPropertyPrice;
}
