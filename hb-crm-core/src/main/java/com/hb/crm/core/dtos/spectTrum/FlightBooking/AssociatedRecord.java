package com.hb.crm.core.dtos.spectTrum.FlightBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Associated record information for flight booking
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AssociatedRecord {
    
    /**
     * Reference identifier for the associated record
     */
    private String reference;
    
    /**
     * Date when the record was created
     */
    private String creationDate;
    
    /**
     * Origin system code that created the record
     */
    private String originSystemCode;
    
    /**
     * Flight offer identifier associated with this record
     */
    private String flightOfferId;
}
