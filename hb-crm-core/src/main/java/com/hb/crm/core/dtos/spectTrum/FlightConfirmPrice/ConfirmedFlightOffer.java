package com.hb.crm.core.dtos.spectTrum.FlightConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.hb.crm.core.beans.Flight.FareRules;
import com.hb.crm.core.beans.Flight.Itinerary;
import com.hb.crm.core.beans.Flight.Price;
import com.hb.crm.core.beans.Flight.PricingOptions;
import lombok.Data;
import java.util.List;

/**
 * Confirmed flight offer with updated pricing and requirements
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConfirmedFlightOffer {
    
    private String searchIdentifier;
    private String type;
    private String id;
    private Boolean instantTicketingRequired;
    private Boolean nonHomogeneous;
    private Boolean oneWay;
    private Boolean paymentCardRequired;
    private String lastTicketingDate;
    private String flightReferenceId;
    private Boolean isInternational;
    private List<Itinerary> itineraries;
    private Price price;
    private PricingOptions pricingOptions;
    private List<String> validatingAirlineCodes;
    private List<ConfirmedTravelerPricing> travelerPricings;
    private FareRules fareRules;
    private List<TravelDocument> travelDocuments;
    private DetailedFareRules detailedFareRules;
}
