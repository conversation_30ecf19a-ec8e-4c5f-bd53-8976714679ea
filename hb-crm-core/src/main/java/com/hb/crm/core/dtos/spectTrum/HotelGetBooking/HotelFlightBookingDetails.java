package com.hb.crm.core.dtos.spectTrum.HotelGetBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Flight booking details for hotel package bookings
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelFlightBookingDetails {
    
    /**
     * Travel and technology reference
     */
    private String ttReference;
    
    /**
     * Passenger name record
     */
    private String pnr;
    
    /**
     * Flight code
     */
    private String flightCode;
    
    /**
     * Origin location
     */
    private String origin;
    
    /**
     * Origin airport details
     */
    private HotelFlightAirport originAirport;
    
    /**
     * Destination location
     */
    private String destination;
    
    /**
     * Destination airport details
     */
    private HotelFlightAirport destinationAirport;
    
    /**
     * Ticket time limit
     */
    private LocalDateTime tktTimeLimit;
    
    /**
     * Ticket time limit by airline
     */
    private String tktTimeLimitByAirline;
    
    /**
     * Flight status
     */
    private String status;
    
    /**
     * Validating carrier
     */
    private String validatingCarrier;
    
    /**
     * Fare type
     */
    private String fareType;
    
    /**
     * Travel date
     */
    private LocalDateTime travelDate;
    
    /**
     * Fare expiry date
     */
    private LocalDateTime fareExpireDate;
    
    /**
     * Source
     */
    private String source;
    
    /**
     * Supplier source
     */
    private String supplierSource;
    
    /**
     * Supplier reference ID
     */
    private String supplierReferenceId;
    
    /**
     * Currency
     */
    private String currency;
    
    /**
     * Total amount
     */
    private BigDecimal total;
    
    /**
     * Net total amount
     */
    private BigDecimal NetTotal;
    
    /**
     * Whether pre-combined
     */
    private Boolean preCombine;
    
    /**
     * Whether refundable
     */
    private Boolean isRefundable;
    
    /**
     * Detailed fare rules
     */
    private List<HotelFlightDetailedFareRule> detailedFareRules;
    
    /**
     * Flight itineraries
     */
    private List<HotelFlightItinerary> itineraries;
    
    /**
     * Flight passengers
     */
    private List<HotelFlightPassenger> passengers;
}
