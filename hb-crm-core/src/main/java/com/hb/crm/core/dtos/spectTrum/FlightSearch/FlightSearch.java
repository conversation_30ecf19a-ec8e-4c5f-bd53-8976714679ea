package com.hb.crm.core.dtos.spectTrum.FlightSearch;

import lombok.Data;

import java.util.ArrayList;
@Data
public class FlightSearch {
    private String key;
    private String currencyCode;
    private ArrayList<OriginDestination> originDestinations;
    private  ArrayList<filters> filters;
    private String pageNumber;
    private String pageSize;
    private ArrayList<Traveler> travelers;
    private SearchCriteria searchCriteria;
    private String segmentGroupId;
    private String datewiseCombination;
    private String jobId;
}
