package com.hb.crm.core.dtos.spectTrum.HotelGetBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Hotel booking data containing trip information and booking details
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelGetBookingData {
    
    /**
     * Trip identifier
     */
    private String tripId;
    
    /**
     * Current booking status
     */
    private String bookingStatus;
    
    /**
     * Detailed hotel booking information
     */
    private HotelBookingDetails hotel;
    
    /**
     * Detailed flight booking information (for package bookings)
     */
    private HotelFlightBookingDetails flight;
}
