package com.hb.crm.core.dtos.spectTrum.FlightConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import java.util.List;

/**
 * Data structure for Flight Confirm Price request
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FlightConfirmPriceData {
    
    private String origin;
    private String destination;
    private String flightReferenceId;
    private Boolean isRoundTrip = true;
    private List<Location> locations;
    private List<Aircraft> aircrafts;
    private List<Currency> currencies;
    private List<Carrier> carriers;
    private String type = "flight-offers-pricing";
    private List<FlightOffer> flightOffers;
}
