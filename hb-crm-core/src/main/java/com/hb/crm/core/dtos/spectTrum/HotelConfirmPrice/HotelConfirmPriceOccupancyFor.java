package com.hb.crm.core.dtos.spectTrum.HotelConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Hotel occupancy details for pricing
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelConfirmPriceOccupancyFor {
    
    /**
     * Number of adults
     */
    @JsonProperty("adult")
    private Integer adult;
    
    /**
     * Number of children
     */
    @JsonProperty("children")
    private Integer children;
}
