package com.hb.crm.core.dtos.spectTrum.FlightConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import java.util.Map;

/**
 * Included services in the confirmed price
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class IncludedServices {
    
    private Map<String, BagService> bags;
    private Map<String, CreditCardFee> creditCardFees;
    private DetailedFareRules detailedFareRules;
}
