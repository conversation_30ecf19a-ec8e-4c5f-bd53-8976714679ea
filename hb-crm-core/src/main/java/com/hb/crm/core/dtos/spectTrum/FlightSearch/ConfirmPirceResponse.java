package com.hb.crm.core.dtos.spectTrum.FlightSearch;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.hb.crm.core.Enums.FlightStep;

@JsonIgnoreProperties(ignoreUnknown = true)

public class ConfirmPirceResponse {
    private  confirmPriceData data;

    private FlightStep flightStep;

    public FlightStep getFlightStep() {
        return flightStep;
    }

    public void setFlightStep(FlightStep flightStep) {
        this.flightStep = flightStep;
    }

    public confirmPriceData getData() {
        return data;
    }

    public void setData(confirmPriceData data) {
        this.data = data;
    }
}
