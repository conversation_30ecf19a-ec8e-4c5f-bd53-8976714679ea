package com.hb.crm.core.dtos.spectTrum;

import java.util.List;

public class HotelAvailability {
    private  String propertyId;
    private String checkinDate;
    private String checkoutDate;

     private boolean isPackage;
    private boolean isRefundable=true;
    private String currency;
    private List<occupancies> occupancies;

    public String getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(String propertyId) {
        this.propertyId = propertyId;
    }

    public String getCheckinDate() {
        return checkinDate;
    }

    public void setCheckinDate(String checkinDate) {
        this.checkinDate = checkinDate;
    }

    public String getCheckoutDate() {
        return checkoutDate;
    }

    public void setCheckoutDate(String checkoutDate) {
        this.checkoutDate = checkoutDate;
    }


    public boolean isRefundable() {
        return isRefundable;
    }

    public void setRefundable(boolean refundable) {
        isRefundable = refundable;
    }

    public boolean isPackage() {
        return isPackage;
    }

    public void setPackage(boolean aPackage) {
        isPackage = aPackage;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public List<com.hb.crm.core.dtos.spectTrum.occupancies> getOccupancies() {
        return occupancies;
    }

    public void setOccupancies(List<com.hb.crm.core.dtos.spectTrum.occupancies> occupancies) {
        this.occupancies = occupancies;
    }
}
