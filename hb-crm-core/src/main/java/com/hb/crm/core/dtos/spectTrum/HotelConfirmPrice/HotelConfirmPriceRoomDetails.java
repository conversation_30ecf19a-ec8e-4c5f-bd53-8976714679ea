package com.hb.crm.core.dtos.spectTrum.HotelConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * Hotel room details with options and pricing
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelConfirmPriceRoomDetails {
    
    /**
     * Room identifier
     */
    private String id;
    
    /**
     * Room name
     */
    private String name;
    
    /**
     * Room descriptions
     */
    private List<String> descriptions;
    
    /**
     * Room amenities
     */
    private List<HotelConfirmPriceAmenity> amenities;
    
    /**
     * Room images
     */
    private List<HotelConfirmPriceImage> images;
    
    /**
     * Maximum allowed occupancy
     */
    private HotelConfirmPriceMaxAllowed maxAllowed;
    
    /**
     * Room comments
     */
    private String roomComments;
    
    /**
     * Room options with pricing
     */
    private List<HotelConfirmPriceRoomOption> options;
}
