package com.hb.crm.core.dtos.spectTrum.FlightBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.hb.crm.core.dtos.spectTrum.FlightConfirmPrice.Aircraft;
import com.hb.crm.core.dtos.spectTrum.FlightConfirmPrice.Carrier;
import com.hb.crm.core.dtos.spectTrum.FlightConfirmPrice.Currency;
import com.hb.crm.core.dtos.spectTrum.FlightConfirmPrice.Location;
import com.hb.crm.core.beans.Travelers.Traveler;
import lombok.Data;

import java.util.List;

/**
 * Detailed flight booking data containing all booking information
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FlightBookingData {
    
    /**
     * Whether this is a pre-combined booking
     */
    private Boolean preCombine;
    
    /**
     * Unique trip identifier for this booking
     */
    private String tripId;
    
    /**
     * Origin airport/location code
     */
    private String origin;
    
    /**
     * Destination airport/location code  
     */
    private String destination;
    
    /**
     * Whether this is a round-trip booking
     */
    private Boolean isRoundTrip;
    
    /**
     * List of locations involved in the trip
     */
    private List<Location> locations;
    
    /**
     * List of aircraft used in the flights
     */
    private List<Aircraft> aircrafts;
    
    /**
     * List of currencies used in pricing
     */
    private List<Currency> currencies;
    
    /**
     * List of airline carriers
     */
    private List<Carrier> carriers;
    
    /**
     * Type of booking
     */
    private String type;
    
    /**
     * Unique identifier for this booking
     */
    private String id;
    
    /**
     * Associated booking records and references
     */
    private List<AssociatedRecord> associatedRecords;
    
    /**
     * List of booked flight offers with detailed information
     */
    private List<BookedFlightOffer> flightOffers;
    
    /**
     * List of travelers with complete booking details
     */
    private List<Traveler> travelers;
}
