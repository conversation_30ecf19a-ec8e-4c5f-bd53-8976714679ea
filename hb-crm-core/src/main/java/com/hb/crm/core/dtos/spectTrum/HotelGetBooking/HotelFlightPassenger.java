package com.hb.crm.core.dtos.spectTrum.HotelGetBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Flight passenger information for hotel package booking
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelFlightPassenger {
    
    /**
     * Passenger title
     */
    private String title;
    
    /**
     * First name
     */
    private String firstName;
    
    /**
     * Middle name
     */
    private String middleName;
    
    /**
     * Last name
     */
    private String lastName;
    
    /**
     * Gender
     */
    private String gender;
    
    /**
     * Email address
     */
    private String email;
    
    /**
     * Date of birth
     */
    private LocalDateTime dob;
    
    /**
     * Passenger type
     */
    private String paxType;
    
    /**
     * Mobile number
     */
    private String mobile;
    
    /**
     * Passport number
     */
    private String passportNumber;
    
    /**
     * Passport nationality
     */
    private String passportNationality;
    
    /**
     * Passport date of issuance
     */
    private String passportDOI;
    
    /**
     * Passport date of expiry
     */
    private String passportDOE;
    
    /**
     * Frequent flyer number
     */
    private String ffNumber;
    
    /**
     * List of frequent flyer numbers
     */
    private List<String> ffNumbers;
    
    /**
     * Fare information
     */
    private List<HotelFlightPassengerFare> fares;
    
    /**
     * Special service requests
     */
    private List<HotelFlightPassengerSSR> ssrs;
    
    /**
     * Ticket information
     */
    private List<HotelFlightPassengerTicket> tickets;
}
