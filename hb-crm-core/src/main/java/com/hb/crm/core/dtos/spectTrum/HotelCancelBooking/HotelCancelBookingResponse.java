package com.hb.crm.core.dtos.spectTrum.HotelCancelBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * Response DTO for Hotel Cancel Booking API from Spectrum Travel API
 * 
 * This DTO represents the response structure for cancelling a hotel booking.
 * 
 * API Endpoint: DELETE /api/v1/beta/cancel/{tripId}
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelCancelBookingResponse {
    
    /**
     * Indicates if the cancellation request was successful
     */
    private Boolean success;
    
    /**
     * Status code of the cancellation response
     */
    private Integer statusCode;
    
    /**
     * Response message describing the cancellation result
     */
    private String message;
    
    /**
     * Boolean indicating whether the booking was successfully cancelled
     */
    private Boolean data;
    
    /**
     * List of errors if the cancellation failed or had issues
     */
    private List<String> errors;
}
