package com.hb.crm.core.dtos.spectTrum.FlightBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Tax information for booking
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BookingTax {
    
    /**
     * Currency code
     */
    private String currency;
    
    /**
     * Tax amount
     */
    private BigDecimal tax;
    
    /**
     * Tax code
     */
    private String code;
}
