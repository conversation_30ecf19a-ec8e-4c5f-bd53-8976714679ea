package com.hb.crm.core.dtos.spectTrum.HotelGetBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Amount due at property information
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelDueAtProperty {
    
    /**
     * Price amount due at property
     */
    private BigDecimal price;
    
    /**
     * Currency code
     */
    private String currency;
}
