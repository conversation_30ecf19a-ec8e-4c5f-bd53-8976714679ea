package com.hb.crm.core.dtos.spectTrum.HotelAvailabilityResponse;

import lombok.Data;

import java.util.List;

/**
 * Hotel check-in information and policies
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
public class CheckIn 
{
    
    /**
     * Check-in begin time
     */
    private String beginTime;
    
    /**
     * Check-in end time
     */
    private String endTime;
    
    /**
     * General check-in instructions
     */
    private String instructions;
    
    /**
     * Special check-in instructions
     */
    private String specialInstructions;
    
    /**
     * Minimum age for check-in
     */
    private int minAge;
    
    /**
     * Additional instructions list
     */
    private List<String> additionalInstructions;
}
