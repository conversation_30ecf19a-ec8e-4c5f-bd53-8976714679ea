package com.hb.crm.core.dtos.spectTrum.HotelGetBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * Response DTO for Hotel Get Booking API from Spectrum Travel API
 * 
 * This DTO represents the response structure for retrieving detailed hotel booking information.
 * 
 * API Endpoint: GET /api/v1/beta/booking/{tripId}
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelGetBookingResponse {
    
    /**
     * Indicates if the request was successful
     */
    private Boolean success;
    
    /**
     * Status code of the response
     */
    private Integer statusCode;
    
    /**
     * Response message
     */
    private String message;
    
    /**
     * Main booking data containing hotel and flight details
     */
    private HotelGetBookingData data;
    
    /**
     * List of errors if the request failed or had issues
     */
    private List<String> errors;
}
