package com.hb.crm.core.dtos.spectTrum.HotelConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * Hotel confirmation data containing detailed property information
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelConfirmPriceData {
    
    /**
     * Property identifier
     */
    private String propertyId;
    
    /**
     * Whether the price has changed since initial search
     */
    private Boolean isPriceChanged;
    
    /**
     * Whether the property supports multiple rate booking
     */
    private Boolean supportsMulipleRateBooking;
    
    /**
     * Whether the property supports check rate functionality
     */
    private Boolean supportsCheckRate;
    
    /**
     * Property reference identifier
     */
    private String propertyReferenceId;
    
    /**
     * Property name
     */
    private String name;
    
    /**
     * Property ratings
     */
    private HotelConfirmPriceRatings ratings;
    
    /**
     * Property category
     */
    private String category;
    
    /**
     * Check-in information
     */
    private HotelConfirmPriceCheckin checkin;
    
    /**
     * Check-out information
     */
    private HotelConfirmPriceCheckout checkout;
    
    /**
     * Property policies
     */
    private List<String> policies;
    
    /**
     * Property attributes
     */
    private List<HotelConfirmPriceAttribute> attributes;
    
    /**
     * Property amenities
     */
    private List<HotelConfirmPriceAmenity> amenities;
    
    /**
     * Available rooms with options and pricing
     */
    private List<HotelConfirmPriceRoomDetails> rooms;
    
    /**
     * Property descriptions
     */
    private List<HotelConfirmPriceDescription> descriptions;
    
    /**
     * Nearby airports
     */
    private List<String> airports;
    
    /**
     * Property images
     */
    private List<HotelConfirmPriceImage> images;
    
    /**
     * Property address
     */
    private HotelConfirmPriceAddress address;
    
    /**
     * Sales type
     */
    private String salesType;
    
    /**
     * Source of the property
     */
    private String source;
    
    /**
     * Whether all passenger details are required
     */
    private Boolean requiresAllPaxesDetails;
    
    /**
     * Free cancellation indicator
     */
    private Boolean freeCancellationInd;
    
    /**
     * Pay at property indicator
     */
    private Boolean payAtPropertyInd;
}
