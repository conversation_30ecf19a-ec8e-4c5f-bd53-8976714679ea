package com.hb.crm.core.dtos.spectTrum.HotelGetBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * Hotel booking image information
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelBookingImage {
    
    /**
     * Image caption
     */
    private String caption;
    
    /**
     * Image links
     */
    private List<HotelBookingImageLink> links;
}
