package com.hb.crm.core.dtos.spectTrum.HotelGetBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Flight passenger ticket information for hotel package booking
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelFlightPassengerTicket {
    
    /**
     * Ticket identifier
     */
    private String ticketId;
    
    /**
     * Original ticket identifier
     */
    private String originalTicketId;
    
    /**
     * Ticket number
     */
    private String ticketNumber;
    
    /**
     * Ticket type
     */
    private String ticketType;
    
    /**
     * Ticket status
     */
    private String ticketStatus;
    
    /**
     * Airline code
     */
    private String airlineCode;
    
    /**
     * Currency
     */
    private String currency;
    
    /**
     * Fare amount
     */
    private String fare;
    
    /**
     * Date when ticket was issued
     */
    private LocalDateTime issuedOn;
    
    /**
     * Whether ticket was re-issued
     */
    private Boolean reIssued;
    
    /**
     * Ticket free text
     */
    private String ticketFreeText;
    
    /**
     * Office identifier
     */
    private String officeId;
    
    /**
     * Flight numeric code
     */
    private String flightNumericCode;
    
    /**
     * Segment reference
     */
    private String segmentReference;
}
