package com.hb.crm.core.dtos.spectTrum.HotelAvailabilityResponse;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Total price information for pay at property options
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PayAtPropertyPrice {
    
    /**
     * Total amount
     */
    private double total;
    
    /**
     * Currency
     */
    private String currency;
}
