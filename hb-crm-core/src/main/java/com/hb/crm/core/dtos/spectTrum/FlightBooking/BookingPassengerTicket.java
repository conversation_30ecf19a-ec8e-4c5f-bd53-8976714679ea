package com.hb.crm.core.dtos.spectTrum.FlightBooking;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Passenger ticket information for booking
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BookingPassengerTicket {
    
    /**
     * Ticket identifier
     */
    private String ticketId;
    
    /**
     * Original ticket identifier
     */
    private String originalTicketId;
    
    /**
     * Ticket number
     */
    private String ticketNumber;
    
    /**
     * Type of ticket
     */
    private String ticketType;
    
    /**
     * Ticket status
     */
    private String ticketStatus;
    
    /**
     * Airline code
     */
    private String airlineCode;
    
    /**
     * Date and time when ticket was issued
     */
    private LocalDateTime issuedOn;
    
    /**
     * Whether the ticket was re-issued
     */
    private Boolean reIssued;
    
    /**
     * Free text information about the ticket
     */
    private String ticketFreeText;
    
    /**
     * Flight numeric code
     */
    private String flightNumericCode;
    
    /**
     * Segment reference
     */
    private String segmentReference;
}
