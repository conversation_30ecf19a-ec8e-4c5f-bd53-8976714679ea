package com.hb.crm.core.dtos.spectTrum.HotelConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * Hotel check-in information
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelConfirmPriceCheckin {
    
    /**
     * Check-in begin time
     */
    private String beginTime;
    
    /**
     * Check-in end time
     */
    private String endTime;
    
    /**
     * Check-in instructions
     */
    private String instructions;
    
    /**
     * Special check-in instructions
     */
    private String specialInstructions;
    
    /**
     * Minimum age for check-in
     */
    private Integer minAge;
    
    /**
     * Additional check-in instructions
     */
    private List<String> additionalInstructions;
}
