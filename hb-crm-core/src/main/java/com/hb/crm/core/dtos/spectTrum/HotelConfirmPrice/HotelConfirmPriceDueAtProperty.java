package com.hb.crm.core.dtos.spectTrum.HotelConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Amount due at property information
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelConfirmPriceDueAtProperty {
    
    /**
     * Price amount due at property
     */
    private BigDecimal price;
    
    /**
     * Currency code for the amount due at property
     */
    private String currency;
}
