package com.hb.crm.core.dtos.spectTrum.HotelAvailabilityResponse;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Payment information for amounts to be paid at property
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PayAtProperty {
    
    /**
     * Amount to be paid at property
     */
    private double amount;
    
    /**
     * Currency for the amount
     */
    private String currency;
}
