package com.hb.crm.core.dtos.spectTrum.FlightConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import java.util.List;

/**
 * Confirmed flight data with updated pricing and requirements
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConfirmedFlightData {
    
    private Boolean preCombine;
    private String origin;
    private String destination;
    private Boolean isRoundTrip;
    private List<Location> locations;
    private List<Aircraft> aircrafts;
    private List<Currency> currencies;
    private List<Carrier> carriers;
    private String type;
    private List<ConfirmedFlightOffer> flightOffers;
    private BookingRequirements bookingRequirements;
}
