package com.hb.crm.core.dtos.spectTrum.HotelConfirmPrice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Hotel deposit policies information
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelConfirmPriceDepositPolicies {
    
    /**
     * Number of nights for deposit
     */
    private String nights;
    
    /**
     * Whether remainder applies
     */
    private Boolean remainder;
    
    /**
     * Deposit percentage
     */
    private String percent;
    
    /**
     * Deposit amount
     */
    private String amount;
    
    /**
     * When deposit is due
     */
    private String due;
}
