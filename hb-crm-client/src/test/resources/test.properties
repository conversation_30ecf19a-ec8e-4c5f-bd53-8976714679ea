test.UserName="<EMAIL>"
spring.Data.mongodb.uri=mongodb+srv://hbdbadmin:<EMAIL>/?retryWrites=true&w=majority
spring.jackson.time-zone=GMT+4
spring.data.mongodb.database=havebreak
spring.data.mongodb.auto-index-creation=true
springdoc.swagger-ui.path=/swagger-ui/index.html
jwt.secret=456yuknjkbhvcd567788oijhsdkfjsdlkfjeowiufksdfwekfjwei456yuknjkbhvcd567788oijhsdkfjsdlkfjeowiufksdfwekfjwei456yuknjkbhvcd567788oijhsdkfjsdlkfjeowiufksdfwekfjwei456yuknjkbhvcd567788oijhsdkfjsdlkfjeowiufksdfwekfjwei
adminjwt.secret=64a6988ec0ecacbdf40ecf504e70b9a5f6174a8992c856c7ee22e1e0be03a8890412904b9d17a467d03559fe573c324271615dbcf191e4cfc259b5a01a3bb824
jwt.expirationDateInMs=43200000
jwt.RefreshexpirationDateInMs=605000000
server.port=8082
server.max-http-header-size=10000000