spring.data.mongodb.uri=mongodb+srv://hbdbadmin:<EMAIL>/?retryWrites=true&w=majority
spring.data.mongodb.uri2=mongodb+srv://hbdbadmin:<EMAIL>/?retryWrites=true&w=majority
spring.data.mongodb.database2=searchhavebreak
spring.data.mongodb.database=havebreak

#spring.Data.mongodb.uri=mongodb://localhost:27017
#spring.data.mongodb.uri2=mongodb://localhost:27017
spring.jackson.time-zone=GMT+4
spring.data.mongodb.auto-index-creation=true
springdoc.swagger-ui.path=/swagger-ui/index.html
jwt.secret=456yuknjkbhvcd567788oijhsdkfjsdlkfjeowiufksdfwekfjwei456yuknjkbhvcd567788oijhsdkfjsdlkfjeowiufksdfwekfjwei456yuknjkbhvcd567788oijhsdkfjsdlkfjeowiufksdfwekfjwei456yuknjkbhvcd567788oijhsdkfjsdlkfjeowiufksdfwekfjwei
adminjwt.secret=64a6988ec0ecacbdf40ecf504e70b9a5f6174a8992c856c7ee22e1e0be03a8890412904b9d17a467d03559fe573c324271615dbcf191e4cfc259b5a01a3bb824
jwt.expirationDateInMs=********
jwt.RefreshexpirationDateInMs=*********
server.port=8082
server.max-http-header-size=********

spring.mail.host=smtp.office365.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=Ruz01282
spring.mail.properties.mail.smtp.auth=false
spring.mail.properties.mail.smtp.starttls.enable=true
twilio.accountSid=**********************************
twilio.authToken=f3b3e4353a971a3a36d8489eabb3ce00
twilio.phoneNumber=+***********
facebookgragh=https://graph.facebook.com/v17.0/
googleouath=https://www.googleapis.com/oauth2/v3/userinfo?access_token=
cloud.aws.credentials.secretKey=JSeZHNR9UVbxGrTLOvgixBw8BdENvZkhIBQx69NU
cloud.aws.credentials.accessKey=********************
cloud.aws.region.static=eu-west-2
cloud.aws.bucketName=media-havebreak-com-dev
Spectrum.Baseurl=https://spectrum-api.trinetiumtech.com
Spectrum.AuthUrl=https://api.trinetiumtech.com/agencyservice/api/token
Spectrum.HotelBaseUrl=https://spectrum.trinetiumtech.com/hotel
Spectrum.FloghtBaseUrl=https://spectrum.trinetiumtech.com/flight
Spectrum.api_key=9c3905bf4cca4b569d612c9ffa6e9d84
Spectrum.agency_Id=555
Spectrum.property_id=13978
Spectrum.property1_id=139781
stripe.secretKey=sk_test_51Io3JwHKp7BQWgvKw5xJSJ2lyf2wBnsBXrG2HwO7XQNXSP7OOT3qIbkPl34hLJFtTbNRqWqB0WpJnglckBlYugi900izxRvL5V
firebase.projectId=havebreak-61f55
spring.security.oauth2.client.registration.google.client-id=502651611052-n6djo5qc26402sbsb50g005tgsaje382.apps.googleusercontent.com
spring.security.oauth2.client.registration.google.client-secret=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

spring.redis.host=localhost
spring.redis.port=6379
redis.active=false
spring.redis.client-type=lettuce
test.UserName="<EMAIL>"