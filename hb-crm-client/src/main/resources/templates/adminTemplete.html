<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
  <head>
 <style>


  .email-havebreak {
  background-color: #ffffff;
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100%;
}

.email-havebreak .div {
  background-color: #ffffff;
  overflow: hidden;
  width: 595px;
  height: 754px;
  position: relative;
}
  .text-wrapper-5 {
    width: 100% !important;
    text-align: center;
  }
.email-havebreak .highLight {
  position: relative;

}

.email-havebreak .overlap-highLight {
  position: relative;
  width: 595px;
  height: 86px;
  background-color: #f5f5f5;
  border-bottom-width: 1px;
  border-bottom-style: solid;
  border-color: rgba(118, 98, 179, 1);
}

.email-havebreak .highLight-2 {
  position: relative;

}

.email-havebreak .text-wrapper {
  position: absolute;
  width: 103px;
  height: 16px;
  top: 0;
  left: 0;
  font-family: "Open Sans", Helvetica;
  font-weight: 400;
  color: rgba(118, 98, 179, 1);
  font-size: 10px;
  text-align: center;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .text-wrapper-2 {
  position: absolute;
  width: 80px;
  height: 16px;
  top: 16px;
  left: 11px;
  font-family: "Open Sans", Helvetica;
  font-weight: 400;
  color: rgba(118, 98, 179, 1);
  font-size: 10px;
  text-align: center;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .text-wrapper-3 {
  position: absolute;
  width: 81px;
  height: 16px;
  top: 29px;
  left: 314px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: rgba(118, 98, 179, 1);
  font-size: 10px;
  text-align: center;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .frame {
  position: absolute;
  width: 91px;
  height: 58px;
  top: 10px;
  left: 28px;
}

.email-havebreak .frame-2 {
  margin-top: 10px;
  display: flex;
  align-items: center;

  position: relative;

}

.email-havebreak .frame-wrapper {
  position: relative;
  width: 80.8px;
  height: 17px;
}

.email-havebreak .frame-3 {
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
}

.email-havebreak .overlap-highLight-wrapper {
  position: relative;
  width: 19px;
  height: 17px;
  padding-right: 5px;
}

.email-havebreak .div-wrapper {
  position: relative;
  width: 17px;
  height: 17px;
  background-color: rgba(118, 98, 179, 1);
  border-radius: 8.5px;
}

.email-havebreak .text-wrapper-4 {
  position: absolute;
  height: 17px;
  top: 0;
  left: 6px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: #ffffff;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .img {
  position: relative;
  width: 11.8px;
  height: 12.2px;
}

.email-havebreak .text-wrapper-5 {
  position: relative;
  width: fit-content;
  margin-top: -1px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: rgba(118, 98, 179, 1);
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .frame-4 {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  position: relative;
  flex: 0 0 auto;
}

.email-havebreak .text-wrapper-6 {
  position: relative;
  width: fit-content;
  font-family: "Open Sans", Helvetica;
  font-weight: 400;
  color: #989898;
  font-size: 8px;
  letter-spacing: 0;
  line-height: 13.2px;
  white-space: nowrap;
}

.email-havebreak .text-wrapper-7 {
  position: relative;
  width: fit-content;
  margin-top: -1px;
  font-family: "Open Sans", Helvetica;
  font-weight: 400;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .navbar-wrapper {
  position: relative;
}

.email-havebreak .navbar {
  padding-bottom: 10px;
  margin-bottom: 10px;
  position: relative;
 }

.email-havebreak .text-wrapper-8 {
  top: 1px;
  left: 7px;
  font-weight: 700;
  position: absolute;
  height: 17px;
  font-family: "Open Sans", Helvetica;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .text-wrapper-9 {
  position: absolute;
  height: 17px;
  top: 26px;
  left: 7px;
  font-family: "Open Sans", Helvetica;
  font-weight: 400;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .bangalore-BLR-to {
  position: absolute;
  height: 17px;
  top: 51px;
  left: 7px;
  font-family: "Open Sans", Helvetica;
  font-weight: 400;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .istanbul-IST-to {
  position: absolute;
  height: 17px;
  top: 76px;
  left: 7px;
  font-family: "Open Sans", Helvetica;
  font-weight: 400;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .text-wrapper-10 {
  position: absolute;
  height: 17px;
  top: 1px;
  left: 190px;
  font-family: "Open Sans", Helvetica;
  font-weight: 700;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .text-wrapper-11 {
  top: 26px;
  left: 190px;
  font-weight: 400;
  position: absolute;
  height: 17px;
  font-family: "Open Sans", Helvetica;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .element {
  top: 51px;
  left: 190px;
  position: absolute;
  height: 17px;
  font-family: "Open Sans", Helvetica;
  font-weight: 400;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .element-2 {
  top: 76px;
  left: 190px;
  position: absolute;
  height: 17px;
  font-family: "Open Sans", Helvetica;
  font-weight: 400;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .text-wrapper-12 {
  top: 1px;
  left: 317px;
  font-weight: 700;
  position: absolute;
  height: 17px;
  font-family: "Open Sans", Helvetica;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .text-wrapper-13 {
  top: 26px;
  left: 317px;
  font-weight: 400;
  position: absolute;
  height: 17px;
  font-family: "Open Sans", Helvetica;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .economy {
  top: 51px;
  position: absolute;
  height: 17px;
  left: 317px;
  font-family: "Open Sans", Helvetica;
  font-weight: 400;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .economy-2 {
  top: 76px;
  position: absolute;
  height: 17px;
  left: 317px;
  font-family: "Open Sans", Helvetica;
  font-weight: 400;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .text-wrapper-14 {
  top: 1px;
  left: 424px;
  font-weight: 700;
  position: absolute;
  height: 17px;
  font-family: "Open Sans", Helvetica;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .text-wrapper-15 {
  top: 26px;
  left: 424px;
  font-weight: 400;
  position: absolute;
  height: 17px;
  font-family: "Open Sans", Helvetica;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .inside {
  top: 51px;
  left: 424px;
  font-weight: 400;
  position: absolute;
  height: 17px;
  font-family: "Open Sans", Helvetica;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .end-external {
  top: 76px;
  left: 424px;
  font-weight: 400;
  position: absolute;
  height: 17px;
  font-family: "Open Sans", Helvetica;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .overlap {
  position: absolute;
  width: 518px;
  height: 93px;
  top: 0;
  left: 0;
}

.email-havebreak .vector {
  position: absolute;
  width: 518px;
  height: 1px;
  top: 22px;
  left: 0;
}

.email-havebreak .vector-2 {
  position: absolute;
  width: 518px;
  height: 1px;
  top: 47px;
  left: 0;
}

.email-havebreak .vector-3 {
  position: absolute;
  width: 518px;
  height: 1px;
  top: 72px;
  left: 0;
}

.email-havebreak .vector-4 {
  position: absolute;
  width: 1px;
  height: 93px;
  top: 0;
  left: 181px;
}

.email-havebreak .vector-5 {
  position: absolute;
  width: 1px;
  height: 93px;
  top: 0;
  left: 309px;
}

.email-havebreak .vector-6 {
  position: absolute;
  width: 1px;
  height: 93px;
  top: 0;
  left: 414px;
}

.email-havebreak .highLight-3 {
  padding-bottom: 10px;
  margin-bottom: 10px;
  border-bottom: rgba(118, 98, 179, 1);
  position: relative;

}

.email-havebreak .navbar-2 {
  position: relative;
  width: 518px;
  height: 47px;
}

.email-havebreak .passport {
  top: 1px;
  left: 141px;
  font-weight: 700;
  position: absolute;
  height: 17px;
  font-family: "Open Sans", Helvetica;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .n {
  position: absolute;
  height: 17px;
  top: 26px;
  left: 141px;
  font-family: "Open Sans", Helvetica;
  font-weight: 400;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .date-of-issue {
  position: absolute;
  height: 17px;
  top: 1px;
  left: 240px;
  font-family: "Open Sans", Helvetica;
  font-weight: 700;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .element-3 {
  top: 26px;
  left: 240px;
  position: absolute;
  height: 17px;
  font-family: "Open Sans", Helvetica;
  font-weight: 400;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .expiry-date {
  position: absolute;
  height: 17px;
  top: 1px;
  left: 332px;
  font-family: "Open Sans", Helvetica;
  font-weight: 700;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .element-4 {
  top: 26px;
  left: 332px;
  position: absolute;
  height: 17px;
  font-family: "Open Sans", Helvetica;
  font-weight: 400;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .overlap-2 {
  position: absolute;
  width: 518px;
  height: 47px;
  top: 0;
  left: 0;
}

.email-havebreak .vector-7 {
  position: absolute;
  width: 1px;
  height: 47px;
  top: 0;
  left: 131px;
}

.email-havebreak .vector-8 {
  position: absolute;
  width: 1px;
  height: 47px;
  top: 0;
  left: 232px;
}

.email-havebreak .vector-9 {
  position: absolute;
  width: 1px;
  height: 47px;
  top: 0;
  left: 414px;
}

.email-havebreak .vector-10 {
  position: absolute;
  width: 1px;
  height: 47px;
  top: 0;
  left: 322px;
}

.email-havebreak .frame-5 {
  border-top-width: 1px;
  border-top-style: solid;
  border-color: rgba(118, 98, 179, 1);
  padding-top: 10px;
  display: flex;
  align-items: center;
   position: relative;

}

.email-havebreak .highLight-4 {
  position: relative;
  width: 81.2px;
  height: 17px;
}

.email-havebreak .ic-round-hotel {
  position: relative;
  width: 12.2px;
  height: 12.2px;
}

.email-havebreak .vector-11 {
  position: absolute;
  width: 552px;
  height: 1px;
  top: 324px;
  left: 22px;
  object-fit: cover;
}

.email-havebreak .vector-12 {
  position: absolute;
  width: 552px;
  height: 1px;
  top: 608px;
  left: 22px;
  object-fit: cover;
}

.email-havebreak .frame-6 {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  gap: 7px;
  position: relative;

}

.email-havebreak .frame-7 {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  position: relative;
  flex: 0 0 auto;
}

.email-havebreak .frame-8 {
  display: flex;
  width: 518px;
  align-items: center;
   position: relative;
  flex: 0 0 auto;
}
  .email-havebreak .frame-10 .text-wrapper-21 {
    width: 500px;
  }

  .email-havebreak .frame-10  .text-wrapper-24{
    width: 500px;
  }
.email-havebreak .frame-9 {
  width: 400px;
  display: inline-flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  flex: 0 0 auto;
}



.email-havebreak .frame-10 {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
  position: relative;
  flex: 0 0 auto;
}

.email-havebreak .text-wrapper-16 {
  position: relative;
  width: fit-content;
  margin-top: -1px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: #989898;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .text-wrapper-17 {
  position: relative;
  width: fit-content;
  margin-top: -3px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .frame-11 {
  display: inline-flex;
  flex-direction: column;
  align-items: flex-end;
  position: relative;
  flex: 0 0 auto;
}

.email-havebreak .text-wrapper-18 {
  position: relative;
  width: fit-content;
  margin-top: -1px;
  font-family: "Open Sans", Helvetica;
  font-weight: 400;
  color: #989898;
  font-size: 8px;
  letter-spacing: 0;
  line-height: 13.2px;
  white-space: nowrap;
}

.email-havebreak .text-wrapper-19 {
  position: relative;
  width: fit-content;
  margin-top: -1px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: #1d1d1d;
  font-size: 8px;
  letter-spacing: 0;
  line-height: 13.2px;
  white-space: nowrap;
}

.email-havebreak .text-wrapper-20 {
  position: relative;
  width: fit-content;
  margin-top: -3px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: rgba(118, 98, 179, 1);
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .text-wrapper-21 {
  position: relative;
  width: fit-content;
  margin-top: -1px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: #1d1d1d;
  font-size: 8px;
  letter-spacing: 0;
  line-height: 13.2px;
  white-space: nowrap;
}

.email-havebreak .frame-12 {
  position: relative;
  flex: 0 0 auto;
}

.email-havebreak .vector-13 {
  position: relative;
  width: 518px;
  height: 1px;
}

.email-havebreak .frame-13 {
  display: inline-flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1px;
  position: relative;
  flex: 0 0 auto;
}

.email-havebreak .text-wrapper-22 {
  position: relative;
  width: fit-content;
  margin-top: -1px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: #989898;
  font-size: 8px;
  letter-spacing: 0;
  line-height: 13.2px;
  white-space: nowrap;
}

.email-havebreak .p {
  position: relative;
  width: fit-content;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: #1d1d1d;
  font-size: 8px;
  letter-spacing: 0;
  line-height: 13.2px;
  white-space: nowrap;
}

.email-havebreak .cancellations-made {
  position: relative;
  width: 519px;
  font-family: "Open Sans", Helvetica;
  font-weight: 400;
  color: #989898;
  font-size: 8px;
  letter-spacing: 0;
  line-height: 13.2px;
}

.email-havebreak .highLight-5 {
  position: relative;
  background-color: #f5f5f5;
  border-top-width: 1px;
  border-top-style: solid;
  border-color: rgba(118, 98, 179, 1);
}

.email-havebreak .overlap-3 {
  position: absolute;
  width: 595px;
  height: 26px;
  top: 101px;
  left: 0;
  background-color: rgba(118, 98, 179, 1);
  border-color: rgba(118, 98, 179, 1);
}

.email-havebreak .frame-14 {
  display: flex;
  width: 518px;
  align-items: center;
  justify-content: space-around;
  gap: 206px;
  position: relative;
  top: 4px;
  left: 39px;
}

.email-havebreak .text-wrapper-23 {
  position: relative;
  width: fit-content;
  margin-top: -1px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: rgba(255, 255, 255, 1);
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}

.email-havebreak .frame-15 {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  position: absolute;
  top: 8px;
  left: 38px;
}

.email-havebreak .frame-16 {
  display: flex;
  width: 518px;
  align-items: center;
  justify-content: space-around;
  gap: 206px;
  position: relative;
  flex: 0 0 auto;
}

.email-havebreak .text-wrapper-24 {
  position: relative;
  width: fit-content;
  margin-top: -1px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: #1d1d1d;
  font-size: 10px;
  letter-spacing: 0;
  line-height: 16.5px;
  white-space: nowrap;
}
  table {
    width: 100%;
    text-align: left;
  }
  th {
    font-weight: 700;
    font-size: 10px;
    font-family: "Open Sans", Helvetica;
    color: #1d1d1d;
  }
  td {
    font-weight: 400;
    font-size: 10px;
    font-family: "Open Sans", Helvetica;
    font-weight: 400;
    color: #1d1d1d;
  }
  td, th {
    padding: 6px;
    border-left: 1px dotted rgba(118, 98, 179, 1);
    padding-left: 21px;
  }
  td:first-child, th:first-child {
    border-left: unset;
    padding-left: unset;
  }
  table tbody tr td {
    border-top: 1px dotted rgba(118, 98, 179, 1);

  }

 </style>
  </head>
  <body>
    <div class="email-havebreak">
      <div class="div">
        <div class="highLight">
          <div class="overlap-highLight">

            <div class="text-wrapper-3">Sun, 22 Sep 2023</div>
            <img class="frame" src="img/Frame.svg" />
          </div>
        </div>
        <div class="frame-2">
          <div class="frame-wrapper">
            <div class="frame-3">
              <div class="overlap-highLight-wrapper">
                <div class="div-wrapper"><div class="text-wrapper-4">1</div></div>
              </div>
              <img class="img" src="img/Group.svg" />
              <div class="text-wrapper-5">Flights</div>
            </div>
          </div>

        </div>
        <div class="navbar-wrapper">
          <div class="navbar">
            <table>
              <thead>
              <tr>
                <th>
                  Path
                </th>
                <th>
                  Check in Date
                </th>
                <th>
                  Class
                </th>
                <th>
                  Flight type
                </th>
              </tr>
              </thead>
              <tbody>
                  <tr th:each="flightPath : ${flightPaths}">
                  <td th:text="${flightPath.path}"></td>
                  <td th:text="${flightPath.checkIn}"></td>
                  <td th:text="${flightPath._class}"></td>
                  <td th:text="${flightPath.flightType}"></td>
                </tr>
              </tbody>
            </table>

          </div>
        </div>
        <div class="highLight-3">
          <table>
            <thead>
            <tr>
              <th>
                Passenger name
              </th>
              <th>
                Passport
              </th>
              <th>
                Date of issue
              </th>
              <th>
                Expiry date
              </th>
              <th>Date of Birth</th>
            </tr>
            </thead>
            <tbody>
            <tr th:each="travellerTemp : ${travellerTemps}">
              <td th:text="${travellerTemp.name}"></td>
              <td th:text="${travellerTemp.passport}"></td>
              <td th:text="${travellerTemp.DateOfIssue}"></td>
              <td th:text="${travellerTemp.ExpireDate}"></td>
              <td th:text="${travellerTemp.DateOfBirth}"></td>
            </tr>
            </tbody>
          </table>

        </div>
        <div class="frame-5">
          <div class="highLight-4">
            <div class="frame-3">
              <div class="overlap-highLight-wrapper">
                <div class="div-wrapper"><div class="text-wrapper-4">2</div></div>
              </div>
              <img class="ic-round-hotel" src="img/hotel.svg" />
              <div class="text-wrapper-5">Hotels</div>
            </div>
          </div>

        </div>
        <img class="vector-11" src="img/vector-2-2.svg" />
        <img class="vector-12" src="img/vector-2-2.svg" />
        <div class="frame-6" >
          <div class="frame-7" th:each="hotel : ${hotels}">
            <div class="frame-8">
              <div class="frame-9">
                <div class="frame-10"><div class="text-wrapper-16" th:text="${hotel.name}"></div></div>
              </div>
              <div class="frame-11">
                <div class="frame-10"><div class="text-wrapper-18">Price</div></div>
               </div>
            </div>
            <div class="frame-8" >
              <div class="frame-9">
                <div class="frame-10"><div class="text-wrapper-18">Rooms Details</div></div>
                <div class="text-wrapper-20" th:each="room : ${hotel.rooms}">
                 <span th:text="${room.name}"></span> <span th:text="${room.optionName}"></span>
                </div>
              </div>
              <div class="frame-9">
                <div class="text-wrapper-21" th:each="room : ${hotel.rooms}" >
                   <span th:text="${room.price}"></span>
                </div>
              </div>
              <div class="frame-12"></div>
            </div>
          </div>

        </div>
        <div class="highLight-5">

          <div class="frame-15">
            <div class="text-wrapper-5">Summary</div>
            <div class="frame-8">
              <div class="frame-10"><div class="text-wrapper-21">Flights</div></div>
              <div class="text-wrapper-21" th:text="${flights}"></div>
            </div>
            <div class="frame-8">
              <div class="frame-10"><div class="text-wrapper-21">Hotels</div></div>
              <div class="text-wrapper-21" th:text="${hotelsPrice}"></div>
            </div>
            <img class="vector-13" src="img/vector-5-3.svg" />
            <div class="frame-8">
              <div class="frame-10"><div class="text-wrapper-24">Total</div></div>
              <div class="text-wrapper-24" th:text="${totalPrice}"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
