package com.hb.crm.client.dto.FollowMeDto;

import java.math.BigDecimal;
import java.util.List;

public class PriceConfirmationResult {
    private boolean success;
    private String message;
    
    // Flight pricing details
    private List<String> flightConfirmPriceReferenceIds;
    private BigDecimal totalFlightPrice;
    private BigDecimal flightServiceFees;
    private BigDecimal flightTaxes;
    
    // Hotel pricing details  
    private BigDecimal totalHotelPrice;
    private BigDecimal hotelServiceFees;
    private BigDecimal hotelTaxes;
    
    // Total pricing breakdown
    private BigDecimal subtotalPrice; // flights + hotels
    private BigDecimal totalServiceFees; // flight + hotel service fees
    private BigDecimal totalTaxes; // flight + hotel taxes
    private BigDecimal finalTotalPrice; // subtotal + fees + taxes
    
    // Expiry information
    private String earliestExpiryDate;
    
    // Constructors
    public PriceConfirmationResult() {}
    
    public PriceConfirmationResult(boolean success, String message) {
        this.success = success;
        this.message = message;
    }

    // Getters and setters
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<String> getFlightConfirmPriceReferenceIds() {
        return flightConfirmPriceReferenceIds;
    }

    public void setFlightConfirmPriceReferenceIds(List<String> flightConfirmPriceReferenceIds) {
        this.flightConfirmPriceReferenceIds = flightConfirmPriceReferenceIds;
    }

    public BigDecimal getTotalFlightPrice() {
        return totalFlightPrice;
    }

    public void setTotalFlightPrice(BigDecimal totalFlightPrice) {
        this.totalFlightPrice = totalFlightPrice;
    }

    public BigDecimal getFlightServiceFees() {
        return flightServiceFees;
    }

    public void setFlightServiceFees(BigDecimal flightServiceFees) {
        this.flightServiceFees = flightServiceFees;
    }

    public BigDecimal getFlightTaxes() {
        return flightTaxes;
    }

    public void setFlightTaxes(BigDecimal flightTaxes) {
        this.flightTaxes = flightTaxes;
    }

    public BigDecimal getTotalHotelPrice() {
        return totalHotelPrice;
    }

    public void setTotalHotelPrice(BigDecimal totalHotelPrice) {
        this.totalHotelPrice = totalHotelPrice;
    }

    public BigDecimal getHotelServiceFees() {
        return hotelServiceFees;
    }

    public void setHotelServiceFees(BigDecimal hotelServiceFees) {
        this.hotelServiceFees = hotelServiceFees;
    }

    public BigDecimal getHotelTaxes() {
        return hotelTaxes;
    }

    public void setHotelTaxes(BigDecimal hotelTaxes) {
        this.hotelTaxes = hotelTaxes;
    }

    public BigDecimal getSubtotalPrice() {
        return subtotalPrice;
    }

    public void setSubtotalPrice(BigDecimal subtotalPrice) {
        this.subtotalPrice = subtotalPrice;
    }

    public BigDecimal getTotalServiceFees() {
        return totalServiceFees;
    }

    public void setTotalServiceFees(BigDecimal totalServiceFees) {
        this.totalServiceFees = totalServiceFees;
    }

    public BigDecimal getTotalTaxes() {
        return totalTaxes;
    }

    public void setTotalTaxes(BigDecimal totalTaxes) {
        this.totalTaxes = totalTaxes;
    }

    public BigDecimal getFinalTotalPrice() {
        return finalTotalPrice;
    }

    public void setFinalTotalPrice(BigDecimal finalTotalPrice) {
        this.finalTotalPrice = finalTotalPrice;
    }

    public String getEarliestExpiryDate() {
        return earliestExpiryDate;
    }

    public void setEarliestExpiryDate(String earliestExpiryDate) {
        this.earliestExpiryDate = earliestExpiryDate;
    }
}
