package com.hb.crm.client.dto.users;

import com.hb.crm.client.dto.MediaWrapperDto;
import com.hb.crm.client.dto.PackageDtos.PackageDto;
import com.hb.crm.client.dto.posts.PostDto;
import com.hb.crm.core.Enums.Gender;
import com.hb.crm.core.beans.Place;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Setter
@Getter
public class InfluencerUserDto {
    private String id;
    private String about;
    private String firstName;
    private String lastName;
    private String username;
    private String email;
    private String mobile;
    private String city;
    private String country;
    private int countriesCount;
    private List<PackageDto> packages;
    private List<InfluencerUserDto> Influencers;
    private List<MediaWrapperDto> medias;
    private int follwerscount = 0;
    private int followingcount = 0;
    private String profileImage;
    private String coverImage;
    private Gender gender;
    private List<Place> places;
    private List<PostDto> posts;
    private List<PostDto> postsOnly;
    private List<PostDto> stories;
    private LocalDateTime birthDate;
    private long packagesCount;
    private boolean hasLiveStream;
    private String liveStreamId;
    private String liveStreamPlaybackUrl;

    private Boolean followedByMe;

    public int getPackageCount() {
        if (this.packages != null)
            return this.packages.size();
        return 0;
    }

    public int getFollowsCount() {
        return this.getFollowingcount();
    }
}
