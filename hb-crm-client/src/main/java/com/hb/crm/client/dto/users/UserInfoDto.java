package com.hb.crm.client.dto.users;

import com.hb.crm.client.dto.MediaWrapperDto;
import com.hb.crm.core.Enums.Gender;
import com.hb.crm.core.Enums.UserType;
import lombok.Data;
import org.springframework.data.annotation.Id;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class UserInfoDto {
    @Id
    private String id;
    private String firstName;
    private String lastName;
    private String username;
    private UserType usertype;
    private String guestEmail;
    private int follwerscount;
    private boolean FollwedByMe;
    private String ProfileImage;
    private String coverImage;
    private List<MediaWrapperDto> medias;
    private LocalDateTime birthDate;
    private String about;
    private String city;
    private String country;
    private Gender gender;
    private boolean privateProfile = false;
    private boolean hasLiveStream = false;
    private String liveStreamId;
    private String liveStreamPlaybackUrl;
}
