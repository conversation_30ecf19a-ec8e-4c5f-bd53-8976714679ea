package com.hb.crm.client.services;

import com.hb.crm.client.CommonService.CommonUtils;
import com.hb.crm.client.CommonService.Mapper;
import com.hb.crm.client.CommonService.PaginationUtil;
import com.hb.crm.client.beans.UserSessionData;
import com.hb.crm.client.config.CustomHundlerar.CustomException;
import com.hb.crm.client.config.DateFormatter;
import com.hb.crm.core.dtos.CreateMediaDto;
import com.hb.crm.client.dto.PageDto;
import com.hb.crm.core.dtos.RefranceModelDto;
import com.hb.crm.client.dto.Results.CommentResultDto;
import com.hb.crm.client.dto.Results.PostResultDto;
import com.hb.crm.client.dto.Results.SplashUserStoriesResultDto;
import com.hb.crm.client.dto.posts.*;
import com.hb.crm.client.dto.splashScreen.splashUserStories;
import com.hb.crm.client.dto.users.SimpleUserinfoDto;
import com.hb.crm.client.services.interfaces.*;
import com.hb.crm.core.CombinedKeys.viewPostKey;
import com.hb.crm.core.Enums.*;
import com.hb.crm.core.beans.*;
import com.hb.crm.core.repositories.*;
import com.hb.crm.core.searchBeans.*;
import com.hb.crm.core.searchRepositories.ReactionSearchRepository;
import com.hb.crm.core.searchRepositories.SearchPostRepository;
import com.hb.crm.core.searchRepositories.SearchStoryRepository;
import com.hb.crm.core.services.interfaces.NotificationService;
import com.hb.crm.core.util.ApplicationUtil;
import org.bson.types.ObjectId;
import org.joda.time.DateTime;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PostServiceImpl implements PostService {

    private static final int ALL_LIMIT = 99999;
    private final PostRepository postRepository;
    private final PostViewsRepository postViewsRepository;
    private final CommentRepository commentRepository;
    private final CommentReactionRepository commentReactionRepository;
    private final PostReactionRepository postReactionRepository;
    private final ReplyReactionRepository replyReactionRepository;
    private final ReplyRepository replyRepository;
    private final MediaRepository mediaRepository;
    private final MongoTemplate mongoTemplate;
    private final MongoTemplate mongoTemplate2;
    private final UserRepository userRepository;
    private final SettingService settingService;
    private final UserSessionData userSessionData;
    private final TagService tagService;
    private final MediaService mediaService;
    private final SubPackageRepository subPackageRepository;
    private final QueryNormalizeService queryNormalizeService;
    private final CommonUtils commonUtils;
    private final Mapper mapper;
    private final SearchStoryRepository searchStoryRepository;
    private final SearchPostRepository searchPostRepository;
    private final ReactionSearchRepository reactionSearchRepository;
    private final MediaReactionRepository mediaReactionRepository;
    private final NotificationService notificationService;
    private Logger logger = LoggerFactory.getLogger(PostServiceImpl.class);

    public PostServiceImpl(PostRepository postRepository,
                           PostViewsRepository postViewsRepository, CommentRepository commentRepository,
                           CommentReactionRepository commentReactionRepository,
                           PostReactionRepository postReactionRepository,
                           ReplyReactionRepository replyReactionRepository,
                           ReplyRepository replyRepository,
                           MediaRepository mediaRepository, @Qualifier("mongoTemplate1") MongoTemplate mongoTemplate,
                           @Qualifier("mongoTemplate2") MongoTemplate mongoTemplate2, UserRepository userRepository, SettingService settingService,
                           UserSessionData userSessionData,
                           TagService tagService, MediaService mediaService, SubPackageRepository subPackageRepository
            , QueryNormalizeService queryNormalizeService, CommonUtils commonUtils, Mapper mapper
            , SearchStoryRepository searchStoryRepository, SearchPostRepository searchPostRepository, ReactionSearchRepository reactionSearchRepository, MediaReactionRepository mediaReactionRepository, NotificationService notificationService) {

        this.postRepository = postRepository;
        this.postViewsRepository = postViewsRepository;
        this.commentRepository = commentRepository;
        this.commentReactionRepository = commentReactionRepository;
        this.postReactionRepository = postReactionRepository;
        this.replyReactionRepository = replyReactionRepository;
        this.replyRepository = replyRepository;
        this.mediaRepository = mediaRepository;
        this.mongoTemplate = mongoTemplate;
        this.mongoTemplate2 = mongoTemplate2;
        this.userRepository = userRepository;
        this.settingService = settingService;
        this.userSessionData = userSessionData;
        this.mapper = mapper;
        this.tagService = tagService;
        this.mediaService = mediaService;
        this.subPackageRepository = subPackageRepository;
        this.queryNormalizeService = queryNormalizeService;
        this.commonUtils = commonUtils;
        this.searchStoryRepository = searchStoryRepository;
        this.searchPostRepository = searchPostRepository;
        this.reactionSearchRepository = reactionSearchRepository;
        this.mediaReactionRepository = mediaReactionRepository;
        this.notificationService = notificationService;
    }

    @Override
    public Page<PostDto> search() {
        final Pageable pageable = ApplicationUtil.createPageRequest(0, 100, "creationDate", "DESC");
        List<PostDto> Posts = postRepository.findAll().stream()
                .filter(post -> post.getPostStatus().equals(PackageStatus.posted))
                .map(Post -> mapper.convertToDto(Post)).collect(Collectors.toList());
        final Page<PostDto> page = new PageImpl<>(Posts.subList(0, Posts.size()), pageable, Posts.size());
        return page;
    }


    @Override
    public String addPostBookmark(String PostId) {
        String id = userSessionData.getId();
        Aggregation query = queryNormalizeService.getUserFields(Criteria.where("_id").is(new ObjectId(id)));
        User user = mongoTemplate.aggregate(query, "user", User.class).getMappedResults().stream().findAny().orElse(null);
        if (user != null) {
            List<Post> PostBookMarks = user.getPostBookMark();
            if (PostBookMarks == null)
                PostBookMarks = new ArrayList<Post>();
            Post post = PostBookMarks.stream().filter(customer -> PostId.equals(customer.getId()))
                    .findAny()
                    .orElse(null);
            if (post == null)
                PostBookMarks.add(new Post(PostId));
            user.setPostBookMark(PostBookMarks);
            userRepository.save(user);
        }
        return PostId;
    }


    @Override
    public void delete(String id) {
        final Optional<Post> byId = postRepository.findById(id);
        if (!byId.isEmpty()) {
            Post post = byId.get();
            if (post.getUser().getId().equals(userSessionData.getId()))
                postRepository.deleteById(id);
                searchStoryRepository.deleteById(id);
                searchPostRepository.deleteById(id);
        }
    }

    @Override
    public String viewStory(String StoryId) {

        String id = userSessionData.getId();
        Optional<Post> optionalPost = postRepository.findById(StoryId);

        if (!optionalPost.isEmpty()) {
            Post post = optionalPost.get();
            searchStory searchStory = searchStoryRepository.findById(StoryId).get();
            final Aggregation aggregation = queryNormalizeService.getPostViewFields(Criteria.where("_id.story._id").is(new ObjectId(StoryId)));
            List<PostViews> views = mongoTemplate.aggregate(aggregation, "postViews", PostViews.class).getMappedResults();
            PostViews view = views.stream().filter(z -> z.getId().getUser().getId().equals(id)).findFirst().orElse(null);
            if (view == null) {
                view = new PostViews();
                viewPostKey key = new viewPostKey();
                key.setStory(post);
                key.setUser(new User(id));
                view.setId(key);
                postViewsRepository.save(view);
                post.setViewsCount(views.size() + 1);
                searchStory.setViewsCount(views.size() + 1);
                searchStoryRepository.save(searchStory);
                postRepository.save(post);
                return StoryId;
            }
            return StoryId;

        }
        return StoryId;

    }

    @Override
    public PageDto<SimpleUserinfoDto> getStoryViewers(String storyId, int pageNumber, int itemsPerPage, String search) {
        PageDto<SimpleUserinfoDto> page = new PageDto<>();
        final Pageable pageable = PageRequest.of(pageNumber, itemsPerPage);

        Query SeQuery = new Query();

        Criteria criteria = new Criteria("id.story.id").is(storyId);
        SeQuery.addCriteria(criteria);
        SeQuery.with(pageable);
        List<SimpleUserinfoDto> users = mongoTemplate.find(SeQuery, PostViews.class).stream()
                .map(PostViews::getId)
                .map(viewPostKey::getUser)
                .filter(z -> z != null)
                .map(z -> mapper.convertToSimpleDto(z))
                .collect(Collectors.toList());
        page.setItems(users);
        if (search != null && !search.isEmpty()) {
            users = users.stream().filter(z -> z != null && (z.getFirstName() != null && z.getFirstName().startsWith(search) || z.getLastName() != null && z.getLastName().startsWith(search)))
                    .collect(Collectors.toList());
            page.setTotalNoOfItems(users.size());
            int start = (int) pageable.getOffset();
            int end = Math.min((start + pageable.getPageSize()), users.size());
            if (start > end) {
                page.setTotalNoOfItems(0);
                page.setItems(new ArrayList<>());
            } else {
                List<SimpleUserinfoDto> pagedUsers = users.subList(start, end);
                page.setItems(pagedUsers);
            }
        } else {
            long count = mongoTemplate.count(SeQuery, PostViews.class);
            page.setTotalNoOfItems(count);
        }

        return page;
    }

    public void sendNotificationCommentPost(Post post, List<PostReaction> reactions, User commentedUser, Comment comment) {
        for (PostReaction reaction : reactions) {
            var reactUser = reaction.getUser();
            // Send notification to user to inform him with status change
            notificationService.sendAndStoreNotification(post.getId(), NotificationType.CommentedOnLikedPost, reactUser,
                    List.of(commentedUser, post, comment),
                    commentedUser.getProfileImage(),
                    null,
                    NotificationEntityType.POST,
                    commentedUser,
                    post.getSlug(),
                    commentedUser.getUsername(), null);
        }
    }

    @Override
    public String addComment(String PostId, CommentDto commentdto) {
        ModelMapper modelMapper = new ModelMapper();
        String id = userSessionData.getId();
        User user = userRepository.findById(id)
                .orElseThrow(() -> new CustomException(404, "User not found!"));
        Post post = postRepository.findById(PostId)
                .orElseThrow(() -> new CustomException(404, "Post not found!"));

        searchStory searchStory = new searchStory();
        searchPost searchPost = new searchPost();
        if (post.getPostType().equals(PostType.Story))
            searchStory = searchStoryRepository.findById(PostId).orElse(null);
        if (post.getPostType().equals(PostType.Post))
            searchPost = searchPostRepository.findById(PostId).orElse(null);

        Comment comment = modelMapper.map(commentdto, Comment.class);
        comment.setUser(new User(id));
        comment.setPost(post);
        comment.setCreatedDate(DateFormatter.formatLocalDateTime(LocalDateTime.now()));
        commentRepository.save(comment);
        final Aggregation countAggregation = queryNormalizeService.getPostComments(Criteria.where("post._id").is(new ObjectId(PostId)), null);
        AggregationResults<CommentDto> Results = mongoTemplate.aggregate(countAggregation, "comment", CommentDto.class);
        int totalCount = Results.getMappedResults().size();
        post.setCommentsCount(totalCount);
        if (searchStory!= null && post.getPostType().equals(PostType.Story)) {
            searchStory.setCommentsCount(totalCount);
            searchStoryRepository.save(searchStory);
        }
        if (searchPost != null && post.getPostType().equals(PostType.Post)) {
            searchPost.setCommentsCount(totalCount);
            searchPostRepository.save(searchPost);
        }
        postRepository.save(post);

        Aggregation aggregation = queryNormalizeService.getPostReactionFields(
                Criteria.where("post._id").is(new ObjectId(PostId)));
        List<PostReaction> reactions = mongoTemplate.aggregate(aggregation, "postReaction", PostReaction.class)
                .getMappedResults();


        // Send notification to the tracked users
        sendNotificationCommentPost(post, reactions, user, comment);


        // Send notification to the owner of the post
        notificationService.sendAndStoreNotification(post.getId(), NotificationType.CommentedOnMyPost,
                post.getUser(), List.of(user, post, comment), user.getProfileImage(),
                null,
                NotificationEntityType.POST,
                comment.getUser(),
                post.getSlug(),
                user.getUsername(), null);

        return PostId;
    }

    @Override
    public String deleteComment(String CommentId) {
        String id = userSessionData.getId();
        Optional<Comment> optionalComment = commentRepository.findById(CommentId);
        if (!optionalComment.isEmpty()) {
            Comment comment = optionalComment.get();
            if (comment.getUser().getId().equals(id)) {
                Optional<Post> optionalPost = postRepository.findById(comment.getPost().getId());
                Post post = optionalPost.get();
                searchStory searchStory = new searchStory();
                searchPost searchPost = new searchPost();

                if (post.getPostType().equals(PostType.Story))
                    searchStory = searchStoryRepository.findById(comment.getPost().getId()).get();
                if (post.getPostType().equals(PostType.Post))
                    searchPost = searchPostRepository.findById(comment.getPost().getId()).get();
                if (post.getCommentsCount() - 1 > 0) {
                    post.setCommentsCount(post.getCommentsCount() - 1);
                    if (post.getPostType().equals(PostType.Story))
                        searchStory.setCommentsCount(post.getCommentsCount() - 1);
                    if (post.getPostType().equals(PostType.Post))
                        searchPost.setCommentsCount(post.getCommentsCount() - 1);
                } else {
                    if (post.getPostType().equals(PostType.Story))
                        searchStory.setCommentsCount(0);
                    if (post.getPostType().equals(PostType.Post))
                        searchPost.setCommentsCount(0);
                    post.setCommentsCount(0);
                }
                postRepository.save(post);
                if (post.getPostType().equals(PostType.Story))
                    searchStoryRepository.save(searchStory);
                if (post.getPostType().equals(PostType.Post))
                    searchPostRepository.save(searchPost);
                commentRepository.delete(comment);
            }
        }
        return CommentId;
    }

    @Override
    public String removePostBookmark(String PostId) {
        String id = userSessionData.getId();
        Aggregation query = queryNormalizeService.getUserFields(Criteria.where("_id").is(new ObjectId(id)));
        User user = mongoTemplate.aggregate(query, "user", User.class).getMappedResults().stream().findAny().orElse(null);
        if (user != null) {
            List<Post> posts = user.getPostBookMark();
            if (posts == null)
                posts = new ArrayList<Post>();
            Post post = posts.stream().filter(item -> PostId.equals(item.getId()))
                    .findAny()
                    .orElse(null);
            if (post != null)
                posts.remove(post);
            user.setPostBookMark(posts);
            userRepository.save(user);
        }
        return PostId;
    }

    @Override
    public PageDto<PostDto> search(Map<String, Object> obj, int page, int limit) {
        if (page < 0) {
            limit = ALL_LIMIT;
            page = 0;
        }
        PageDto<PostDto> pageDto = new PageDto<>();
        final Pageable pageable = PageRequest.of(page, limit);
        Criteria criteria = createSearchSpecification(obj);
        criteria.and("postStatus").is(PackageStatus.posted);
        Sort sort = Sort.by(Sort.Direction.DESC, "update");
        final Aggregation aggregation = queryNormalizeService.getPostFields(criteria, pageable, sort);
        AggregationResults<PostResultDto> countResults = mongoTemplate.aggregate(aggregation, "post", PostResultDto.class);
        List<PostDto> Posts = countResults.getUniqueMappedResult().getFilteredResults();
        long totalCount = countResults.getUniqueMappedResult() != null ? countResults.getUniqueMappedResult().getTotalCount() : 0;
        pageDto.setTotalNoOfItems(totalCount);
        pageDto.setItems(Posts);
        return pageDto;
    }

    @Override
    public String reactPostOrStory(String PostId, ReactionType reactionType) {
        String id = userSessionData.getId();
        this.PostReact(true, PostId, id, reactionType);
        return PostId;
    }

    @Override
    public String reactComment(String CommentId, ReactionType reactionType) {
        String id = userSessionData.getId();
        Optional<Comment> optionalComment = commentRepository.findById(CommentId);
        Comment comment = optionalComment.get();
        CommentReaction reaction = new CommentReaction();
        reaction.setUser(new User(id));
        reaction.setReactionType(reactionType);
        reaction.setComment(comment);
        Aggregation aggregation = queryNormalizeService.getCommentReactionFields(Criteria.where("comment._id").is(new ObjectId(CommentId)));
        List<CommentReaction> reactions = mongoTemplate.aggregate(aggregation, "commentReaction", CommentReaction.class).getMappedResults();
        ReactionSearch reactionSearch = new ReactionSearch();
        CommentReaction myReact = reactions.stream().filter(_react -> _react.getUser().getId().equals(id)).findAny()
                .orElse(null);
        if (myReact == null) {
            comment.setNumberOfReactions(reactions.size() + 1);
            commonUtils.CreateReaction(comment.getId(), id, ReactionType.like, EntityName.Comment, EntityType.React);
            commentRepository.save(comment);
            commentReactionRepository.save(reaction);
        } else {
            reactionSearch = commonUtils.getReaction(id, comment.getId(), EntityType.React);
            if (reactionSearch != null) {
                reactionSearch.setReactionType(reactionType);
                reactionSearchRepository.save(reactionSearch);
            } else {
                commonUtils.CreateReaction(comment.getId(), id, ReactionType.like, EntityName.Comment, EntityType.React);
            }
            reaction.setId(myReact.getId());
            commentReactionRepository.save(reaction);
        }
        return CommentId;
    }

    @Override
    public String reactMedia(String MediaId, ReactionType reactionType) {
        String id = userSessionData.getId();
        Optional<Media> optionalComment = mediaRepository.findById(MediaId);
        Media media = optionalComment.get();
        MediaReaction reaction = new MediaReaction();
        reaction.setUser(new User(id));
        reaction.setReactionType(reactionType);
        reaction.setMedia(media);
        Aggregation aggregation = queryNormalizeService.getMediaReactionFields(Criteria.where("media._id").is(new ObjectId(MediaId)));
        List<MediaReaction> reactions = mongoTemplate.aggregate(aggregation, "mediaReaction", MediaReaction.class).getMappedResults();
        ReactionSearch reactionSearch = new ReactionSearch();
        MediaReaction myReact = reactions.stream().filter(_react -> _react.getUser().getId().equals(id)).findAny()
                .orElse(null);
        if (myReact == null) {
            media.setNumberOfReactions(reactions.size() + 1);
            commonUtils.CreateReaction(media.getId(), id, ReactionType.like, EntityName.Media, EntityType.React);
            mediaRepository.save(media);
            mediaReactionRepository.save(reaction);
        } else {
            reactionSearch = commonUtils.getReaction(id, media.getId(), EntityType.React);
            if (reactionSearch != null) {
                reactionSearch.setReactionType(reactionType);
                reactionSearchRepository.save(reactionSearch);
            } else {
                commonUtils.CreateReaction(media.getId(), id, ReactionType.like, EntityName.Media, EntityType.React);
            }
            reaction.setId(myReact.getId());
            mediaReactionRepository.save(reaction);
        }
        return MediaId;
    }

    @Override
    public String unReactPostOrStory(String PostId) {
        String id = userSessionData.getId();
        this.PostReact(false, PostId, id, null);
        return PostId;
    }

    @Transactional
    public void PostReact(boolean React, String PostId, String UserId, ReactionType reactionType) {
        if (React) {

            PostReaction react = new PostReaction();
            searchStory searchStory = new searchStory();
            searchPost searchPost = new searchPost();

            Optional<Post> optionalPost = postRepository.findById(PostId);
            if (optionalPost.get() != null) {
                Post post = optionalPost.get();
                if (post.getPostType().equals(PostType.Story))
                    searchStory = searchStoryRepository.findById(PostId).get();
                if (post.getPostType().equals(PostType.Post))
                    searchPost = searchPostRepository.findById(PostId).get();
                react.setUser(new User(UserId));
                react.setReactionType(reactionType);
                react.setPost(post);
                Aggregation aggregation = queryNormalizeService.getPostReactionFields(Criteria.where("post._id").is(new ObjectId(PostId)));
                List<PostReaction> reactions = mongoTemplate.aggregate(aggregation, "postReaction", PostReaction.class).getMappedResults();
                PostReaction myReact = reactions.stream().filter(z -> z.getUser().getId().equals(UserId))
                        .findAny().orElse(null);

                ReactionSearch reactionSearch = new ReactionSearch();
                if (myReact == null) {

                    post.setReactsCount(reactions.size() + 1);
                    if (post.getPostType().equals(PostType.Story))
                        searchStory.setReactsCount(reactions.size() + 1);
                    if (post.getPostType().equals(PostType.Post))
                        searchPost.setReactsCount(reactions.size() + 1);

                    commonUtils.CreateReaction(PostId, UserId, ReactionType.like, EntityName.Post, EntityType.React);
                    postReactionRepository.save(react);
                } else {
                    reactionSearch = commonUtils.getReaction(UserId, PostId, EntityType.React);
                    if (reactionSearch != null) {
                        reactionSearch.setReactionType(reactionType);
                        reactionSearchRepository.save(reactionSearch);
                    } else {
                        commonUtils.CreateReaction(PostId, UserId, ReactionType.like, EntityName.Post, EntityType.React);
                    }
                    react.setId(myReact.getId());
                    postReactionRepository.save(react);
                }
                postRepository.save(post);
                if (post.getPostType().equals(PostType.Story))
                    searchStoryRepository.save(searchStory);
                if (post.getPostType().equals(PostType.Post))
                    searchPostRepository.save(searchPost);
            }

        } else {
            Optional<Post> optionalPost = postRepository.findById(PostId);
            if (optionalPost.get() != null) {
                Post post = optionalPost.get();
                searchStory searchStory = new searchStory();
                searchPost searchPost = new searchPost();

                if (post.getPostType().equals(PostType.Story))
                    searchStory = searchStoryRepository.findById(PostId).get();
                if (post.getPostType().equals(PostType.Story))
                    searchPost = searchPostRepository.findById(PostId).get();
                Aggregation aggregation = queryNormalizeService.getPostReactionFields(Criteria.where("post._id").is(new ObjectId(PostId)));
                List<PostReaction> reactions = mongoTemplate.aggregate(aggregation, "postReaction", PostReaction.class).getMappedResults();
                PostReaction react = reactions.stream().filter(z -> z.getUser().getId().equals(UserId))
                        .findAny().orElse(null);

                ReactionSearch reactionSearch = commonUtils.getReaction(UserId, PostId, EntityType.React);
                if (reactionSearch != null)
                    reactionSearchRepository.delete(reactionSearch);
                if (react != null) {
                    postReactionRepository.delete(react);
                    if (reactions.size() > 0) {
                        post.setReactsCount(reactions.size() - 1);
                        if (post.getPostType().equals(PostType.Story))
                            searchStory.setReactsCount(reactions.size() - 1);
                        if (post.getPostType().equals(PostType.Post))
                            searchPost.setReactsCount(reactions.size() - 1);
                    } else {
                        post.setReactsCount(0);
                        if (post.getPostType().equals(PostType.Story))
                            searchStory.setReactsCount(0);
                        if (post.getPostType().equals(PostType.Post))
                            searchPost.setReactsCount(0);

                    }
                }
                if (post.getPostType().equals(PostType.Story))
                    searchStoryRepository.save(searchStory);
                if (post.getPostType().equals(PostType.Post))
                    searchPostRepository.save(searchPost);
                postRepository.save(post);
            }
        }
    }

    @Override
    public String unReactComment(String CommentId) {

        Optional<Comment> OptionalComment = commentRepository.findById(CommentId);
        Comment comment = OptionalComment.get();
        Aggregation aggregation = queryNormalizeService.getCommentReactionFields(Criteria.where("comment._id").is(new ObjectId(CommentId)));
        List<CommentReaction> reactions = mongoTemplate.aggregate(aggregation, "commentReaction", CommentReaction.class).getMappedResults();

        CommentReaction myReact = reactions.stream().filter(_react -> _react.getUser().getId().equals(userSessionData.getId())).findAny()
                .orElse(null);
        if (myReact != null && myReact.getUser().getId().equals(userSessionData.getId())) {
            commentReactionRepository.delete(myReact);
            if (reactions.size() > 0) {
                comment.setNumberOfReactions(reactions.size() - 1);
            } else {
                comment.setNumberOfReactions(0);
            }
        }
        commentRepository.save(comment);

        return CommentId;

    }

    @Override
    public String unReactMedia(String MediaId) {

        Optional<Media> OptionalMedia = mediaRepository.findById(MediaId);
        Media media = OptionalMedia.get();
        Aggregation aggregation = queryNormalizeService.getMediaReactionFields(Criteria.where("media._id").is(new ObjectId(MediaId)));
        List<MediaReaction> reactions = mongoTemplate.aggregate(aggregation, "mediaReaction", MediaReaction.class).getMappedResults();

        MediaReaction myReact = reactions.stream().filter(_react -> _react.getUser().getId().equals(userSessionData.getId())).findAny()
                .orElse(null);
        if (myReact != null && myReact.getUser().getId().equals(userSessionData.getId())) {
            mediaReactionRepository.delete(myReact);
            if (reactions.size() > 0) {
                media.setNumberOfReactions(reactions.size() - 1);
            } else {
                media.setNumberOfReactions(0);
            }
        }
        mediaRepository.save(media);

        return MediaId;

    }

    @Override
    public String addReply(String CommentId, ReplyDto replyDto) {
        ModelMapper modelMapper = new ModelMapper();
        String id = userSessionData.getId();
        var useropt = userRepository.findById(id);
        if (!id.isEmpty()) {
            Comment comment = commentRepository.findById(CommentId).orElse(null);
            if (comment != null) {
                Reply reply = modelMapper.map(replyDto, Reply.class);
                reply.setUser(new User(id));
                reply.setRefcomment(comment);
                reply.setCreatedDate(DateFormatter.formatLocalDateTime(LocalDateTime.now()));
                replyRepository.save(reply);
                comment.setNumberOfReplyes(comment.getNumberOfReplyes() + 1);
                commentRepository.save(comment);

                useropt.ifPresent(user -> notificationService.sendAndStoreNotification(
                        comment.getPost().getId(),
                        NotificationType.ReplyComment,
                        user,
                        List.of(comment, reply, comment.getPost()),
                        user.getProfileImage(),
                        null,
                        NotificationEntityType.POST,
                        user,
                        comment.getPost().getSlug(),
                        user.getUsername(), null));
            }
        }
        return CommentId;

    }

    @Override

    public String deleteReply(String CommentId, String ReplyId) {
        Comment comment = commentRepository.findById(CommentId).orElse(null);
        if (comment != null) {
            replyRepository.deleteById(ReplyId);
            if (comment.getNumberOfReplyes() - 1 > 0) {
                comment.setNumberOfReplyes(comment.getNumberOfReplyes() - 1);
            } else {
                comment.setNumberOfReplyes(0);
            }
            commentRepository.save(comment);
        }

        return CommentId;

    }

    @Override
    public String unReactReply(String ReplyId) {
        String id = userSessionData.getId();
        Aggregation query = queryNormalizeService.getUserFields(Criteria.where("_id").is(new ObjectId(id)));
        User user = mongoTemplate.aggregate(query, "user", User.class).getMappedResults().stream().findAny().orElse(null);
        if (user != null) {

            Reply reply = replyRepository.findById(ReplyId).orElse(null);

            Aggregation aggregation = queryNormalizeService.getReplyReactionFields(Criteria.where("reply._id").is(new ObjectId(ReplyId)));
            List<ReplyReaction> reactions = mongoTemplate.aggregate(aggregation, "replyReaction", ReplyReaction.class).getMappedResults();
            ReplyReaction myReact = reactions.stream()
                    .filter(_react -> _react.getUser().getId().equals(id)

                    ).findAny()
                    .orElse(null);


            if (myReact != null) {
                if (myReact.getUser().getId().equals(id))
                    replyReactionRepository.delete(myReact);
                if (reactions.size() > 0) {
                    reply.setNumberOfReactions(reactions.size() - 1);
                } else {
                    reply.setNumberOfReactions(0);
                }
            }
            replyRepository.save(reply);
        }
        return ReplyId;

    }

    @Override
    public String reactReply(String ReplyId, ReactionType reactionType) {
        String id = userSessionData.getId();
        Aggregation query = queryNormalizeService.getUserFields(Criteria.where("_id").is(new ObjectId(id)));
        User user = mongoTemplate.aggregate(query, "user", User.class).getMappedResults().stream().findAny().orElse(null);
        if (user != null) {
            Reply reply = replyRepository.findById(ReplyId).orElse(null);
            if (reply != null) {
                ReplyReaction reaction = new ReplyReaction();
                reaction.setUser(user);
                reaction.setReactionType(reactionType);
                reaction.setReply(reply);

                Aggregation aggregation = queryNormalizeService.getReplyReactionFields(Criteria.where("reply._id").is(new ObjectId(ReplyId)));
                List<ReplyReaction> reactions = mongoTemplate.aggregate(aggregation, "replyReaction", ReplyReaction.class).getMappedResults();
                Reaction myReact = reactions.stream()
                        .filter(_react ->
                                _react.getUser().getId().equals(user.getId()))
                        .findAny()
                        .orElse(null);

                if (myReact == null) {
                    replyReactionRepository.save(reaction);
                    reply.setNumberOfReactions(reactions.size() + 1);
                } else {
                    reaction.setId(myReact.getId());
                    replyReactionRepository.save(reaction);
                }
                replyRepository.save(reply);
            }
        }
        return ReplyId;

    }

    public PostDto getPostById(String id) {
        if (id == null) {
            return null;
        }
        final Optional<Post> byId = postRepository.findById(id);
        if (byId.isEmpty()) {
            return null;
        }
        PostDto post = mapper.convertToDto(byId.get());

        return post;
    }

    @Override
    @Transactional
    public void update(CreateUpdatePostDto obj) {

        if(obj.getMedia() == null || obj.getMedia().isEmpty()) {
            throw new CustomException(400, "At least one media item is required.");
        } else if (!obj.getMedia().isEmpty()) {
            for (var media :obj.getMedia()) {
                if(media.getMediaType().equals(MediaType.video)){
                    if(media.getVideoDurationMS()==null || media.getVideoDurationMS().equals(BigDecimal.ZERO)){
                        throw new CustomException(400, "Each video must have a duration greater than 0 milliseconds.");

                    }
                }
            }
        }
        String id = userSessionData.getId();

        if (obj.getTags() != null && !obj.getTags().isEmpty())
            obj.setTags(tagService.checkNewTags(obj.getTags()));

        var generatedMedia = generateMediaWithWrapper(obj.getMedia());
        obj.setMedia(new ArrayList<>());

        Post post = mapper.convertToEntity(obj);


        if(post == null)
            return;

        post.setMedia(generatedMedia);

        Aggregation query = queryNormalizeService.getUsersList(Criteria.where("_id").is(new ObjectId(id)));
        User user = mongoTemplate.aggregate(query, "user", User.class).getMappedResults().stream().findAny().orElse(null);
        Optional<SubPackage> sub = Optional.empty();

        if (obj.getPackage() != null && obj.getPackage().getId() != null && !obj.getPackage().getId().isEmpty()) {
            sub = subPackageRepository.findById(obj.getPackage().getId());
            if (sub.isEmpty()) {
                throw new CustomException(404, "There is no package with this Id");
            }
        }

        post.setPostStatus(PackageStatus.posted);
        if (user != null) {

            if(obj.getPostType().equals(PostType.Story)){
                // Fetch and validate user existence, then set it to the post

                Setting storySetting = settingService.getByName("Story-Limit-PerUser");
                int limit;
                if(storySetting.getValue()==null || storySetting.getValue().isEmpty()){
                    limit=-1;
                }else {
                    limit = Integer.parseInt(storySetting.getValue());
                }

                Criteria   criteria =Criteria.where("username").is(user.getUsername());
                Pageable pageable = PageRequest.of(0, 99999999);
                Aggregation aggregation=       queryNormalizeService.getUsersWithRecentStoriesSplash(criteria,null,99999999,pageable,false);
                var  Results = mongoTemplate.aggregate(aggregation, "user",
                        SplashUserStoriesResultDto.class);
                List<splashUserStories> users = Objects.requireNonNull(Results.getUniqueMappedResult()).getFilteredResults();
                if(!users.isEmpty() && limit > 0){
                    splashUserStories userStory=users.getFirst();
                    if(userStory.getStories().size()>=limit){
                        throw new CustomException(500, "User reached the max limit of added Stories");
                    }
                }
            }
            if(obj.getTaggedUsers()!=null && !obj.getTaggedUsers().isEmpty()) {
              List<ObjectId> usersId=obj.getTaggedUsers().stream().map(RefranceModelDto::getId).map(ObjectId::new).collect(Collectors.toList());
                Aggregation taggedquery = queryNormalizeService.getUsersList(Criteria.where("_id").in(usersId));
                List<User> users = mongoTemplate.aggregate(taggedquery, "user", User.class).getMappedResults();
                post.setTaggedUsers(users);
            }
            post.setUser(user);
            if (obj.getId() != null) {
                Post oldPost = postRepository.findById(obj.getId()).orElse(null);


                if(oldPost==null){
                    throw new CustomException(404,"Entity Not Found");

                }
                String   slug="";
                if(oldPost.getSlug() != null && !oldPost.getSlug().isEmpty()) {
                        slug= slugify(oldPost.getSlug());

                }else {
                        slug=user.getUsername()+"-"+(post.getPostType().equals(PostType.Story)? "Story":"Post");
                        slug= generateSlug(slug);

                }

                if (!Objects.equals(post.getUser().getId(), id))
                {
                    throw new CustomException(404,"only owner can update the entity");
                }
                if (obj.getPackage() != null && obj.getPackage().getId() != null && !obj.getPackage().getId().isEmpty())
                    post.setPackage(sub.get().get_package());
                post.setSlug(slug);
                post.setCreated(oldPost.getCreated());
                post.setUpdate((DateFormatter.formatLocalDateTime(LocalDateTime.now())));
            }
            else {

                String slug = user.getUsername()+"-"+(post.getPostType().equals(PostType.Story)? "Story":"Post");
                slug= generateSlug(slug);
                post.setSlug(slug);
                if (obj.getPackage() != null && obj.getPackage().getId() != null && !obj.getPackage().getId().isEmpty())
                    post.setPackage(sub.get().get_package());
                post.setCreated(DateFormatter.formatLocalDateTime(LocalDateTime.now()));
                post.setUpdate((DateFormatter.formatLocalDateTime(LocalDateTime.now())));
            }

            postRepository.save(post);
            logger.info("post Saved");
            simpleUserInfo simpleUserInfo = new simpleUserInfo();
            simpleUserInfo.setId(user.getId());
            simpleUserInfo.setFirstName(user.getFirstName());
            simpleUserInfo.setLastName(user.getLastName());
            simpleUserInfo.setProfileImage(user.getProfileImage());
            simpleUserInfo.setCoverImage(user.getCoverImage());
            logger.info("simpleUserInfo created");

            simplePackageInfo simplePackageInfo = new simplePackageInfo();
            if (sub.isPresent()) {
                logger.info("Package present");
                simplePackageInfo.setName(sub.get().getName());
                simplePackageInfo.setId(sub.get().getId());
            }
            logger.info("simplePackageInfo created");

            if (obj.getPostType() != null) {
                if (obj.getPostType().equals(PostType.Story)) {
                    logger.info("creating story index");
                    searchStory searchStorey = mapper.convertPostToSearchStory(post);
                    if (sub.isPresent())
                        searchStorey.setPackage(simplePackageInfo);
                    searchStorey.setUser(simpleUserInfo);
                    searchStoryRepository.save(searchStorey);
                } else {
                    searchPost searchPost = mapper.convertPostToSearchPost(post);
                    if (sub.isPresent())
                        searchPost.setPackage(simplePackageInfo);
                    searchPost.setUser(simpleUserInfo);
                    searchPostRepository.save(searchPost);
                }
            }

            List<Media> mediasToSave = new ArrayList<>();
            for (MediaWrapper media : post.getMedia()) {
                media.getMedia().setUser(user);
                media.getMedia().setPost(post);
                mediasToSave.add(media.getMedia());
            }

            mediaRepository.saveAll(mediasToSave);
            var savedPost = postRepository.save(post);

            var userFollowers = findFollowers(savedPost.getUser().getId());

            Optional<MediaWrapper> packageImageMedia = Optional.<MediaWrapper> empty();

            if(savedPost.getPackage() != null && savedPost.getPackage().getMedias() != null)
                packageImageMedia =  savedPost.getPackage()
                        .getMedias()
                        .stream()
                        .filter(MediaWrapper::isMainImage)
                        .findAny();

            // Send notification to followed influencers
            var packageImageMediaUrl = packageImageMedia.map(MediaWrapper::getUrl).orElse(null);
            for (User follower : userFollowers) {
                notificationService.sendAndStoreNotification(savedPost.getId(),
                        savedPost.getPostType().equals(PostType.Story) ? NotificationType.NewStoryFromFollowedInfluencer : NotificationType.NewPostFromFollowedInfluencer,
                        follower,
                        List.of(savedPost, user),
                        packageImageMediaUrl,
                        savedPost.getUser().getProfileImage(),
                        NotificationEntityType.POST,
                        savedPost.getUser(),
                        savedPost.getSlug(),
                        savedPost.getUser().getUsername(),
                        UserType.Influencer);
            }

            // Send notification to all users who have put the package of the post as favourite
            if(savedPost.getPackage() != null) {
                // Find all reactions that are "Favourite" type for the package of the post
                List<ReactionSearch> favouriteReactions = reactionSearchRepository.findByEntityNameAndEntityTypeAndEntityId(
                        EntityName.Package,
                        EntityType.Favourite,
                        savedPost.getPackage().getId()
                );

                // Extract the user's ids
                List<String> usersIds = favouriteReactions
                        .stream()
                        .map(ReactionSearch::getUserId)
                        .toList();

                // fetch users from the database
                List<User> users = userRepository.findAllById(usersIds);

                // Send notification to each user
                for (User favouritePackageUser : users) {
                notificationService.sendAndStoreNotification(
                        savedPost.getId(),
                        savedPost.getPostType().equals(PostType.Story) ? NotificationType.NewStoryFromFavouritePackageUser : NotificationType.NewPostFromFavouritePackageUser,
                        favouritePackageUser,
                        List.of(savedPost, user, post.getPackage()),
                        packageImageMediaUrl,
                        savedPost.getUser().getProfileImage(),
                        NotificationEntityType.POST,
                        savedPost.getUser(),
                        savedPost.getSlug(),
                        savedPost.getUser().getUsername(),
                        UserType.Influencer);
                }
            }
        }
    }



    public String generateSlug(String name) {
        String baseSlug = slugify(name); // Convert name to URL-friendly slug
        String slug = baseSlug;
        int counter = 1;

        while (!isSlugUnique(slug)) {
            slug = baseSlug + "-" + counter;
            counter++;
        }

        return slug;
    }

    public String slugify(String input) {
        return input.toLowerCase()
                .replaceAll("[^a-z0-9\\s-]", "")   // remove special characters
                .replaceAll("\\s+", "-")           // replace spaces with hyphens
                .replaceAll("-+", "-")             // replace multiple hyphens
                .replaceAll("^-|-$", "");           // trim hyphens from start/end
    }
    @Override
    public void TagUsers(List<RefranceModelDto> taggedUsers,String postId){
        Post post = postRepository.findById(postId).orElseThrow(()-> new CustomException(404,"post not found"));
        String id = userSessionData.getId();
        if (!Objects.equals(post.getUser().getId(), id))
        {
            throw new CustomException(404,"only owner can update the entity");
        }
        if( taggedUsers!=null && !taggedUsers.isEmpty()) {
            List<ObjectId> usersId=taggedUsers.stream().map(RefranceModelDto::getId).map(ObjectId::new).collect(Collectors.toList());
            Aggregation taggedquery = queryNormalizeService.getUsersList(Criteria.where("_id").in(usersId));
            List<User> users = mongoTemplate.aggregate(taggedquery, "user", User.class).getMappedResults();
            post.setTaggedUsers(users);
        }
        if (post.getPostType() != null) {
            if (post.getPostType().equals(PostType.Story)) {
                searchStory dummy = mapper.convertPostToSearchStory(post);

                searchStory searchStorey= searchStoryRepository.findById(postId).orElse(null);
                if(searchStorey==null) {
                    searchStorey = dummy;
                    logger.info("creating story index");
                    if (post.getPackage() != null && post.getPackage().getId() != null && !post.getPackage().getId().isEmpty()) {
                     var   sub = subPackageRepository.findById(post.getPackage().getId());
                        if (sub.isEmpty()) {
                            throw new CustomException(404, "There is no package with this Id");
                        }
                        simplePackageInfo simplePackageInfo = new simplePackageInfo();
                        simplePackageInfo.setName(sub.get().getName());
                        simplePackageInfo.setId(sub.get().getId());
                        searchStorey.setPackage(simplePackageInfo);

                    }

                }else {
                    searchStorey.setUser(dummy.getUser());
                }
                 searchStoryRepository.save(searchStorey);
            } else {
                searchPost dummy = mapper.convertPostToSearchPost(post);


                searchPost SearchPost= searchPostRepository.findById(postId).orElse(null);
                if(SearchPost==null) {
                    SearchPost = dummy;
                    logger.info("creating story index");
                    if (post.getPackage() != null && post.getPackage().getId() != null && !post.getPackage().getId().isEmpty()) {
                        var   sub = subPackageRepository.findById(post.getPackage().getId());
                        if (sub.isEmpty()) {
                            throw new CustomException(404, "There is no package with this Id");
                        }
                        simplePackageInfo simplePackageInfo = new simplePackageInfo();
                        simplePackageInfo.setName(sub.get().getName());
                        simplePackageInfo.setId(sub.get().getId());
                        SearchPost.setPackage(simplePackageInfo);

                    }

                }else {
                    SearchPost.setUser(dummy.getUser());
                }
                searchPostRepository.save(SearchPost);
            }
        }
    }

    /**
     * Adds reel media content to an existing post.
     * <p>
     * This method allows users to add reel-type media content to their posts.
     * It validates the user's authentication, retrieves the target post, converts
     * the provided media DTOs to the appropriate format, and updates the post
     * with the new media content.
     *
     * @param postId The unique identifier of the Post to which reels will be added.
     *               Must be a valid MongoDB ObjectId string representing an existing Post.
     * @param mediasDto A list of CreateMediaDto objects containing the media information to be added.
     *                  Each media item will be automatically set to MediaType.reel regardless of
     *                  their original mediaType value.
     *
     * @throws CustomException with status code 404 if:
     *                         <ul>
     *                           <li>The specified post is not found in the database</li>
     *                           <li>The current user is not the owner of the post</li>
     *                         </ul>
     *
     * <p>Processing Steps:
     * <ol>
     *   <li>Sets all media items to MediaType.reel type</li>
     *   <li>Validates user authentication and retrieves user information</li>
     *   <li>Retrieves the target post and validates ownership</li>
     *   <li>Converts media DTOs to MediaWrapper entities with proper relationships</li>
     *   <li>Adds new media to existing post media collection</li>
     *   <li>Updates media ownership and post relationships</li>
     *   <li>Persists changes to both media and post repositories</li>
     * </ol>
     *
     * @see CreateMediaDto
     * @see MediaType#reel
     * @see MediaWrapper
     * @see Post
     */
    @Override
    public void AddReels(String postId, List<CreateMediaDto> mediasDto) {
        // Set all media items to a reel type regardless of their original mediaType
        mediasDto.forEach(media -> media.setMediaType(MediaType.reel));

        // Build aggregation query to retrieve current user information with normalized fields
        Aggregation query = queryNormalizeService.getUsersList(Criteria.where("_id").is(new ObjectId(userSessionData.getId())));
        User user = mongoTemplate.aggregate(query, "user", User.class).getMappedResults().stream().findAny().orElse(null);

        // Proceed only if user authentication is successful
        if (user != null) {
            // Retrieve the target Post by ID, throw exception if not found
            Post post = postRepository.findById(postId).orElseThrow(() -> new CustomException(404, "Post not found"));

            // Validate that the current user is the owner of the post
            if (!Objects.equals(post.getUser().getId(), user.getId())) {
                throw new CustomException(404, "Only owner can update the entity");
            }

            // Convert the media DTOs to MediaWrapper entities
            List<MediaWrapper> newMediaWrappers = generateMediaWithWrapper(mediasDto);

            // Get the existing media list from the post
            List<MediaWrapper> existingMedia = post.getMedia();
            if (existingMedia == null) {
                existingMedia = new ArrayList<>();
            }

            // Add new media wrappers to the existing collection
            existingMedia.addAll(newMediaWrappers);
            post.setMedia(existingMedia);

            // Update media ownership and post relationships
            List<Media> mediasToSave = new ArrayList<>();
            for (MediaWrapper mediaWrapper : newMediaWrappers) {
                mediaWrapper.getMedia().setUser(user);
                mediaWrapper.getMedia().setPost(post);
                mediasToSave.add(mediaWrapper.getMedia());
            }

            // Batch save all media ownership updates
            mediaRepository.saveAll(mediasToSave);

            // Persist the updated post to the database
            postRepository.save(post);
        }
    }

    @Override
    public void reRequest(CreateUpdatePostDto obj) {
        String id = userSessionData.getId();
        obj.setTags(tagService.checkNewTags(obj.getTags()));

        var generatedMedia = generateMediaWithWrapper(obj.getMedia());
        obj.setMedia(new ArrayList<>());

        Post post = mapper.convertToEntity(obj);
        post.setMedia(generatedMedia);

        Optional<User> user = userRepository.findById(id);
        if (user.get() != null) {
            post.setUser(user.get());
            if (obj.getId() != null) {
                Post oldPost = postRepository.findById(obj.getId()).get();
                post.setCreated(oldPost.getCreated());
                post.setUpdate((DateFormatter.formatLocalDateTime(LocalDateTime.now())));
                post.setPostStatus(PackageStatus.returned);
            }
            postRepository.save(post);
        }
    }

    /**
     * Retrieves paginated bookmarked posts for a specific user or the current user.
     *
     * @param userId Optional user ID. If null, uses the current authenticated user's ID
     * @param page Zero-based page number
     * @param size Number of items per page
     * @return PageDto containing the bookmarked posts and pagination metadata
     * @throws CustomException if the specified user is not found with status code 404
     */
    @Override
    public PageDto<PostDto> GetBookMarkPosts(String userId, int page, int size) {
        PageDto<PostDto> pageDto = new PageDto<>();
        Pageable pageable = PageRequest.of(page, size);
        // Determine user ID
        String effectiveUserId = userId != null ? userId : userSessionData.getId();
        // Confirm user exists
        User user = userRepository.findById(effectiveUserId)
                .orElseThrow(() -> new CustomException(404, "User not found"));
        if(user.getPostBookMark()==null || user.getPostBookMark().isEmpty()) {
            pageDto.setTotalNoOfItems(0);
            pageDto.setItems( new ArrayList<>());
            return pageDto;
        }
       List<ObjectId> PostsId   = user.getPostBookMark().stream().map(Post::getId).map(ObjectId::new).toList();
        // Base match criteria
        Criteria criteria = Criteria.where("_id").in(PostsId);
        Sort sort = Sort.by(Sort.Direction.DESC, "update");
        final Aggregation aggregation = queryNormalizeService.getPostFields(criteria, pageable, sort);
        AggregationResults<PostResultDto> countResults = mongoTemplate.aggregate(aggregation, "post", PostResultDto.class);
        List<PostDto> Posts = Objects.requireNonNull(countResults.getUniqueMappedResult()).getFilteredResults();
        long totalCount = countResults.getUniqueMappedResult() != null ? countResults.getUniqueMappedResult().getTotalCount() : 0;
        pageDto.setTotalNoOfItems(totalCount);
        pageDto.setItems(Posts);
        return pageDto;
     }

    @Override
    public PageDto<searchStory> getHighlightedStories1(int pageNumber, int itemsPerPage) {
        long AggregationStart = System.currentTimeMillis(); //start of the function
        LocalDateTime pastDay = LocalDateTime.now().minusDays(1);
        Criteria criteria = new Criteria();
        criteria.and("update").gte(pastDay);
        PageDto<searchStory> stories = PaginationUtil.getPagedResult(mongoTemplate2, searchStory.class, criteria, pageNumber, itemsPerPage, "update", "DESC");
        long AggregationEnd = System.currentTimeMillis(); //start of the function
        long executionTime = AggregationStart - AggregationEnd;
        logger.info("aggregation executed in {} ms", executionTime);

        if (userSessionData.getId() != null) {
            //commonUtils.GetBookedPostsAndReacts(userSessionData.getId(), Posts);
        }
        long UserMatching = System.currentTimeMillis(); //start of the function
        long UserExecutionTime = UserMatching - AggregationEnd;
        logger.info("UserExecutionTime executed in {} ms", UserExecutionTime);


        return stories;
    }

    @Override
    public PageDto<PostDto> getHighlightedStories(int pageNumber, int itemsPerPage) {
        long AggregationStart = System.currentTimeMillis(); //start of the function
        PageDto<PostDto> pageDto = new PageDto<>();
        final Pageable pageable = PageRequest.of(pageNumber, itemsPerPage, Sort.Direction.DESC, "PostedDate");
        Criteria criteria = new Criteria();
        criteria.and("postStatus").is(PackageStatus.posted);
        criteria.and("postType").is(PostType.Story);

        Aggregation aggregation = queryNormalizeService.getPostFields(criteria, pageable, pageable.getSort());
        AggregationResults<PostResultDto> Results = mongoTemplate.aggregate(aggregation, "post", PostResultDto.class);
        List<PostDto> Posts = Results.getUniqueMappedResult().getFilteredResults();
        long AggregationEnd = System.currentTimeMillis(); //start of the function
        long executionTime = AggregationStart - AggregationEnd;
        logger.error("aggregation executed in {} ms", executionTime);


        long UserMatching = System.currentTimeMillis(); //start of the function
        long UserExecutionTime = UserMatching - AggregationEnd;

        logger.error("UserMatching executed in {} ms", UserExecutionTime);

        long totalCount = Results.getUniqueMappedResult() != null ? Results.getUniqueMappedResult().getTotalCount() : 0;
        pageDto.setTotalNoOfItems(totalCount);
        pageDto.setItems(Posts);
        return pageDto;
    }


    @Override
    public Page<CommentDto> getPostComments(String postId, int pageNumber, int itemsPerPage) {
        final Pageable pageable = ApplicationUtil.createPageRequest(pageNumber, itemsPerPage, "createdDate", "DESC");

        long start = System.currentTimeMillis(); //start of the function

        long gettingComment = System.currentTimeMillis(); //start of the function

        long executionTime = gettingComment - start;
        logger.error("getting comment executed in {} ms", executionTime);
        final Aggregation countAggregation = queryNormalizeService.getPostComments(Criteria.where("post._id").is(new ObjectId(postId)), pageable);

        AggregationResults<CommentResultDto> Results = mongoTemplate.aggregate(countAggregation, "comment", CommentResultDto.class);
        long totalCount = Results.getUniqueMappedResult() != null ? Results.getUniqueMappedResult().getTotalCount() : 0;
        List<CommentDto> comments = Results.getUniqueMappedResult().getFilteredResults();
        logger.error("start getting reacts");

        long gettingReacts = System.currentTimeMillis(); //start of the function

        long executionReactTime = gettingReacts - gettingComment;
        logger.error("getting comment reacts in {} ms", executionReactTime);
        final Page<CommentDto> page = new PageImpl<>(comments, pageable, totalCount);

        return page;
    }


    @Override
    public Page<ReplyDto> getCommentReply(String commentId, int pageNumber, int itemsPerPage) {
        final Pageable pageable = ApplicationUtil.createPageRequest(pageNumber, itemsPerPage, "update", "DESC");
        List<ReplyDto> replies = replyRepository.findAll().stream()
                .filter(z -> z.getRefcomment() != null && z.getRefcomment().getId().equals(commentId))
                .map(x -> mapper.convertToDto(x))
                .collect(Collectors.toList());


        final Page<ReplyDto> page = new PageImpl<>(replies, pageable, replies.size());
        return page;

    }

    @Override
    public PageDto<PostDto> getExplorer(SearchExplorerDto explorer, int pageNumber, int itemsPerPage) {
        long AggregationStart = System.currentTimeMillis(); //start of the function
        PageDto<PostDto> pageDto = new PageDto<>();
        final Pageable pageable = PageRequest.of(pageNumber, itemsPerPage, Sort.Direction.DESC, "PostedDate");
        Criteria criteria = new Criteria();
        criteria.and("postStatus").is(PackageStatus.posted);
        criteria.and("postType").is(PostType.Post);
        if (explorer.getText() == null)
            explorer.setText("");
        criteria.orOperator(
                Criteria.where("text").regex(explorer.getText(), "i"),
                Criteria.where("user.firstName").regex(explorer.getText(), "i"),
                Criteria.where("user.lastName").regex(explorer.getText(), "i"),
                new Criteria().andOperator(
                        Criteria.where("package").exists(true),
                        Criteria.where("package.name").regex(explorer.getText(), "i")
                )

        );

        Aggregation countAggregation = queryNormalizeService.getPostFields(criteria, pageable, pageable.getSort());
        AggregationResults<PostResultDto> countResults = mongoTemplate.aggregate(countAggregation, "post", PostResultDto.class);
        List<PostDto> Posts = countResults.getUniqueMappedResult().getFilteredResults();

        long AggregationEnd = System.currentTimeMillis(); //start of the function
        long executionTime = AggregationStart - AggregationEnd;
        logger.error("aggregation executed in {} ms", executionTime);


        long UserMatching = System.currentTimeMillis(); //start of the function
        long UserExecutionTime = UserMatching - AggregationEnd;

        logger.error("UserMatching executed in {} ms", UserExecutionTime);

        long totalCount = countResults.getUniqueMappedResult() != null ? countResults.getUniqueMappedResult().getTotalCount() : 0;
        pageDto.setTotalNoOfItems(totalCount);
        pageDto.setItems(Posts);
        return pageDto;
    }


    // Helper method to extract posts from the aggregation result


    @Override
    public List<ReactionDto> getPostReacts(String postId) {
        List<ReactionDto> reacts =
                mongoTemplate.find(Query.query(Criteria.where("post.id").is(postId)), PostReaction.class)
                        .stream().map(x -> mapper.convertToDto(x)).collect(Collectors.toList());
        return reacts;
    }

    @Override
    public List<ReactionDto> getCommentReacts(String commentId) {
        final Aggregation aggregation = queryNormalizeService.getCommentReacts(Criteria.where("comment._id").is(new ObjectId(commentId)));
        List<CommentReaction> Reactions = mongoTemplate.aggregate(aggregation, "commentReaction", CommentReaction.class).getMappedResults().stream().collect(Collectors.toList());
        List<ReactionDto> reacts = Reactions.stream().map(react -> mapper.convertToDto(react)).collect(Collectors.toList());
        return reacts;
    }

    @Override
    public List<ReactionDto> getMediaReacts(String mediaId) {
        final Aggregation aggregation = queryNormalizeService.getMediaReacts(Criteria.where("media._id").is(new ObjectId(mediaId)));
        List<MediaReaction> Reactions = mongoTemplate.aggregate(aggregation, "mediaReaction", MediaReaction.class).getMappedResults().stream().collect(Collectors.toList());
        List<ReactionDto> reacts = Reactions.stream().map(react -> mapper.convertToDto(react)).collect(Collectors.toList());
        return reacts;
    }

    @Override
    public List<ReactionDto> getReplyReacts(String replyId) {
        final Aggregation aggregation = queryNormalizeService.getReplayReacts(Criteria.where("reply._id").is(new ObjectId(replyId)));
        List<ReplyReaction> Reactions = mongoTemplate.aggregate(aggregation, "replyReaction", ReplyReaction.class).getMappedResults().stream().collect(Collectors.toList());
        List<ReactionDto> reacts = Reactions.stream().map(react -> mapper.convertToDto(react)).collect(Collectors.toList());
        return reacts;
    }

    @Override
    public List<InfluencerPostDto> getRequestedPosts(PostType postType) {
        Criteria criteria = new Criteria();
        criteria.where("user._id").is(new ObjectId(userSessionData.getId()))
                .and("postType").is(postType).and("postStatus").is(PackageStatus.draft);
        final Aggregation aggregation = queryNormalizeService.getPostFields(criteria, null, null);
        List<Post> Posts = mongoTemplate.aggregate(aggregation, "post", Post.class).getMappedResults().stream().collect(Collectors.toList());
        List<InfluencerPostDto> posts = Posts.stream()
                .map(post -> mapper.convertToInfluenceDto(post))
                .collect(Collectors.toList());

        return posts;
    }


    /**
     * Retrieves all posts owned by a specific user.
     * <p>
     * This method performs a MongoDB aggregation to fetch posts where the user ID matches
     * the provided ID. The posts are then converted to DTOs for client consumption.
     * <p>
     * The aggregation pipeline:
     * 1. Matches posts based on user ID
     * 2. Retrieves all post fields using queryNormalizeService
     * 3. Maps results to Post entities
     * 4. Converts Post entities to PostDto objects
     *
     * @param id The unique identifier of the user whose posts are to be retrieved
     * @return List<PostDto> A list of PostDto objects representing the user's posts.
     *         Returns an empty list if no posts are found.
     * @see PostDto
     * @see Post
     */
    @Override
    public List<PostDto> GetUserOwnedPosts(String id) {
        // Create aggregation query to fetch posts for the specified user
        final Aggregation aggregation = queryNormalizeService.getPostFields(
            Criteria.where("user._id").is(new ObjectId(id)), 
            null,  // No pagination
            null   // No sorting
        );

        // Execute aggregation and transform results to PostDto objects
        return mongoTemplate.aggregate(aggregation, "post", Post.class)
                .getMappedResults()
                .stream()
                .map(mapper::convertToDto)
                .collect(Collectors.toList());
    }


    @Override
    public PostDto getPostBySlug(String slug) {
        var post = postRepository.findBySlug(slug).orElse(null);
        return mapper.convertToDto(post);
    }


    private Criteria createSearchSpecification(Map<String, Object> obj) {
        Criteria query = new Criteria();
        List<Criteria> list = new ArrayList<>();
        for (Map.Entry<String, Object> entry : obj.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (value != null && !value.toString().isEmpty()) {
                list.add(Criteria.where(key).regex(value.toString(), "i"));
            }
        }
        if (list.size() > 0)
            query.orOperator(list);
        return query;
    }


    /**
     * Searches and retrieves posts based on a search keyword with pagination support.
     * This method performs a full-text search across posts using MongoDB's search capabilities.
     * <br><br>
     * Searchable fields:
     * <ul>
     *   <li>Post Description (text content)</li>
     *   <li>User Information (first name and last name)</li>
     *   <li>Package Title (name)</li>
     *   <li>Package Description</li>
     * </ul>
     * 
     * The search functionality:
     * <ul>
     *   <li>Performs case-insensitive matching across all searchable fields</li>
     *   <li>Orders results by creation date (newest first)</li>
     *   <li>Supports pagination for efficient data retrieval</li>
     *   <li>Returns both search results and total count for pagination</li>
     * </ul>
     *
     * @param search The search keyword to filter posts. Can be null or empty to return all posts
     * @param page Zero-based page number for pagination
     * @param size Number of items per page
     * @return PageDto containing:
     * <ul>
     *   <li>List of matching searchPost objects</li>
     *   <li>Total count of matching posts</li>
     * </ul>
     *         The results are sorted by creation date in descending order
     */
    @Override
    public PageDto<searchPost> searchExplore(String search, int page, int size) {
        // Initialize the response DTO for paginated results
        PageDto<searchPost> pageDto = new PageDto<>();

        // Configure pagination with sorting
        // Using DESC sort to show newest posts first
        final Pageable pageable = PageRequest.of(page, size, Sort.Direction.DESC, "creationDate");

        // Execute search query with pagination
        // If search is empty, returns all posts
        Page<searchPost> searchResults = searchPostRepository.findByKeyword(search, pageable);

        // Populate response DTO with results and metadata
        pageDto.setTotalNoOfItems(searchResults.getTotalElements());  // Total matching posts
        pageDto.setItems(searchResults.getContent());                 // Current page content

        return pageDto;
    }

    @Override
    public boolean isSlugUnique(String slug) {
        return !postRepository.existsBySlug(slug);
    }


    public List<User> findFollowers(String influencerId) {
        // Get followed influencers
        var followedInfluencersReactions = reactionSearchRepository.findByEntityNameAndEntityTypeAndEntityId(
                EntityName.User,
                EntityType.Follow,
                influencerId
        );

        var followedInfluencersIds = followedInfluencersReactions
                .stream()
                .map(ReactionSearch::getUserId)
                .filter(Objects::nonNull)
                .toList();

        return userRepository.findByIdIn(followedInfluencersIds);
    }

    public List<MediaWrapper> generateMediaWithWrapper(List<CreateMediaDto> mediasRequest) {
        if(mediasRequest == null)
            return new ArrayList<>();
        List<MediaWrapper> createdMedias = new ArrayList<>();
        for (CreateMediaDto createMediaDto : mediasRequest) {
            Media newMedia = new Media();
            newMedia.setMediaType(createMediaDto.getMediaType());
            newMedia.setSource(createMediaDto.getSource());
            newMedia.setVideoUrl(createMediaDto.getVideoUrl());
            newMedia.setVideoSize(createMediaDto.getVideoSize());
            newMedia.setCreationDate(DateTime.now().toDate());
            newMedia.setLastUpdate(DateTime.now().toDate());
            newMedia.setThumbnailCaptureUrl(createMediaDto.getThumbnailCaptureUrl());
            newMedia.setThumbnailClipUrl(createMediaDto.getThumbnailClipUrl());
            newMedia.setOverlays(createMediaDto.getOverlays());
            newMedia.setTitle(createMediaDto.getTitle());
            newMedia.setLatitude(createMediaDto.getLatitude());
            newMedia.setLongtuid(createMediaDto.getLongtuid());
            newMedia.setVideoDuration(createMediaDto.getVideoDuration());
            newMedia.setVideoDurationMS(createMediaDto.getVideoDurationMS());
            newMedia.setDescription(createMediaDto.getDescription());
            newMedia.setClipStartTimecode(createMediaDto.getClipStartTimecode());
            newMedia.setClipEndTimecode(createMediaDto.getClipEndTimecode());
            newMedia.setStartTime(createMediaDto.getStartTime());
            newMedia.setEndTime(createMediaDto.getEndTime());
            newMedia.setTags(createMediaDto.getTags());

            if(createMediaDto.getTaggedUsers()!=null && !createMediaDto.getTaggedUsers().isEmpty()) {
                List<ObjectId> usersId = createMediaDto.getTaggedUsers().stream().map(RefranceModelDto::getId).map(ObjectId::new).collect(Collectors.toList());
                Aggregation taggedquery = queryNormalizeService.getUsersList(Criteria.where("_id").in(usersId));
                List<User> users = mongoTemplate.aggregate(taggedquery, "user", User.class).getMappedResults();
                newMedia.setTaggedUsers(users);
            }

            if(createMediaDto.getMediaType() == MediaType.image){
                try {
                    String category = createMediaDto.getSource().split("/")[1];
                    ImageCategory imageCategory = ImageCategory.valueOf(category);
                    newMedia.setImageCategory(imageCategory);
                } catch (Exception ignored) { }
            }


            var savedMedia = mediaRepository.save(newMedia);

            MediaWrapper mediaWrapper = new MediaWrapper();
            mediaWrapper.setMedia(savedMedia);
            mediaWrapper.setUrl(newMedia.getMediaType() == MediaType.video ? newMedia.getVideoUrl() : newMedia.getSource());
            mediaWrapper.setType(createMediaDto.getMediaType());
            mediaWrapper.setMainImage(createMediaDto.isMainImage());
            createdMedias.add(mediaWrapper);
        }

        return createdMedias;
    }
}
