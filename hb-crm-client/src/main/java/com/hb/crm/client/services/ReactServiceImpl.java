package com.hb.crm.client.services;

import com.hb.crm.client.CommonService.CommonUtils;
import com.hb.crm.client.CommonService.Mapper;
import com.hb.crm.client.beans.UserSessionData;
import com.hb.crm.client.config.CustomHundlerar.CustomException;
import com.hb.crm.client.services.interfaces.QueryNormalizeService;
import com.hb.crm.client.services.interfaces.ReactService;
import com.hb.crm.core.CombinedKeys.FollowsKey;
import com.hb.crm.core.CombinedKeys.viewPostKey;
import com.hb.crm.core.Enums.*;
import com.hb.crm.core.beans.*;
import com.hb.crm.core.repositories.*;
import com.hb.crm.core.searchBeans.*;
import com.hb.crm.core.searchRepositories.ReactionSearchRepository;
import com.hb.crm.core.searchRepositories.SearchPostRepository;
import com.hb.crm.core.searchRepositories.SearchStoryRepository;
import com.hb.crm.core.searchRepositories.SearchUserRepository;
import com.hb.crm.core.services.interfaces.NotificationService;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class ReactServiceImpl implements ReactService {
    private final CommonUtils commonUtils;
    private final UserSessionData userSessionData;
    private final SearchStoryRepository searchStoryRepository;
    private final PostRepository postRepository;
    private final MongoTemplate mongoTemplate;
    private final MongoTemplate mongoTemplate2;
    private final PostViewsRepository postViewsRepository;
    private final QueryNormalizeService queryNormalizeService;
    private final UserRepository userRepository;
    private final CommentRepository commentRepository;
    private final CommentReactionRepository commentReactionRepository;
    private final ReactionSearchRepository reactionSearchRepository;
    private final MediaRepository mediaRepository;
    private final MediaReactionRepository mediaReactionRepository;
    private final ReplyRepository replyRepository;
    private final ReplyReactionRepository replyReactionRepository;
    private final SearchPostRepository searchPostRepository;
    private final PostReactionRepository postReactionRepository;
    private final FollowsRepository followsRepository;
    private final SearchUserRepository searchUserRepository;
    private final NotificationService notificationService;
    private final SubPackageRepository subPackageRepository;
    private final Mapper mapper;
    private Logger logger = LoggerFactory.getLogger(ReactServiceImpl.class);
    public ReactServiceImpl(
            CommonUtils commonUtils
            , QueryNormalizeService queryNormalizeService
            , UserSessionData userSessionData
            , SearchStoryRepository searchStoryRepository
            , PostRepository postRepository
            , @Qualifier("mongoTemplate1") MongoTemplate mongoTemplate
            , @Qualifier("mongoTemplate2") MongoTemplate mongoTemplate2
            , PostViewsRepository postViewsRepository, UserRepository userRepository
            , CommentRepository commentRepository, CommentReactionRepository commentReactionRepository
            , ReactionSearchRepository reactionSearchRepository, MediaRepository mediaRepository
            , MediaReactionRepository mediaReactionRepository, ReplyRepository replyRepository
            , ReplyReactionRepository replyReactionRepository, SearchPostRepository searchPostRepository
            , PostReactionRepository postReactionRepository, FollowsRepository followsRepository
            , SearchUserRepository searchUserRepository, NotificationService notificationService, SubPackageRepository subPackageRepository, Mapper mapper) {
        this.commonUtils = commonUtils;
        this.queryNormalizeService = queryNormalizeService;
        this.userSessionData = userSessionData;
        this.searchStoryRepository = searchStoryRepository;
        this.postRepository = postRepository;
        this.mongoTemplate = mongoTemplate;
        this.mongoTemplate2 = mongoTemplate2;
        this.postViewsRepository = postViewsRepository;
        this.userRepository = userRepository;
        this.commentRepository = commentRepository;
        this.commentReactionRepository = commentReactionRepository;
        this.reactionSearchRepository = reactionSearchRepository;
        this.mediaRepository = mediaRepository;
        this.mediaReactionRepository = mediaReactionRepository;
        this.replyRepository = replyRepository;
        this.replyReactionRepository = replyReactionRepository;
        this.searchPostRepository = searchPostRepository;
        this.postReactionRepository = postReactionRepository;
        this.followsRepository = followsRepository;
        this.searchUserRepository = searchUserRepository;
        this.notificationService = notificationService;
        this.subPackageRepository = subPackageRepository;
        this.mapper = mapper;
    }


    @Override
    public List<ReactionSearch> userReaction(String date, String toDate) throws ParseException {
        String id = userSessionData.getId();
        LocalDateTime filterDate = LocalDateTime.parse(date);
        Query query = new Query();
        query.addCriteria(Criteria.where("userId").is(id));
        query.addCriteria(Criteria.where("entityName").ne(EntityName.User));
        if (toDate != null && !toDate.isEmpty()) {
            LocalDateTime toFilterDate = LocalDateTime.parse(date);
            Criteria criteria = new Criteria();
            criteria.andOperator(Criteria.where("updated").gte(filterDate), Criteria.where("updated").lte(toFilterDate));

        } else {
            query.addCriteria(Criteria.where("updated").gte(filterDate));
        }
        return mongoTemplate2.find(query, ReactionSearch.class);
    }

    @Override
    public List<ReactionSearch> userfollows() {
        String id = userSessionData.getId();
        Query query = new Query();
        query.addCriteria(Criteria.where("userId").is(id));
        query.addCriteria(Criteria.where("entityName").is(EntityName.User));

        return mongoTemplate2.find(query, ReactionSearch.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReactionSearch AddReact(ReactionSearch reactionSearch) {
        if(reactionSearch.getEntityId() != null && !reactionSearch.getEntityId().isEmpty())
             if(!ObjectId.isValid(reactionSearch.getEntityId()))
                 throw new CustomException(400, "Entity id provided is invalid!");

        if(reactionSearch.getUserId() != null && !reactionSearch.getUserId().isEmpty())
            if(!ObjectId.isValid(reactionSearch.getUserId()))
                throw new CustomException(400, "User id provided is invalid!");

        return switch (reactionSearch.getEntityName()) {
            case Post -> processPostAction(reactionSearch.getId(), reactionSearch.getEntityId(),
                    reactionSearch.getEntityType(), reactionSearch.getReactionType());

            case Package -> processPackageAction(reactionSearch.getId(), reactionSearch.getEntityId(),
                    reactionSearch.getEntityType());

            case FollowPackage -> processFollowPackageAction(reactionSearch.getId(), reactionSearch.getEntityId(),
                    reactionSearch.getEntityType());

            case Comment -> processCommentAction(reactionSearch.getId(), reactionSearch.getEntityId(),
                    reactionSearch.getEntityType(), reactionSearch.getReactionType());

            case Media -> processMediaAction(reactionSearch.getId(), reactionSearch.getEntityId(),
                    reactionSearch.getEntityType(), reactionSearch.getReactionType());

            case User -> processUserAction(reactionSearch.getId(), reactionSearch.getEntityId(),
                    reactionSearch.getEntityType());

            default -> throw new IllegalArgumentException("Unknown entity: " + reactionSearch.getEntityName());
        };
    }

    @Override
    public void RemoveReact(String reactId, String entityId, EntityName entityName, EntityType entityType) {
        switch (entityName) {
            case Post:
                removePostReaction(reactId, entityId, entityType);
                break;
            case Package:
                removePackageReaction(reactId, entityId, entityType);
                break;
            case FollowPackage:
                removePackageReaction(reactId, entityId, entityType);
                break;
            case Comment:
                removeCommentReaction(reactId, entityId, entityType);
                break;
            case Media:
                removeMediaReaction(reactId, entityId, entityType);
                break;
            case User:
                UnFollowInfluence(reactId, entityId);
                break;
            default:
                throw new IllegalArgumentException("Unknown entity: " + entityName);
        }
    }

    private ReactionSearch viewStory(String reactId, String StoryId) {
        String id = userSessionData.getId();
        Optional<Post> optionalPost = postRepository.findById(StoryId);

        if (optionalPost.isPresent()) {
            Post post = optionalPost.get();
            searchStory searchStory = searchStoryRepository.findById(StoryId).get();
            final Aggregation aggregation = queryNormalizeService.getPostViewFields(Criteria.where("_id.story._id").is(new ObjectId(StoryId)));
            List<PostViews> views = mongoTemplate.aggregate(aggregation, "postViews", PostViews.class).getMappedResults();
            PostViews view = views.stream().filter(z -> z.getId().getUser().getId().equals(id)).findFirst().orElse(null);

            if (view == null) {
                view = new PostViews();
                viewPostKey key = new viewPostKey();
                key.setStory(post);
                key.setUser(new User(id));
                view.setId(key);
                postViewsRepository.save(view);
                post.setViewsCount(views.size() + 1);
                searchStory.setViewsCount(views.size() + 1);
                searchStoryRepository.save(searchStory);
                postRepository.save(post);
                return commonUtils.CreateReaction(StoryId, id, null, EntityName.Post, EntityType.View, reactId);
            }
            return commonUtils.getReaction(id, StoryId, EntityType.View);
        }
        return null;
    }
    @Transactional
    public ReactionSearch SharePostOrStory(String entityId) {
        String id = userSessionData.getId();
        Aggregation query = queryNormalizeService.getUserFields(Criteria.where("_id").is(new ObjectId(id)));
        User user = mongoTemplate.aggregate(query, "user", User.class).getMappedResults().stream().findAny().orElse(null);
        if (user != null) {
            int sharing= user.getNumberOfSharingContent();
            Optional<Post> optionalPost = postRepository.findById(entityId);
            if (optionalPost.isPresent()) {
                Post post = optionalPost.get();
                if (post.getPostType().equals(PostType.Story)) {
                    searchStory searchStory = searchStoryRepository.findById(entityId).get();
                    int shares = post.getSharesCount();
                    post.setSharesCount(shares + 1);
                    searchStory.setSharesCount(shares + 1);
                    searchStoryRepository.save(searchStory);
                    postRepository.save(post);
                    user.setNumberOfSharingContent(sharing + 1);
                    userRepository.save(user);
                } else {
                    searchPost searchPost = searchPostRepository.findById(entityId).get();
                    int shares = post.getSharesCount();
                    post.setSharesCount(shares + 1);
                    searchPost.setSharesCount(shares + 1);
                    searchPostRepository.save(searchPost);
                    postRepository.save(post);
                    user.setNumberOfSharingContent(sharing + 1);
                    userRepository.save(user);
                }

            }
            return new ReactionSearch();

        }
        return  null;
     }

    /**
     * Adds a package to the user's favorites list.
     *
     * @param reactId   The unique identifier for the reaction
     * @param PackageId The unique identifier of the package to be added to favorites
     * @return ReactionSearch object containing the favorite reaction details, or null if user not found
     * @throws CustomException if the package is already in the user's favorites
     */
    private ReactionSearch addPackageToFavourites(String reactId, String PackageId,PackageType packageType) {
        // Get current user's ID from session
        String id = userSessionData.getId();

        subPackageRepository.findById(PackageId).orElseThrow(() -> new CustomException(404, "Package not found!"));

        // Create aggregation query to fetch user details
        Aggregation query = queryNormalizeService.getUserFields(Criteria.where("_id").is(new ObjectId(id)));

        // Fetch user from database
        User user = mongoTemplate.aggregate(query, "user", User.class).getMappedResults().stream().findAny().orElse(null);

        if (user != null) {
            // Get user's likes list or create new if null
            List<Like> likes = user.getLike();
            if (likes == null)
                likes = new ArrayList<>();

            // Find all package favorite reactions for current user
            List<ReactionSearch> favouriteReactions = reactionSearchRepository.findByEntityNameAndEntityTypeAndUserId(
                    packageType==PackageType.TravelWithMe? EntityName.Package: EntityName.FollowPackage,
                    EntityType.Favourite,
                    id
            );

            // Check if package is already in favorites
            ReactionSearch reaction = favouriteReactions
                    .stream()
                    .filter(z -> z.getEntityId().equals(PackageId))
                    .findFirst()
                    .orElse(null);

            if (reaction == null) {
                // Add new package to favorites
                likes.add(new Like("", "", new SubPackage(PackageId)));
                user.setLike(likes);
                userRepository.save(user);
                // Create and return new reaction
                return commonUtils.CreateReaction(PackageId, id, null, packageType==PackageType.TravelWithMe? EntityName.Package: EntityName.FollowPackage, EntityType.Favourite, reactId);
            } else {
                // Throw exception if package already exists in favorites
                throw new CustomException(500, "Package Already In favourites");
            }
        }
        return null;
    }

    private void removePackageFavourite(String reactId, String PackageId) {

        String id = userSessionData.getId();
        Aggregation query = queryNormalizeService.getUserFields(Criteria.where("_id").is(new ObjectId(id)));
        User user = mongoTemplate.aggregate(query, "user", User.class).getMappedResults().stream().findAny().orElse(null);
        if (user != null) {
            List<Like> likes = user.getLike();
            if (likes == null)
                likes = new ArrayList<Like>();
            Like like = likes.stream().filter(customer -> customer.getPackageref() != null && PackageId.equals(customer.getPackageref().getId()))
                    .findAny()
                    .orElse(null);
            if (like != null)
                likes.remove(like);

            user.setLike(likes);
            userRepository.save(user);
        }
        reactionSearchRepository.deleteById(reactId);
    }

    private ReactionSearch reactPostOrStory(String reactId, String PostId, ReactionType reactionType) {
        return PostReact(reactId, true, PostId, userSessionData.getId(), reactionType);
    }

    private ReactionSearch reactComment(String reactId, String CommentId, ReactionType reactionType) {
        String id = userSessionData.getId();
        Optional<Comment> optionalComment = commentRepository.findById(CommentId);
        User user = userRepository.findById(id).orElseThrow(() -> new CustomException(404, "User not found!"));

        if (optionalComment.isPresent()) {
            Comment comment = optionalComment.get();
            CommentReaction reaction = new CommentReaction();
            reaction.setUser(new User(id));
            reaction.setReactionType(reactionType);
            reaction.setComment(comment);

            Aggregation aggregation = queryNormalizeService.getCommentReactionFields(
                    Criteria.where("comment._id").is(new ObjectId(CommentId)));
            List<CommentReaction> reactions = mongoTemplate.aggregate(aggregation, "commentReaction", CommentReaction.class)
                    .getMappedResults();

            CommentReaction myReact = reactions.stream()
                    .filter(_react -> _react.getUser().getId().equals(id))
                    .findAny()
                    .orElse(null);

            if (myReact == null) {
                comment.setNumberOfReactions(reactions.size() + 1);
                commentRepository.save(comment);
                commentReactionRepository.save(reaction);

                // Send notification to the owner of the comment
                sendNotificationReactComment(comment, comment.getUser(), user);

                return commonUtils.CreateReaction(comment.getId(), id, reactionType, EntityName.Comment, EntityType.React, reactId);
            } else {
                ReactionSearch reactionSearch = commonUtils.getReaction(id, comment.getId(), EntityType.React);
                if (reactionSearch != null) {
                    reactionSearch.setReactionType(reactionType);
                    return reactionSearchRepository.save(reactionSearch);
                } else {
                    return commonUtils.CreateReaction(comment.getId(), id, reactionType, EntityName.Comment, EntityType.React, reactId);
                }
            }
        }
        return null;
    }

    private ReactionSearch reactMedia(String reactId, String MediaId, ReactionType reactionType) {
        String id = userSessionData.getId();
        Optional<Media> optionalMedia = mediaRepository.findById(MediaId);

        if (optionalMedia.isPresent()) {
            Media media = optionalMedia.get();
            MediaReaction reaction = new MediaReaction();
            reaction.setUser(new User(id));
            reaction.setReactionType(reactionType);
            reaction.setMedia(media);

            Aggregation aggregation = queryNormalizeService.getMediaReactionFields(
                    Criteria.where("media._id").is(new ObjectId(MediaId)));
            List<MediaReaction> reactions = mongoTemplate.aggregate(aggregation, "mediaReaction", MediaReaction.class)
                    .getMappedResults();

            MediaReaction myReact = reactions.stream()
                    .filter(_react -> _react.getUser().getId().equals(id))
                    .findAny()
                    .orElse(null);

            if (myReact == null) {
                media.setNumberOfReactions(reactions.size() + 1);
                mediaRepository.save(media);
                mediaReactionRepository.save(reaction);

                // Send notification to the owner of the media
                if(media.getMediaType() == MediaType.reel) {
                    User user = userRepository.findById(id)
                            .orElseThrow(() -> new CustomException(400, "Current user not found!"));
                    SendNotificationForReactReel(media,user);
                }
                syncMedia(media);
                return commonUtils.CreateReaction(media.getId(), id, reactionType, EntityName.Media, EntityType.React, reactId);
            } else {
                ReactionSearch reactionSearch = commonUtils.getReaction(id, media.getId(), EntityType.React);
                if (reactionSearch != null) {
                    reactionSearch.setReactionType(reactionType);
                    return reactionSearchRepository.save(reactionSearch);
                } else {
                    return commonUtils.CreateReaction(media.getId(), id, reactionType, EntityName.Media, EntityType.React, reactId);
                }
            }
        }
        return null;
    }
    private void syncMedia(Media media) {
        Query query = new Query(
                Criteria.where("medias.media._id").is(new ObjectId(media.getId()))
        );

        Query postQuery = new Query(
                Criteria.where("media.media._id").is(new ObjectId(media.getId()))
        );

        // Prepare update object for media arrays
        Update update = new Update()
                .set("medias.$.media.title", media.getTitle())
                .set("medias.$.media.creationDate", media.getCreationDate())
                .set("medias.$.media.description", media.getDescription())
                .set("medias.$.media.source", media.getSource())
                .set("medias.$.media.videoUrl", media.getVideoUrl())
                .set("medias.$.media.imageCategory", media.getImageCategory())
                .set("medias.$.media.videoDuration", media.getVideoDuration())
                .set("medias.$.media.videoDurationMS", media.getVideoDurationMS())
                .set("medias.$.media.thumbnailClipUrl", media.getThumbnailClipUrl())
                .set("medias.$.media.thumbnailCaptureUrl", media.getThumbnailCaptureUrl())
                .set("medias.$.media.mediaType", media.getMediaType())
                .set("medias.$.media.ownerId", media.getOwnerId())
                .set("medias.$.media.videoSize", media.getVideoSize())
                .set("medias.$.media.lastUpdaterId", media.getLastUpdaterId())
                .set("medias.$.media.employee", media.getEmployee())
                .set("medias.$.media.numberOfReactions", media.getNumberOfReactions())
                .set("medias.$.media.numberOfComments", media.getNumberOfComments())
                .set("medias.$.media.userInfo.id", media.getUser() != null ? media.getUser().getId() : null);

        //user
        if (media.getUser() != null && media.getUser().getUsername() != null) {
            update.set("medias.$.media.userInfo.id", media.getUser().getId());
            update.set("medias.$.media.userInfo.username", media.getUser().getUsername());
            update.set("medias.$.media.userInfo.firstName", media.getUser().getFirstName());
            update.set("medias.$.media.userInfo.lastName", media.getUser().getLastName());
            update.set("medias.$.media.userInfo.usertype", media.getUser().getUsertype());
            update.set("medias.$.media.userInfo.coverImage", media.getUser().getCoverImage());
            update.set("medias.$.media.userInfo.profileImage", media.getUser().getProfileImage());
        }
        //package
        if (media.get_package() != null && media.get_package().getSlug() != null) {
            update
                    .set("medias.$.media._package.id", media.get_package().getId())
                    .set("medias.$.media._package.name",   media.get_package().getName() )
                    .set("medias.$.media._package.slug",  media.get_package().getSlug())
                    .set("medias.$.media._package.description",  media.get_package().getDescription());
        }

        // Prepare update object for single media in post/package
        Update updatePost = new Update()
                .set("media.$.media.title", media.getTitle())
                .set("media.$.media.creationDate", media.getCreationDate())
                .set("media.$.media.description", media.getDescription())
                .set("media.$.media.source", media.getSource())
                .set("media.$.media.videoUrl", media.getVideoUrl())
                .set("media.$.media.imageCategory", media.getImageCategory())
                .set("media.$.media.videoDuration", media.getVideoDuration())
                .set("media.$.media.videoDurationMS", media.getVideoDurationMS())
                .set("media.$.media.thumbnailClipUrl", media.getThumbnailClipUrl())
                .set("media.$.media.thumbnailCaptureUrl", media.getThumbnailCaptureUrl())
                .set("media.$.media.mediaType", media.getMediaType())
                .set("media.$.media.ownerId", media.getOwnerId())
                .set("media.$.media.videoSize", media.getVideoSize())
                .set("media.$.media.lastUpdaterId", media.getLastUpdaterId())
                .set("media.$.media.employee", media.getEmployee())
                .set("media.$.media.numberOfReactions", media.getNumberOfReactions())
                .set("media.$.media.numberOfComments", media.getNumberOfComments());
        //user
        if (media.getUser() != null && media.getUser().getUsername() != null) {
            updatePost.set("media.$.media.userInfo.id", media.getUser().getId());
            updatePost.set("media.$.media.userInfo.username", media.getUser().getUsername());
            updatePost.set("media.$.media.userInfo.firstName", media.getUser().getFirstName());
            updatePost.set("media.$.media.userInfo.lastName", media.getUser().getLastName());
            updatePost.set("media.$.media.userInfo.usertype", media.getUser().getUsertype());
            updatePost.set("media.$.media.userInfo.coverImage", media.getUser().getCoverImage());
            updatePost.set("media.$.media.userInfo.profileImage", media.getUser().getProfileImage());
        }
        //post
        if (media.getPost() != null && media.getPost().getSlug() != null) {
            updatePost.set("media.$.media.post.id", media.getPost().getId());
            updatePost.set("media.$.media.post.text", media.getPost().getText());
            updatePost.set("media.$.media.post.slug", media.getPost().getSlug());

        }
        if (media.getTags() != null &&
                !media.getTags().isEmpty() &&
                media.getTags().stream().anyMatch(tag -> tag.getText() != null)) {

            update.set("medias.$.media.tags", media.getTags());
            updatePost.set("media.$.media.tags", media.getTags());
        }
        // Run updates on all affected classes
        mongoTemplate2.updateMulti(postQuery, updatePost, searchStory.class);
        mongoTemplate2.updateMulti(postQuery, updatePost, searchPost.class);
        mongoTemplate2.updateMulti(query, update, searchPackage.class);
    }

    private String unReactPostOrStory(String reactId, String PostId) {
        String id = userSessionData.getId();
        this.PostReact(reactId, false, PostId, id, null);
        return PostId;
    }

    private String unReactComment(String reactId, String CommentId) {

        Optional<Comment> OptionalComment = commentRepository.findById(CommentId);
        Comment comment = OptionalComment.get();
        Aggregation aggregation = queryNormalizeService.getCommentReactionFields(Criteria.where("comment._id").is(new ObjectId(CommentId)));
        List<CommentReaction> reactions = mongoTemplate.aggregate(aggregation, "commentReaction", CommentReaction.class).getMappedResults();

        CommentReaction myReact = reactions.stream().filter(_react -> _react.getUser().getId().equals(userSessionData.getId())).findAny()
                .orElse(null);
        if (myReact != null && myReact.getUser().getId().equals(userSessionData.getId())) {
            commentReactionRepository.delete(myReact);
            if (reactions.size() > 0) {
                comment.setNumberOfReactions(reactions.size() - 1);
            } else {
                comment.setNumberOfReactions(0);
            }
        }

        commentRepository.save(comment);
        ReactionSearch reactionSearch = commonUtils.getReaction(userSessionData.getId(), CommentId,EntityType.React);
        if (reactionSearch != null) {
            reactionSearchRepository.delete(reactionSearch);
        }
        return CommentId;

    }

    private String unReactMedia(String reactId, String MediaId) {

        Optional<Media> OptionalMedia = mediaRepository.findById(MediaId);
        Media media = OptionalMedia.get();
        Aggregation aggregation = queryNormalizeService.getMediaReactionFields(Criteria.where("media._id").is(new ObjectId(MediaId)));
        List<MediaReaction> reactions = mongoTemplate.aggregate(aggregation, "mediaReaction", MediaReaction.class).getMappedResults();

        MediaReaction myReact = reactions.stream().filter(_react -> _react.getUser().getId().equals(userSessionData.getId())).findAny()
                .orElse(null);
        if (myReact != null && myReact.getUser().getId().equals(userSessionData.getId())) {
            mediaReactionRepository.delete(myReact);
            if (reactions.size() > 0) {
                media.setNumberOfReactions(reactions.size() - 1);
            } else {
                media.setNumberOfReactions(0);
            }
        }
        reactionSearchRepository.deleteById(reactId);
        mediaRepository.save(media);
        syncMedia(media);
        return MediaId;

    }

    private String removePostBookmark(String reactId, String PostId) {
        String id = userSessionData.getId();
        Aggregation query = queryNormalizeService.getUserFields(Criteria.where("_id").is(new ObjectId(id)));
        User user = mongoTemplate.aggregate(query, "user", User.class).getMappedResults().stream().findAny().orElse(null);
        if (user != null) {
            List<Post> posts = user.getPostBookMark();
            if (posts == null)
                posts = new ArrayList<Post>();
            Post post = posts.stream().filter(item -> PostId.equals(item.getId()))
                    .findAny()
                    .orElse(null);
            if (post != null)
                posts.remove(post);
            user.setPostBookMark(posts);
            userRepository.save(user);
        }
        reactionSearchRepository.deleteById(reactId);

        return PostId;
    }

    private String unsaveReel(String reactId, String reelId) {
        String id = userSessionData.getId();
        Aggregation query = queryNormalizeService.getUserFields(Criteria.where("_id").is(new ObjectId(id)));
        User user = mongoTemplate.aggregate(query, "user", User.class).getMappedResults().stream().findAny().orElse(null);

        if (user != null) {
            List<String> savedReelsIds = user.getSavedReelsIds();

            if(savedReelsIds.stream().anyMatch(reelId::equals))
                savedReelsIds.remove(reelId);

            user.setSavedReelsIds(savedReelsIds);
            userRepository.save(user);
        }
        reactionSearchRepository.deleteById(reactId);

        return reelId;
    }

    private String reactReply(String ReplyId, ReactionType reactionType) {
        String id = userSessionData.getId();
        Aggregation query = queryNormalizeService.getUserFields(Criteria.where("_id").is(new ObjectId(id)));
        User user = mongoTemplate.aggregate(query, "user", User.class).getMappedResults().stream().findAny().orElse(null);
        if (user != null) {
            Reply reply = replyRepository.findById(ReplyId).orElse(null);
            if (reply != null) {
                ReplyReaction reaction = new ReplyReaction();
                reaction.setUser(user);
                reaction.setReactionType(reactionType);
                reaction.setReply(reply);

                Aggregation aggregation = queryNormalizeService.getReplyReactionFields(Criteria.where("reply._id").is(new ObjectId(ReplyId)));
                List<ReplyReaction> reactions = mongoTemplate.aggregate(aggregation, "replyReaction", ReplyReaction.class).getMappedResults();
                Reaction myReact = reactions.stream()
                        .filter(_react ->
                                _react.getUser().getId().equals(user.getId()))
                        .findAny()
                        .orElse(null);

                if (myReact == null) {
                    replyReactionRepository.save(reaction);
                    reply.setNumberOfReactions(reactions.size() + 1);
                } else {
                    reaction.setId(myReact.getId());
                    replyReactionRepository.save(reaction);
                }
                replyRepository.save(reply);
            }
        }
        return ReplyId;

    }

    private ReactionSearch addPostBookmark(String reactId, String PostId) {
        String id = userSessionData.getId();
        Aggregation query = queryNormalizeService.getUserFields(Criteria.where("_id").is(new ObjectId(id)));
        User user = mongoTemplate.aggregate(query, "user", User.class).getMappedResults().stream().findAny().orElse(null);

        if (user != null) {
            List<Post> PostBookMarks = user.getPostBookMark();
            if (PostBookMarks == null)
                PostBookMarks = new ArrayList<>();

            Post post = PostBookMarks.stream()
                    .filter(customer -> PostId.equals(customer.getId()))
                    .findAny()
                    .orElse(null);

            if (post == null) {
                PostBookMarks.add(new Post(PostId));
                user.setPostBookMark(PostBookMarks);
                userRepository.save(user);
                return commonUtils.CreateReaction(PostId, id, null, EntityName.Post, EntityType.PostMark, reactId);
            }
        }
        return null;
    }

    private ReactionSearch saveReel(String reactId, String reelId) {
        String id = userSessionData.getId();
        Aggregation query = queryNormalizeService.getUserFields(Criteria.where("_id").is(new ObjectId(id)));
        User user = mongoTemplate.aggregate(query, "user", User.class).getMappedResults().stream().findAny().orElse(null);

        if (user != null) {
            List<String> savedReelsIds = user.getSavedReelsIds();

            if(savedReelsIds.stream()
                    .anyMatch(savedId -> savedId.equals(reelId))){
                return null;
            }

            savedReelsIds.add(reelId);
            user.setSavedReelsIds(savedReelsIds);
            userRepository.save(user);
            return commonUtils.CreateReaction(reelId, id, null, EntityName.Media, EntityType.SaveReel, reactId);

        }
        return null;
    }

    private String unReactReply(String ReplyId) {
        String id = userSessionData.getId();
        Aggregation query = queryNormalizeService.getUserFields(Criteria.where("_id").is(new ObjectId(id)));
        User user = mongoTemplate.aggregate(query, "user", User.class).getMappedResults().stream().findAny().orElse(null);
        if (user != null) {

            Reply reply = replyRepository.findById(ReplyId).orElse(null);

            Aggregation aggregation = queryNormalizeService.getReplyReactionFields(Criteria.where("reply._id").is(new ObjectId(ReplyId)));
            List<ReplyReaction> reactions = mongoTemplate.aggregate(aggregation, "replyReaction", ReplyReaction.class).getMappedResults();
            ReplyReaction myReact = reactions.stream()
                    .filter(_react -> _react.getUser().getId().equals(id)

                    ).findAny()
                    .orElse(null);


            if (myReact != null) {
                if (myReact.getUser().getId().equals(id))
                    replyReactionRepository.delete(myReact);
                if (reactions.size() > 0) {
                    reply.setNumberOfReactions(reactions.size() - 1);
                } else {
                    reply.setNumberOfReactions(0);
                }
            }
            replyRepository.save(reply);
        }
        return ReplyId;

    }

    @Transactional
    private ReactionSearch PostReact(String reactId, boolean React, String PostId, String UserId, ReactionType reactionType) {
        if (React) {
            PostReaction react = new PostReaction();
            Optional<Post> optionalPost = postRepository.findById(PostId);
            User user = userRepository.findById(UserId).orElseThrow(() -> new CustomException(404, "User not found!"));
            if (optionalPost.isPresent()) {
                Post post = optionalPost.get();
                searchStory searchStory = null;
                searchPost searchPost = null;

                if (post.getPostType().equals(PostType.Story))
                    searchStory = searchStoryRepository.findById(PostId).orElse(mapper.convertPostToSearchStory(post));
                if (post.getPostType().equals(PostType.Post))
                    searchPost = searchPostRepository.findById(PostId).orElse(mapper.convertPostToSearchPost(post));

                react.setUser(new User(UserId));
                react.setReactionType(reactionType);
                react.setPost(post);

                Aggregation aggregation = queryNormalizeService.getPostReactionFields(
                        Criteria.where("post._id").is(new ObjectId(PostId)));
                List<PostReaction> reactions = mongoTemplate.aggregate(aggregation, "postReaction", PostReaction.class)
                        .getMappedResults();

                PostReaction myReact = reactions.stream()
                        .filter(z -> z.getUser().getId().equals(UserId))
                        .findAny()
                        .orElse(null);

                if (myReact == null) {
                    post.setReactsCount(reactions.size() + 1);
                    if (post.getPostType().equals(PostType.Story))
                        searchStory.setReactsCount(reactions.size() + 1);
                    if (post.getPostType().equals(PostType.Post))
                        searchPost.setReactsCount(reactions.size() + 1);

                    postReactionRepository.save(react);
                    var savedPost = postRepository.save(post);

                    if (post.getPostType().equals(PostType.Story))
                        searchStoryRepository.save(searchStory);
                    if (post.getPostType().equals(PostType.Post))
                        searchPostRepository.save(searchPost);

                    // send notification to the tracked users
                    sendNotificationReactPost(savedPost, reactions, user);

                    var userPost = userRepository.findById(savedPost.getUser().getId()).get();
                    // Send notification to the influencer
                    sendNotificationToInfluencerForPost(savedPost, userPost, user);

                    return commonUtils.CreateReaction(PostId, UserId, reactionType, EntityName.Post, EntityType.React, reactId);
                } else {
                    ReactionSearch reactionSearch = commonUtils.getReaction(UserId, PostId, EntityType.React);
                    if (reactionSearch != null) {
                        reactionSearch.setReactionType(reactionType);

                        // send notification to the tracked users
                        sendNotificationReactPost(post, reactions, user);

                        // Send notification to the influencer
                        sendNotificationToInfluencerForPost(post, post.getUser(), user);
                        return reactionSearchRepository.save(reactionSearch);
                    } else {
                        // send notification to the tracked users
                        sendNotificationReactPost(post, reactions, user);

                        // Send notification to the influencer
                        sendNotificationToInfluencerForPost(post, post.getUser(), user);

                        return commonUtils.CreateReaction(PostId, UserId, reactionType, EntityName.Post, EntityType.React, reactId);
                    }
                }
            }
        } else {
            Optional<Post> optionalPost = postRepository.findById(PostId);
            if (optionalPost.isPresent()) {
                Post post = optionalPost.get();
                searchStory searchStory = null;
                searchPost searchPost = null;

                if (post.getPostType().equals(PostType.Story))
                    searchStory = searchStoryRepository.findById(PostId).get();
                if (post.getPostType().equals(PostType.Post))
                    searchPost = searchPostRepository.findById(PostId).get();

                Aggregation aggregation = queryNormalizeService.getPostReactionFields(
                        Criteria.where("post._id").is(new ObjectId(PostId)));
                List<PostReaction> reactions = mongoTemplate.aggregate(aggregation, "postReaction", PostReaction.class)
                        .getMappedResults();

                PostReaction react = reactions.stream()
                        .filter(z -> z.getUser().getId().equals(UserId))
                        .findAny()
                        .orElse(null);

                ReactionSearch reactionSearch = commonUtils.getReaction(UserId, PostId, EntityType.React);
                if (reactionSearch != null) {
                    reactionSearchRepository.delete(reactionSearch);
                }

                if (react != null) {
                    postReactionRepository.delete(react);
                    if (reactions.size() > 0) {
                        post.setReactsCount(reactions.size() - 1);
                        if (post.getPostType().equals(PostType.Story))
                            searchStory.setReactsCount(reactions.size() - 1);
                        if (post.getPostType().equals(PostType.Post))
                            searchPost.setReactsCount(reactions.size() - 1);
                    } else {
                        post.setReactsCount(0);
                        if (post.getPostType().equals(PostType.Story))
                            searchStory.setReactsCount(0);
                        if (post.getPostType().equals(PostType.Post))
                            searchPost.setReactsCount(0);
                    }

                    if (post.getPostType().equals(PostType.Story))
                        searchStoryRepository.save(searchStory);
                    if (post.getPostType().equals(PostType.Post))
                        searchPostRepository.save(searchPost);

                    postRepository.save(post);
                }
                return reactionSearch;
            }
        }
        return null;
    }

    private ReactionSearch FollowInfluence(String reactId, String Influence) {
        String id = userSessionData.getId();
        logger.info("user Id" + id);
        logger.info("Influence Id" + Influence);

        Criteria criteria = new Criteria();
        criteria.orOperator(
                Criteria.where("_id").is(new ObjectId(id)),
                Criteria.where("_id").is(new ObjectId(Influence))
        );

        Aggregation query = queryNormalizeService.getUserFields(criteria);
        List<User> users = mongoTemplate.aggregate(query, "user", User.class).getMappedResults();

        Optional<User> byId = users.stream().filter(z -> z.getId().equals(id)).findAny();
        Optional<User> InfluenceOp = users.stream().filter(z -> z.getId().equals(Influence)).findAny();

        if (InfluenceOp.isPresent() && byId.isPresent()) {
            User user = byId.get();
            User Influence1 = InfluenceOp.get();

            Criteria FollowCriteria = new Criteria();
            FollowCriteria.orOperator(
                    Criteria.where("_id.user._id").is(new ObjectId(Influence)),
                    Criteria.where("_id.follower._id").is(new ObjectId(id))
            );

          Aggregation followAggregation = queryNormalizeService.getFollowsFields(FollowCriteria, null, null);
           List<Follows> Follows = mongoTemplate.aggregate(followAggregation, "follows", Follows.class).getMappedResults();

            List<Follows> InfluenceFollow = Follows.stream()
                 .filter(z -> z.getId() != null && z.getId().getUser() != null && z.getId().getUser().getId() != null &&
                           z.getId().getUser().getId().equals(Influence)).toList();

            List<Follows> UserFollow = Follows.stream()
                 .filter(z ->z.getId().getFollower() != null && z.getId().getFollower().getId() != null &&
                         z.getId().getFollower().getId().equals(id)).toList();


            var reactionOpt = reactionSearchRepository.findByEntityNameAndEntityTypeAndEntityIdAndUserId(EntityName.User,
                    EntityType.Follow,
                    Influence,
                    id);

            if (reactionOpt.isEmpty()) {
                FollowsKey followsKey = new FollowsKey();
                followsKey.setFollower(user);
                followsKey.setUser(Influence1);

                Follows follows = new Follows();
                follows.setId(followsKey);

                Influence1.setFollwerscount(InfluenceFollow.size() + 1);
                user.setFollowingcount(UserFollow.size() + 1);

                followsRepository.save(follows);
                userRepository.save(user);
                userRepository.save(Influence1);

                var searchUser = searchUserRepository.findById(user.getId()).orElse(new SearchUser(user));
                var searchInfluencer = searchUserRepository.findById(Influence1.getId()).orElse(new SearchUser(Influence1));

                searchUser.setFollowingcount(user.getFollowingcount());
                searchInfluencer.setFollwerscount(Influence1.getFollwerscount());

                searchUserRepository.saveAll(List.of(searchUser, searchInfluencer));

                // Send notification to the influencer
                sendNotificationToInfluencerForFollow(user, Influence1);

                return commonUtils.CreateReaction(Influence, id, null, EntityName.User, EntityType.Follow, reactId);
            } else {
                throw new CustomException(500, "Influencer Already Followed");
            }
        }
        return null;
    }

    public void UnFollowInfluence(String reactId, String Influence) {
        String id = userSessionData.getId();
        Criteria criteria = new Criteria();
        criteria.orOperator(Criteria.where("_id").is(new ObjectId(id)),
                Criteria.where("_id").is(new ObjectId(Influence)));
        Aggregation query = queryNormalizeService.getUserFields(criteria);
        List<User> users = mongoTemplate.aggregate(query, "user", User.class).getMappedResults();
        Optional<User> byId = users.stream().filter(z -> z.getId().equals(id)).findAny();
        Optional<User> InfluenceOp = users.stream().filter(z -> z.getId().equals(Influence)).findAny();
        if (!InfluenceOp.isEmpty()) {
            if (!byId.isEmpty()) {
                User user = byId.get();
                User Influence1 = InfluenceOp.get();

                Criteria FollowCriteria = new Criteria();
                FollowCriteria.orOperator(Criteria.where("_id.user._id").is(new ObjectId(Influence)),
                        Criteria.where("_id.follower._id").is(new ObjectId(id)));
                Aggregation followAggregation = queryNormalizeService.getFollowsFields(FollowCriteria, null, null);
                List<Follows> Follows = mongoTemplate.aggregate(followAggregation, "follows", Follows.class).getMappedResults();

                Optional<Follows> follow = Follows
                        .stream()
                        .filter(z ->
                                z.getId() != null && z.getId().getUser() != null && z.getId().getUser().getId() != null &&
                                        z.getId().getFollower() != null && z.getId().getFollower().getId() != null &&
                                        z.getId().getUser().getId().equals(Influence)
                                        && z.getId().getFollower().getId().equals(id))
                        .findAny();
                var react = reactionSearchRepository.findById(reactId);
                if (follow.isPresent() || react.isPresent()) {
                    follow.ifPresent(followsRepository::delete);
                    reactionSearchRepository.deleteById(reactId);
                    long InfluencedFollowers = Follows
                            .stream()
                            .filter(z ->
                                    z.getId() != null && z.getId().getUser() != null && z.getId().getUser().getId() != null &&
                                            z.getId().getFollower() != null && z.getId().getFollower().getId() != null &&
                                            z.getId().getUser()
                                                    .getId().equals(Influence))
                            .count();
                    InfluencedFollowers--;
                    Influence1.setFollwerscount((int) InfluencedFollowers);
                    userRepository.save(Influence1);

                    long followingCounts = Follows
                            .stream()
                            .filter(z -> z.getId() != null && z.getId().getFollower() != null
                                    && z.getId().getFollower().getId() != null
                                    && z.getId().getFollower().getId().equals(id)
                            ).count();
                    followingCounts--;
                    user.setFollowingcount((int) followingCounts);
                    userRepository.save(user);


                    var searchUser = searchUserRepository.findById(user.getId()).orElse(new SearchUser(user));
                    var searchInfluencer = searchUserRepository.findById(Influence1.getId()).orElse(new SearchUser(Influence1));

                    searchUser.setFollowingcount(user.getFollowingcount());
                    searchInfluencer.setFollwerscount(Influence1.getFollwerscount());

                    searchUserRepository.saveAll(List.of(searchUser, searchInfluencer));
                }
            }
        }

    }

    @Transactional
    public ReactionSearch processPostAction(String id, String entityId, EntityType entityType, ReactionType reactionType) {
        return switch (entityType) {
            case React -> reactPostOrStory(id, entityId, reactionType);
            case PostMark -> addPostBookmark(id, entityId);
            case View -> viewStory(id, entityId);
            case Share -> SharePostOrStory(entityId);
            default -> throw new IllegalArgumentException("Unknown action for Post: " + entityType);
        };
    }

    private ReactionSearch processPackageAction(String id, String entityId, EntityType entityType) {
        if (entityType == EntityType.Favourite) {
            return addPackageToFavourites(id, entityId,PackageType.TravelWithMe);
        }

        throw new IllegalArgumentException("Unknown action for Package: " + entityType);
    }
    private ReactionSearch processFollowPackageAction(String id, String entityId, EntityType entityType) {
        if (entityType == EntityType.Favourite) {
            return addPackageToFavourites(id, entityId,PackageType.FollowMe);
        }

        throw new IllegalArgumentException("Unknown action for Package: " + entityType);
    }

    private ReactionSearch processCommentAction(String id, String entityId, EntityType entityType, ReactionType reactionType) {
        if (entityType == EntityType.React) {
            return reactComment(id, entityId, reactionType);
        }
        throw new IllegalArgumentException("Unknown action for Comment: " + entityType);
    }

    private ReactionSearch processMediaAction(String id, String entityId, EntityType entityType, ReactionType reactionType) {

        return switch (entityType) {
            case React -> reactMedia(id, entityId, reactionType);
            case SaveReel -> saveReel(id, entityId);
            default -> throw new IllegalArgumentException("Unknown action for Media: " + entityType);
        };
    }

    private ReactionSearch processUserAction(String reactId, String entityId, EntityType entityType) {
        if (entityType == EntityType.Follow) {
            return FollowInfluence(reactId, entityId);
        }
        if(entityType==EntityType.Share){
            return ShareUser(entityId);

        }
        throw new IllegalArgumentException("Unknown action for User: " + entityType);
    }
    @Transactional
    public ReactionSearch ShareUser(String entityId) {
        String id = userSessionData.getId();
        Aggregation query = queryNormalizeService.getUserFields(Criteria.where("_id").is(new ObjectId(id)));
        User user = mongoTemplate.aggregate(query, "user", User.class).getMappedResults().stream().findAny().orElse(null);
        int Sharingnumber= user.getNumberOfSharingContent();
        user.setNumberOfSharingContent(Sharingnumber+1);

         Aggregation sharedQuery = queryNormalizeService.getUserFields(Criteria.where("_id").is(new ObjectId(entityId)));
        User shareduser = mongoTemplate.aggregate(sharedQuery, "user", User.class).getMappedResults().stream().findAny().orElse(null);
        int sharedCount = shareduser.getSharesCount();
        shareduser.setSharesCount(sharedCount+1);

        userRepository.saveAll(List.of(user, shareduser));

        var searchUser = searchUserRepository.findById(user.getId()).orElse(new SearchUser(user));
        var searchInfluencer = searchUserRepository.findById(shareduser.getId()).orElse(new SearchUser(shareduser));

        searchUser.setNumberOfSharingContent(user.getNumberOfSharingContent());
        searchInfluencer.setSharesCount(shareduser.getSharesCount());

        searchUserRepository.saveAll(List.of(searchUser, searchInfluencer));
          return  null;
    }
    private void removePostReaction(String reactId, String entityId, EntityType entityType) {
        if (entityType == EntityType.React) {
            // Logic for removing post reaction
            unReactPostOrStory(reactId, entityId);
        } else if (entityType == EntityType.PostMark) {
            // Logic for removing post bookmark
            removePostBookmark(reactId, entityId);
        }
    }

    private void removePackageReaction(String reactId, String entityId, EntityType entityType) {
        if (entityType == EntityType.Favourite) {
            removePackageFavourite(reactId, entityId);
            // Logic for removing package from favorites
        }
    }

    private void removeCommentReaction(String reactId, String entityId, EntityType entityType) {
        if (entityType == EntityType.React) {
            unReactComment(reactId, entityId);
        }
    }

    private void removeMediaReaction(String reactId, String entityId, EntityType entityType) {
        if (entityType == EntityType.React) {
            // Logic for removing media reaction
            unReactMedia(reactId, entityId);
        }
        else if(entityType == EntityType.SaveReel){
            unsaveReel(reactId, entityId);
        }
    }


    public void sendNotificationReactPost(Post post, List<PostReaction> reactions, User reactedUser) {
        for (PostReaction reaction : reactions) {
             var reactUser = reaction.getUser();
            // Send notification to user to inform him with status change
            notificationService.sendAndStoreNotification(
                    post.getId(),
                    post.getPostType() == PostType.Post ? NotificationType.ReactOnLikedPost : NotificationType.ReactOnLikedStory,
                    reactUser,
                    List.of(post, reactedUser), reactedUser.getProfileImage(),
                    null,
                    NotificationEntityType.POST,
                    reactedUser,
                    post.getSlug(),
                    reactedUser.getUsername(), null);
        }
    }


    public void sendNotificationReactComment(Comment comment, User user, User reactedUser) {
        // Send notification to user to inform him with status change
        notificationService.sendAndStoreNotification(
                comment.getId(),
                NotificationType.ReactOnMyComment,
                user,
                List.of(comment, reactedUser), reactedUser.getProfileImage(),
                null,
                NotificationEntityType.POST,
                reactedUser,
                comment.getPost().getSlug(),
                reactedUser.getUsername(), null);

    }

    public void sendNotificationToInfluencerForPost(Post post, User influencer, User reactedUser){
        notificationService.sendAndStoreNotification(
                reactedUser.getId(),
                post.getPostType() == PostType.Post ? NotificationType.ReactOnMyPost: NotificationType.ReactOnMyStory,
                influencer,
                List.of(post, reactedUser),
                reactedUser.getProfileImage(),
                null,
                NotificationEntityType.POST,
                reactedUser,
                post.getSlug(),
                reactedUser.getUsername(), null);
    }

    public void sendNotificationToInfluencerForFollow(User follower, User influencer){
        notificationService.sendAndStoreNotification(
                follower.getId(),
                NotificationType.Follow,
                influencer,
                List.of(follower), follower.getProfileImage(),
                null,
                NotificationEntityType.USER,
                follower,
                null,
                follower.getUsername(), UserType.Traveler);
    }

    public void SendNotificationForReactReel(Media media, User reactedUser) {
        User owner = userRepository.findById(media.getUser().getId())
                .orElseThrow(() -> new CustomException(400, "The media belong to non existing user"));
        notificationService.sendAndStoreNotification(
                reactedUser.getId(),
                NotificationType.ReactOnMyReel,
                owner,
                List.of(media, reactedUser),
                reactedUser.getProfileImage(),
                null,
                NotificationEntityType.MEDIA,
                reactedUser,
                null,
                reactedUser.getUsername(), null);
    }
}
