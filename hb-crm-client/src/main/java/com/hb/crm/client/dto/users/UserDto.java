package com.hb.crm.client.dto.users;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hb.crm.client.dto.mood.MoodDto;
import com.hb.crm.core.Enums.Gender;
import com.hb.crm.core.Enums.UserType;
import com.hb.crm.core.beans.UserInfo;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class UserDto {
    private String id;
    private LocalDateTime creationDate;
    private LocalDateTime updatedDate;
    private String about;

    private String firstName;
    private String lastName;
    private String username;

    private String email;
    private String mobile;
    private UserInfo userInfo;
    private Gender gender;
    protected String code;
    private String role;
    private String collectionDeviceId;
    private boolean accountLocked = false;
    private String city;
    private String country;
    private String token;
    private String coverImage;
    private UserType usertype = UserType.Traveler;
    private boolean privateProfile = false;

    private String profileImage;
    private boolean EmailActivated = false;
    private String EmailCode = "";
    private boolean PhoneActiviated = false;

    private boolean hasMood = false;

    private String PhoneCode = "";
    private boolean LookoutEnabled = false;
    private Number follwerscount = 0;
    private int failedPasswordChanges = 0;
    private int failedLoginAttempts = 0;
    @JsonIgnore
    private List<MoodDto> moods;

    
    private boolean hasLiveStream;
    private String liveStreamId;
    private String liveStreamPlaybackUrl;

}
