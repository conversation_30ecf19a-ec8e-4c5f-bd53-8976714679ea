package com.hb.crm.client.services;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hb.crm.client.CommonService.CommonUtils;
import com.hb.crm.client.CommonService.Mapper;
import com.hb.crm.client.CommonService.SpectrumCommon;
import com.hb.crm.client.beans.ObjectBoxEntity.PackageSyncEntity;
import com.hb.crm.client.beans.UserSessionData;
import com.hb.crm.client.config.CustomHundlerar.CustomException;
import com.hb.crm.client.config.DateFormatter;
import com.hb.crm.client.dto.*;
import com.hb.crm.client.dto.SuccessMessage;
import com.hb.crm.core.dtos.CreateMediaDto;
import com.hb.crm.client.dto.PackageDtos.*;
import com.hb.crm.client.dto.Results.PackageDtoResultDto;
import com.hb.crm.client.dto.Results.PackageResultDto;
import com.hb.crm.client.dto.Results.searchResultDto;
import com.hb.crm.client.dto.posts.PostDto;
import com.hb.crm.client.dto.spectTrum.BookPackage;
import com.hb.crm.client.dto.spectTrum.FlightPrices;
import com.hb.crm.client.dto.spectTrum.FlightResponse.ConfirmPirceResponse;
import com.hb.crm.client.dto.spectTrum.FlightSearch.*;
import com.hb.crm.client.dto.spectTrum.Price;
import com.hb.crm.client.dto.spectTrum.PriceAndCheckoutUrl;
import com.hb.crm.client.services.interfaces.*;
import com.hb.crm.core.CashService.CashService;
import com.hb.crm.core.CombinedKeys.FollowsKey;
import com.hb.crm.core.CombinedKeys.SubscribeKey;
import com.hb.crm.core.CombinedKeys.UserPackageKey;
import com.hb.crm.core.Enums.*;
import com.hb.crm.core.beans.*;
import com.hb.crm.core.beans.Flight.Flight;
import com.hb.crm.core.beans.Flight.FlightsDirections;
import com.hb.crm.core.beans.Hotel.Hotel;
import com.hb.crm.core.beans.Hotel.HotelAvilabiltyResponse;
import com.hb.crm.core.beans.Hotel.ReservedRoom;
import com.hb.crm.core.beans.Hotel.RoomReservation;
import com.hb.crm.core.beans.Package;
import com.hb.crm.core.beans.PackagePlaces.*;
import com.hb.crm.core.dtos.CreateUpdatePackageDto;
import com.hb.crm.core.dtos.PackageEditRequest;
import com.hb.crm.core.dtos.PageDto;
import com.hb.crm.core.dtos.RefranceModelDto;
import com.hb.crm.core.repositories.*;
import com.hb.crm.core.repositories.chat.GroupConversationRepository;
import com.hb.crm.core.searchBeans.ReactionSearch;
import com.hb.crm.core.searchBeans.searchPackage;
import com.hb.crm.core.searchRepositories.ReactionSearchRepository;
import com.hb.crm.core.searchRepositories.SearchPackageRepository;
import com.hb.crm.core.services.chat.ChatMessageService;
import com.hb.crm.core.services.interfaces.NotificationService;
import com.hb.crm.core.util.ApplicationUtil;
import com.mongodb.MongoException;
import org.bson.types.ObjectId;
import org.jetbrains.annotations.Nullable;
import org.joda.time.DateTime;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;

@Service
public class PackageServiceImpl implements PackageService {

    private static final int ALL_LIMIT = 99999;
    private final PackageRepository packageRepository;
    private final UserRepository userRepository;
    private final SubscribeRepository subscribeRepository;
    private final SubPackageRepository subPackageRepository;
    private final PackageNotificationRepository packageNotificationRepository;
    private final PackageReactionRepository packageReactionRepository;
    private final MongoTemplate mongoTemplate;
    private final MongoTemplate searchmongoTemplate;
    private final UserSessionData userSessionData;
    private final com.hb.crm.client.services.interfaces.MediaService mediaService;
    private final ActivityCloneRepository activityCloneRepository;
    private final SettingService settingService;
    private final Spectrum spectrum;
    private final StripePaymentService paymentService;
    private final CashService cashService;
    private final TagService tagService;
    private final QueryNormalizeService queryNormalazieService;
    private final AtlasService atlasService;
    private final SpectrumCommon spectrumCommon;
    private final CountryRepository countryRepository;
    private final CityRepository cityRepository;
    private final AreaRepository areaRepository;
    private final CommonUtils commonUtils;
    private final Mapper mapper;
    private final SearchPackageRepository searchPackageRepository;
    private final Logger logger = LoggerFactory.getLogger(PackageServiceImpl.class);
    private final ActivityRepository activityRepository;
    private final ActivityCategoryRepository activityCategoryRepository;
    private final SearchService searchService;
    private final MediaRepository mediaRepository;
    private final TravelerSubscribeRepository travelerSubscribeRepository;
    private final ReactionSearchRepository reactionSearchRepository;
    private final ChatMessageService chatMessageService;
    private final GroupConversationRepository groupConversationRepository;
    private final NotificationService notificationService;
    private final PackageNotificationSettingRepository packageNotificationSettingRepository;
    private final ModelMapper modelMapper;
    private final QueryNormalizeService queryNormalizeService;

    public PackageServiceImpl(PackageRepository packageRepository,
                              UserRepository userRepository,
                              SubscribeRepository subscribeRepository,
                              SubPackageRepository subPackageRepository,
                              PackageNotificationRepository packageNotificationRepository,
                              PackageReactionRepository packageReactionRepository,
                              @Qualifier("mongoTemplate1") MongoTemplate mongoTemplate,
                              @Qualifier("mongoTemplate2") MongoTemplate searchmongoTemplate,
                              UserSessionData userSessionData,
                              MediaService mediaService, ActivityCloneRepository activityCloneRepository,
                              SettingService settingService, Spectrum spectrum, StripePaymentService paymentService,
                              CashService cashService, QueryNormalizeService queryNormalazieService,
                              AtlasService atlasService, SpectrumCommon spectrumCommon,
                              CountryRepository countryRepository,
                              CityRepository cityRepository, AreaRepository areaRepository,
                              CommonUtils commonUtils,
                              Mapper mapper, com.hb.crm.core.searchRepositories.SearchPackageRepository searchPackageRepository,
                              ActivityRepository activityRepository, ActivityCategoryRepository activityCategoryRepository,
                              SearchService searchService, MediaRepository mediaRepository,
                              TravelerSubscribeRepository travelerSubscribeRepository,
                              ReactionSearchRepository reactionSearchRepository, ChatMessageService chatMessageService,
                              GroupConversationRepository groupConversationRepository, NotificationService notificationService
                             , PackageNotificationSettingRepository packageNotificationSettingRepository, ModelMapper modelMapper, TagService tagService, QueryNormalizeService queryNormalizeService) {
        this.packageRepository = packageRepository;
        this.userRepository = userRepository;
        this.subscribeRepository = subscribeRepository;
        this.subPackageRepository = subPackageRepository;
        this.packageNotificationRepository = packageNotificationRepository;
        this.packageReactionRepository = packageReactionRepository;
        this.mongoTemplate = mongoTemplate;
        this.searchmongoTemplate = searchmongoTemplate;
        this.userSessionData = userSessionData;
        this.mediaService = mediaService;
        this.activityCloneRepository = activityCloneRepository;
        this.settingService = settingService;
        this.spectrum = spectrum;
        this.paymentService = paymentService;
        this.cashService = cashService;
        this.tagService = tagService;
        this.queryNormalazieService = queryNormalazieService;
        this.atlasService = atlasService;
        this.spectrumCommon = spectrumCommon;
        this.countryRepository = countryRepository;
        this.cityRepository = cityRepository;
        this.areaRepository = areaRepository;
        this.commonUtils = commonUtils;
        this.mapper = mapper;
        this.searchPackageRepository = searchPackageRepository;
        this.activityRepository = activityRepository;
        this.activityCategoryRepository = activityCategoryRepository;
        this.searchService = searchService;
        this.mediaRepository = mediaRepository;
        this.travelerSubscribeRepository = travelerSubscribeRepository;
        this.reactionSearchRepository = reactionSearchRepository;
        this.chatMessageService = chatMessageService;
        this.groupConversationRepository = groupConversationRepository;
        this.notificationService = notificationService;
        this.packageNotificationSettingRepository = packageNotificationSettingRepository;
        this.modelMapper = modelMapper;
        this.queryNormalizeService = queryNormalizeService;
    }

    @Override
    @Transactional
    public void updatePackageInfo(PackageSyncEntity syncedPackageInfo) {
        SubPackage subPackage = subPackageRepository.findById(syncedPackageInfo.getPackageId())
                .orElseThrow(() -> new CustomException(404, "Package not found!"));
        subPackage.setCapacity(syncedPackageInfo.getCapacity());
        subPackage.setSubscribeCount(syncedPackageInfo.getSubscribeCount());
        subPackageRepository.save(subPackage);
    }

    @Override
    public PageDto<PlacesDto> getPackagePlacesByInfluencerId(String influencerId, Pageable pageable) {
        List<PlacesDto> allPlaces = new ArrayList<>();

        // Fetch package places
        List<PackageCountry> packageCountries = packageRepository.findByInfulancerId(influencerId)
                .stream()
                .flatMap(pkg -> pkg.getPackagePlaces() != null
                        ? pkg.getPackagePlaces().stream()
                        : Stream.empty())
                .toList();

        // Extract country and city IDs
        Set<String> countryIds = packageCountries.stream()
                .map(PackageCountry::getPropertyId)
                .collect(Collectors.toSet());

        Set<String> cityIds = packageCountries.stream()
                .flatMap(pkgCountry -> pkgCountry.getCities() != null
                        ? pkgCountry.getCities().stream().map(PackageCity::getPropertyId)
                        : Stream.empty())
                .collect(Collectors.toSet());

        // Retrieve and map countries to PlaceDto
        List<PlacesDto> countryPlaces = countryRepository.findAllById(countryIds)
                .stream()
                .map(country -> new PlacesDto(
                        country.getId(),
                        country.getName(),
                        country.getDescription(),
                        country.getMedias()))
                .toList();

        // Retrieve and map cities to PlaceDto
        List<PlacesDto> cityPlaces = cityRepository.findAllById(cityIds)
                .stream()
                .map(city -> new PlacesDto(
                        city.getId(),
                        city.getName(),
                        city.getDescription(),
                        city.getMedias()))
                .toList();


        // Combine lists and ensure distinct entries
        allPlaces.addAll(countryPlaces);
        allPlaces.addAll(cityPlaces);
        List<PlacesDto> distinctPlaces = allPlaces.stream()
                .distinct()
                .toList();

        // Pagination logic
        int start = (int) pageable.getOffset();
        int end = Math.min(start + pageable.getPageSize(), allPlaces.size());

        List<PlacesDto> paginatedList = allPlaces.subList(start, end);

        var result = new PageDto<PlacesDto>();
        result.setItems(paginatedList);
        result.setTotalNoOfItems(allPlaces.size());

        return result;
    }

    @Override
    public PageDto<CountSubPackage> search(int pageNumber, PackageType packageTypeFilter) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();

        // retrieve the user
        Aggregation query = queryNormalazieService.getUserFields(Criteria.where("_id").is(new ObjectId(userSessionData.getId())));

        User user = mongoTemplate.aggregate(query, "user", User.class)
                .getMappedResults()
                .stream()
                .findAny()
                .orElse(null);

        // retrieve the influencer that the user follow

        Aggregation followedAggregation = queryNormalazieService.getFollowsFields(Criteria.where("_id.follower._id").is(new ObjectId(user.getId())), null, null);
        List<Follows> allFollows = mongoTemplate.aggregate(followedAggregation, "follows", Follows.class).getMappedResults();
        List<String> usersId = allFollows.stream()
                .map(Follows::getId)
                .map(FollowsKey::getUser)
                .filter(Objects::nonNull)
                .map(User::getId)
                .sorted()
                .toList();
        List<String> moods = new ArrayList<>();
        if (user.getMoods() != null) {
            moods = user.getMoods()
                    .stream()
                    .map(Mood::getTitle)
                    .sorted().collect(Collectors.toList());
        }
        // Get IDs of all packages the user is subscribed to
        List<String> packagesId = getSubscribedPackages().stream().map(searchPackage::getId).toList();

        String key = pageNumber + "_" + String.join(", ", moods) + "_" + String.join(", ", "usersId");

        String cachedValue = (String) cashService.retrieveData(key);
        if (cachedValue == null) {
            List<ObjectId> objectsId = usersId
                    .stream()
                    .map(ObjectId::new)
                    .toList();

            List<ObjectId> packagesobjectsId = packagesId
                    .stream()
                    .map(ObjectId::new)
                    .toList();

            Aggregation aggregation = atlasService.PackageSorting(moods, objectsId, packagesobjectsId, packageTypeFilter);
            List<CountSubPackage> Packages = searchmongoTemplate.aggregate(aggregation, "search", CountSubPackage.class)
                    .getMappedResults();

            PageDto<CountSubPackage> page = new PageDto<>();
            page.setItems(Packages);
            page.setTotalNoOfItems(Packages.size());

            return page;

        } else {
            List countSubPackage = objectMapper.readValue(cachedValue, List.class);
            PageDto<CountSubPackage> page = new PageDto<>();
            page.setItems(countSubPackage);
            page.setTotalNoOfItems(countSubPackage.size());
            return page;
        }
    }


    @Override
    public PageDto<PackageDto> search(Map<String, Object> obj, int page, int limit) throws JsonProcessingException {
        if (page < 0) {
            limit = ALL_LIMIT;
            page = 0;
        }

        PageDto<PackageDto> pageDto = new PageDto<>();
        final Pageable pageable = ApplicationUtil.createPageRequest(page, limit, "start", "ASC");
        final Criteria searchQuery = createSearchSpecification(obj);

        searchQuery.and("packageStatus").is(PackageStatus.posted);
        searchQuery.and("packageType").is(PackageType.TravelWithMe);
        searchQuery.orOperator(
                Criteria.where("state").is(State.OnGoing.getValue()),
                Criteria.where("state").is(State.NotStarted.getValue())
        );

        Aggregation aggregation = queryNormalazieService.getPackage(searchQuery, pageable);
        AggregationResults<PackageResultDto> Results = mongoTemplate.aggregate(aggregation, "subPackage", PackageResultDto.class);
        List<PackageDto> aggregationResults = Objects.requireNonNull(Results.getUniqueMappedResult()).getFilteredResults()
                .stream()
                .map(mapper::convertToDto)
                .collect(Collectors.toList());

        pageDto.setItems(aggregationResults);
        long totalCount = Results.getUniqueMappedResult() != null ? Results.getUniqueMappedResult().getTotalCount() : 0;
        pageDto.setTotalNoOfItems(totalCount);
        return pageDto;
    }

    @Override
    public PageDto<PackageDto> searchAvailableFollowMe(Map<String, Object> obj, int page, int limit) {
        if (page < 0) {
            limit = ALL_LIMIT;
            page = 0;
        }

        PageDto<PackageDto> pageDto = new PageDto<>();
        final Pageable pageable = ApplicationUtil.createPageRequest(page, limit, "start", "ASC");
        final Criteria searchQuery = createSearchSpecification(obj);

        searchQuery.and("packageStatus").is(PackageStatus.posted);
        searchQuery.and("packageType").is(PackageType.TravelWithMe);
        searchQuery.and("state").is(State.Completed.getValue());
        searchQuery.and("_package.availableForFollowMe").is(true);
        Aggregation aggregation = queryNormalazieService.getPackage(searchQuery, pageable);
        AggregationResults<PackageResultDto> Results = mongoTemplate.aggregate(aggregation, "subPackage", PackageResultDto.class);
        List<PackageDto> aggregationResults = Objects.requireNonNull(Results.getUniqueMappedResult()).getFilteredResults()
                .stream()
                .map(mapper::convertToDto)
                .collect(Collectors.toList());


        long totalCount = Results.getUniqueMappedResult() != null ? Results.getUniqueMappedResult().getTotalCount() : 0;
        pageDto.setTotalNoOfItems(totalCount);
        pageDto.setItems(aggregationResults);

        return pageDto;
    }

    @Override
    public PackageDto getPackageById(String id) {
        if (id == null)
            return null;

        final Optional<SubPackage> byId = subPackageRepository.findById(id);

        if (byId.isEmpty())
            return null;

        PackageDto _package = mapper.convertToDto(byId.get());


        return _package;
    }

    @Override
    public void postPackage(String id) {
        final Optional<SubPackage> byId = subPackageRepository.findById(id);
        final searchPackage searchPackage = searchPackageRepository.findById(id).orElseThrow();

        if (byId.isPresent()) {
            SubPackage _package = byId.get();
            _package.setPackageStatus(PackageStatus.posted);
            searchPackage.setPackageStatus(PackageStatus.posted);

            if (_package.get_package().getInfulancer().getId().equals(userSessionData.getId())) {
                var savedPackage = subPackageRepository.save(_package);
                searchPackageRepository.save(searchPackage);

                var packageImageMedia = savedPackage.get_package().getMedias()
                        .stream()
                        .filter(MediaWrapper::isMainImage)
                        .toList()
                        .getFirst();

                String packageImageMediaUrl = null;
                if (packageImageMedia != null)
                    packageImageMediaUrl = packageImageMedia.getUrl();


                // Find the user that follows the influencer of the package and send them notification.
                var packageInfluencerFollowers = findFollowers(savedPackage.get_package().getInfulancer().getId());

                for (User follower : packageInfluencerFollowers) {
                    notificationService.sendAndStoreNotification(
                            savedPackage.getId(),
                            NotificationType.FollowedInfluencerNewPackage,
                            follower,
                            List.of(savedPackage, savedPackage.get_package().getInfulancer()),
                            savedPackage.get_package().getInfulancer().getProfileImage(),
                            packageImageMediaUrl,
                            NotificationEntityType.PACKAGE,
                            savedPackage.get_package().getInfulancer(),
                            savedPackage.getSlug(),
                            savedPackage.get_package().getInfulancer().getUsername(), null);
                }

                // Send notification to users that have the same moods as the package
                var packageMoods = savedPackage.get_package().getMoods();
                List<User> matchedMoodsUsers = userRepository.findByMoodsIn(packageMoods);

                for (User mathcedUser : matchedMoodsUsers) {
                    if (mathcedUser.getFcmTokens() != null && !mathcedUser.getFcmTokens().isEmpty()) {
                        notificationService.sendAndStoreNotification(
                                savedPackage.getId(),
                                NotificationType.MatchedMoodsNewPackage,
                                mathcedUser,
                                List.of(savedPackage, savedPackage.get_package().getInfulancer()),
                                savedPackage.get_package().getInfulancer().getProfileImage(),
                                packageImageMediaUrl,
                                NotificationEntityType.PACKAGE,
                                savedPackage.get_package().getInfulancer(),
                                savedPackage.getSlug(),
                                savedPackage.get_package().getInfulancer().getUsername(), null);
                    }
                }
            }
        }
    }

    @Override
    public PackageDto RatePackage(RateDto ratedto) {
        String userId = userSessionData.getId();
        final Optional<SubPackage> byId = subPackageRepository.findById(ratedto.getPackageId());

        if (byId.isEmpty())
            return null;

        final searchPackage searchPackage = searchPackageRepository.findById(ratedto.getPackageId()).orElseThrow();
        Package _package = byId.get().get_package();
        List<Rate> pRates = _package.getRates();

        if (pRates == null)
            pRates = new ArrayList<>();

        Rate oldRate = pRates.stream().filter(z -> z.getUserID().equals(userId)).findAny().orElse(null);

        if (oldRate == null) {
            Rate rate = new Rate();
            rate.setRate(ratedto.getRate());
            rate.setUserID(userId);
            pRates.add(rate);
        } else {
            int index = pRates.indexOf(oldRate);
            pRates.remove(oldRate);
            oldRate.setRate(ratedto.getRate());
            pRates.add(index, oldRate);
        }
        double average = pRates.stream().mapToDouble(Rate::getRate).average().orElse(0);
        searchPackage.setAvgRate(average);
        _package.setRates(pRates);
        searchPackage.setRates(pRates);
        packageRepository.save(_package);
        searchPackageRepository.save(searchPackage);
        return mapper.convertToDto(byId.get());
    }

    @Override
    public PackageDto RateFollowMePackage(RateDto ratedto) {
        String userId = userSessionData.getId();
        final Optional<SubPackage> byId = subPackageRepository.findById(ratedto.getPackageId());

        if (byId.isEmpty())
            return null;

        final searchPackage searchPackage = searchPackageRepository.findById(ratedto.getPackageId()).orElseThrow();
        SubPackage _package = byId.get();
        List<Rate> pRates = _package.getRates();

        if (pRates == null)
            pRates = new ArrayList<>();

        Rate oldRate = pRates.stream().filter(z -> z.getUserID().equals(userId)).findAny().orElse(null);

        if (oldRate == null) {
            Rate rate = new Rate();
            rate.setRate(ratedto.getRate());
            rate.setUserID(userId);
            pRates.add(rate);
        } else {
            int index = pRates.indexOf(oldRate);
            pRates.remove(oldRate);
            oldRate.setRate(ratedto.getRate());
            pRates.add(index, oldRate);
        }

        double average = pRates.stream().mapToDouble(Rate::getRate).average().orElse(0);
        searchPackage.setFollowMeAvgRate(average);
        searchPackage.setFollowMeRates(pRates);
        _package.setRates(pRates);

        subPackageRepository.save(_package);
        searchPackageRepository.save(searchPackage);
        return mapper.convertToDto(byId.get());
    }

    /**
     * Updates or creates a package and its associated subpackage based on the provided data.
     *
     * <p>This method handles both creation of new packages and updates to existing ones.
     * It performs the following operations:</p>
     *
     * <ul>
     *   <li>Converts the input DTO to include media wrapper information</li>
     *   <li>Maps the DTO to entity objects (Package and SubPackage)</li>
     *   <li>Retrieves the current user from the session</li>
     *   <li>Sets package dates based on start date and duration</li>
     *   <li>For new packages: creates and saves new entities with current timestamps</li>
     *   <li>For existing packages: preserves certain fields while updating others</li>
     *   <li>Saves package data to both main and search repositories</li>
     *   <li>Updates media ownership information</li>
     * </ul>
     *
     * <p><strong>Package Creation Flow:</strong></p>
     * <ul>
     *   <li>Sets the current user as the package influencer</li>
     *   <li>Sets creation and update timestamps</li>
     *   <li>Saves the main package if it's a "TravelWithMe" type</li>
     *   <li>Saves the subpackage</li>
     *   <li>Creates and saves search index entry</li>
     *   <li>Automatically subscribes the creator to the package</li>
     * </ul>
     *
     * <p><strong>Package Update Flow:</strong></p>
     * <ul>
     *   <li>Retrieves existing package data</li>
     *   <li>Preserves critical fields (creation date, status, rejection notes, etc.)</li>
     *   <li>Updates modification timestamp</li>
     *   <li>Saves updated entities to both repositories</li>
     * </ul>
     *
     * @param obj The package data transfer object containing all package information
     *            including basic details, media, places, activities, and metadata
     * @throws RuntimeException         if the current user cannot be retrieved from session
     * @throws IllegalArgumentException if the package ID exists but the package cannot be found
     * @see CreateUpdatePackageDto
     * @see Package
     * @see SubPackage
     * @see PackageType#TravelWithMe
     */
    @Override
    @Transactional
    public SuccessMessage update(CreateUpdatePackageDto obj) {
        var generatedMedia = generateMediaWithWrapper(obj.getMedias());
        obj.setMedias(new ArrayList<>());

        if(obj.getId() != null) {
            var subPackage = subPackageRepository.findById(obj.getId()).orElseThrow(() ->
                    new CustomException(404, "Package not found with that id"));

            if(subPackage.getPackageStatus().equals(PackageStatus.posted)) {
                createUpdateRequest(obj, subPackage);
                return new SuccessMessage("Update Request created");
            }
        }


        // Map DTOs to entity objects
        Package _package = mapper.convertToEntity(obj);
        SubPackage _sub = mapper.convertToSubEntity(obj);

        _package.setMedias(generatedMedia);
        if (obj.getTags() != null && !obj.getTags().isEmpty())
            obj.setTags(tagService.checkNewTags(obj.getTags()));
        // Retrieve current user from session using an aggregation query
        Aggregation query = queryNormalazieService.getUserFields(Criteria.where("_id").is(new ObjectId(userSessionData.getId())));
        User user = mongoTemplate.aggregate(query, "user", User.class).getMappedResults().stream().findAny().orElse(null);

        if (user != null) {
            // Calculate package end date based on start date and duration
            _sub.setStart(DateFormatter.formatLocalDateTime(obj.getStart()));
            _sub.setEnd(obj.getEnd());

            if (obj.getId() == null) {
                // **NEW PACKAGE CREATION FLOW**

                // Set the current user as the package influencer
                _package.setInfulancer(user);

                // Set timestamps for new package creation
                _sub.setCreationDate(DateFormatter.formatLocalDateTime(LocalDateTime.now()));
                _sub.setUpdateDate(DateFormatter.formatLocalDateTime(LocalDateTime.now()));

                // Establish relationship between sub-package and main package
                _sub.set_package(_package);

                // Save main package only if it's a "TravelWithMe" type
                if (_sub.getPackageType().equals(PackageType.TravelWithMe))
                    packageRepository.save(_package);

                // Save the subpackage entity
                var savedPackage = subPackageRepository.save(_sub);

                // Create and save search index entry for discoverability
                searchPackage searchPackage = mapper.convertPackageToSearchPackage(_sub);
                PackageType packageType = searchPackage.getPackageType().equals(PackageType.FollowMe) ? PackageType.TravelWithMe : PackageType.FollowMe;
                searchPackage OtherPackage = searchPackageRepository.findByPackageIdAndPackageType(_package.getId(), packageType).orElse(null);
                if (OtherPackage != null) {
                    OtherPackage.setMedias(searchPackage.getMedias());
                    OtherPackage.setTags(searchPackage.getTags());
                    OtherPackage.setMoods(searchPackage.getMoods());
                    searchPackageRepository.save(OtherPackage);
                }
                searchPackageRepository.save(searchPackage);

                // Automatically subscribe the package creator
                this.SubscribePackage(_sub.getId());

                // Send Notification
                sendNotificationForCreatePackage(savedPackage);

            }
            else {
                // ** EXISTING PACKAGE UPDATE FLOW **

                // Retrieve existing package data to preserve certain fields
                SubPackage sPackage = subPackageRepository.findById(obj.getId())
                        .orElseThrow(() -> new CustomException(404, "Package not found!"));

                // Preserve critical fields that shouldn't be modified during updates
                _sub.setCreationDate(sPackage.getCreationDate());
                _sub.setPackageStatus(sPackage.getPackageStatus());
                _sub.setRejectionNote(sPackage.getRejectionNote());
                _sub.setStart(DateFormatter.formatLocalDateTime(sPackage.getStart()));
                _sub.setEnd(DateFormatter.formatLocalDateTime(sPackage.getEnd()));
                _sub.setState(sPackage.getState());

                // Preserve main package fields and relationships
                _package.setId(sPackage.get_package().getId());
                _package.setStart(DateFormatter.formatLocalDateTime(sPackage.get_package().getStart()));
                _package.setEnd(DateFormatter.formatLocalDateTime(sPackage.get_package().getEnd()));
                _package.setAvailableForFollowMe(sPackage.get_package().isAvailableForFollowMe());
                _package.setAvailableFrom(sPackage.get_package().getAvailableFrom());
                _package.setFollowMeDiscount(sPackage.get_package().getFollowMeDiscount());

                // Ensure influencer is set (fallback to current user if null)
                if (_package.getInfulancer() == null)
                    _package.setInfulancer(user);

                // Update the modification timestamp
                _sub.setUpdateDate(DateFormatter.formatLocalDateTime(LocalDateTime.now()));

                // Re-establish package relationship
                _sub.set_package(_package);

                // Save main package only if it's a "TravelWithMe" type
                if (_sub.getPackageType().equals(PackageType.TravelWithMe))
                    packageRepository.save(_package);

                // Save updated sub-package
                subPackageRepository.save(_sub);

                // Update search index with modified package data
                searchPackage searchPackage = mapper.convertPackageToSearchPackage(_sub);
                searchPackageRepository.save(searchPackage);
            }

            // ** MEDIA OWNERSHIP UPDATE **
            // Update media ownership information if package has associated media
            if (_package.getMedias() != null) {
                List<Media> mediasToSave = new ArrayList<>();

                // Extract media objects from wrappers and update user ownership
                _package.getMedias().stream()
                        .map(MediaWrapper::getMedia)
                        .filter(Objects::nonNull)
                        .forEach(media -> {
                            media.setUser(user);
                            media.set_package(_package);
                            mediasToSave.add(media);
                        });

                // Batch saves all media ownership updates
                mediaRepository.saveAll(mediasToSave);
            }
        }
        return new SuccessMessage("Package created successfully");
    }

    private void createUpdateRequest(CreateUpdatePackageDto modifications, SubPackage subPackage) {
        List<PackageEditRequest> modifyRequests = subPackage.getModifyRequest();

        if (modifyRequests == null) {
            modifyRequests = new ArrayList<>();
            subPackage.setModifyRequest(modifyRequests);
        }

        // Find if there's a non-approved request
        PackageEditRequest nonApprovedRequest = modifyRequests.stream()
                .filter(request -> !request.isApproved())
                .findFirst()
                .orElse(null);

        if (nonApprovedRequest != null) {
            // If there's a non-approved request, just update its modifications
            nonApprovedRequest.setModifications(modifications);
        } else {
            // If there's no non-approved request, create a new one with incremented version
            int newVersion = modifyRequests.stream()
                    .mapToInt(PackageEditRequest::getVersion)
                    .max()
                    .orElse(0) + 1;

            PackageEditRequest newRequest = new PackageEditRequest(newVersion, false, modifications);
            modifyRequests.add(newRequest);
        }

        subPackageRepository.save(subPackage);
    }

    /**
     * Adds reel media content to an existing package.
     * <p>
     * This method allows influencers to add reel-type media content to their packages.
     * It validates the user's authentication, retrieves the target package, converts
     * the provided media DTOs to the appropriate format, and updates both the main
     * package entity and the search index for discoverability.
     *
     * @param PackageId The unique identifier of the SubPackage to which reels will be added.
     *                  Must be a valid MongoDB ObjectId string representing an existing SubPackage.
     * @param mediasDto A list of CreateMediaDto objects containing the media information to be added.
     *                  Each media item will be automatically set to MediaType.reel regardless of
     *                  the original mediaType value. The list should not be null or empty.
     * @throws CustomException with status code 404 if:
     *                         - The SubPackage with the given PackageId is not found
     *                         - The parent Package referenced by the SubPackage is not found
     * @implNote This method performs the following operations:
     * 1. Sets all media items to MediaType.reel
     * 2. Converts DTOs to MediaWrapper format
     * 3. Validates user authentication via session data
     * 4. Retrieves and validates the target SubPackage and its parent Package
     * 5. Converts media DTOs to MediaWrapper entities
     * 6. Updates the package's media collection (Note: current implementation overwrites existing media)
     * 7. Persists changes to both package and search repositories
     * @see CreateMediaDto
     * @see MediaType#reel
     * @see MediaWrapper
     * @see SubPackage
     * @see Package
     * @see searchPackage
     */
    @Override
    public void AddReels(String PackageId, List<CreateMediaDto> mediasDto) {
        // Set all media items to a reel type regardless of their original mediaType
        mediasDto.forEach(media -> media.setMediaType(MediaType.reel));

        // Build aggregation query to retrieve current user information with normalized fields
        Aggregation query = queryNormalazieService.getUserFields(Criteria.where("_id").is(new ObjectId(userSessionData.getId())));

        // Execute the aggregation query to get the authenticated user
        User user = mongoTemplate.aggregate(query, "user", User.class).getMappedResults().stream().findAny().orElse(null);

        // Proceed only if user authentication is successful
        if (user != null) {
            // Retrieve the target SubPackage by ID, throw exception if not found
            SubPackage sPackage = subPackageRepository.findById(PackageId).orElseThrow(() -> new CustomException(404, " package not found"));

            // Retrieve the parent Package entity, throw exception if not found
            Package _package = packageRepository.findById(sPackage.get_package().getId()).orElseThrow(() -> new CustomException(404, " package not found"));

            // Establish the relationship between SubPackage and Package
            sPackage.set_package(_package);

            // Convert the media wrapper DTOs to MediaWrapper entities
            List<MediaWrapper> newMediaWrappers = generateMediaWithWrapper(mediasDto);

            // Update media ownership and post relationships
            List<Media> mediasToSave = new ArrayList<>();
            for (MediaWrapper mediaWrapper : newMediaWrappers) {
                mediaWrapper.getMedia().setUser(user);
                mediaWrapper.getMedia().set_package(_package);
                mediasToSave.add(mediaWrapper.getMedia());
            }

            mediaRepository.saveAll(mediasToSave);

            // Get the existing media list from the package
            List<MediaWrapper> old = _package.getMedias();

            // Add new media wrappers to the existing collection
            old.addAll(newMediaWrappers);

            _package.setMedias(old);

            // Persist the updated package to the database
            packageRepository.save(_package);

            // Convert the SubPackage to a search-optimized format for indexing
            searchPackage searchPackage = mapper.convertPackageToSearchPackage(sPackage);

            // Update the search index to ensure the package remains discoverable
            searchPackageRepository.save(searchPackage);

            // TODO: Update userId for package places - implementation pending
        }
    }

    @Override
    public void reRequest(CreateUpdatePackageDto obj) {

        var generatedMedia = generateMediaWithWrapper(obj.getMedias());
        obj.setMedias(new ArrayList<>());

        // Map DTOs to entity objects
        Package _package = mapper.convertToEntity(obj);
        SubPackage _sub = mapper.convertToSubEntity(obj);

        _package.setMedias(generatedMedia);

        Aggregation query = queryNormalazieService.getUserFields(Criteria.where("_id").is(new ObjectId(userSessionData.getId())));
        User user = mongoTemplate.aggregate(query, "user", User.class).getMappedResults().stream().findAny().orElse(null);

        if (user != null) {
            _sub.setEnd(obj.getEnd());
            if (obj.getId() != null) {
                SubPackage sPackage = subPackageRepository.findById(obj.getId()).get();
                _package.setId(sPackage.get_package().getId());
                _sub.setPackageStatus(PackageStatus.returned);

                _package.setInfulancer(user);
                _sub.setCreationDate(LocalDateTime.now());
                _sub.set_package(_package);

                if (_sub.getPackageType().equals(PackageType.TravelWithMe))
                    packageRepository.save(_package);

                subPackageRepository.save(_sub);
                searchPackage searchPackage = mapper.convertPackageToSearchPackage(_sub);
                searchPackageRepository.save(searchPackage);
            }

            List<Media> mediasToSave = new ArrayList<>();
            // Update userId for package medias
            _package.getMedias().stream()
                    .map(MediaWrapper::getMedia)
                    .filter(Objects::nonNull)
                    .forEach(media -> {
                        media.setUser(user);
                        media.set_package(_package);
                        mediasToSave.add(media);
                    });


            mediaRepository.saveAll(mediasToSave);
        }
    }

    @SuppressWarnings("LoggingSimilarMessage")
    @Override
    public FullPackageDto getFullPackageById(String id) {
        if (id == null)
            return null;

        long packageFetchStart = System.currentTimeMillis(); //start of the function
        Aggregation query = queryNormalazieService.getFullPackage(Criteria.where("_id").is(new ObjectId(id)));
        SubPackage subPackage = mongoTemplate.aggregate(query, "subPackage", SubPackage.class).getMappedResults().stream().findAny().orElse(null);
        long packageFetchEnd = System.currentTimeMillis(); //start of the function
        long packageFetch = packageFetchEnd - packageFetchStart; //start of the function
        long getSubscribesStart = System.currentTimeMillis();
        FullPackageDto fullPackageDto = mapper.convertToFullDto(subPackage);
        assert subPackage != null;
        List<Subscribe> Subscribes = getPackageSubscribes(subPackage.get_package().getId());
        long getSubscribesEnd = System.currentTimeMillis();
        long getSubscribes = getSubscribesEnd - getSubscribesStart;
        boolean isSubscribed = !Subscribes.isEmpty();
        long OtherexecutedStart = System.currentTimeMillis();
        Criteria criteria = new Criteria();
        criteria.and("postStatus").is(PackageStatus.posted);
        criteria.and("Package._id").is(new ObjectId(fullPackageDto.get_Package().getId()));
        Sort sort = Sort.by(Sort.Direction.DESC, "PostedDate");
        Aggregation aggregation = queryNormalazieService.getPostFields(criteria, null, sort);
        List<Post> aggregateResult = mongoTemplate.aggregate(aggregation, "post", Post.class).getMappedResults();
        List<PostDto> Posts = aggregateResult.stream().map(mapper::convertToDto).collect(Collectors.toList());
        List<ActivityGroupDto> activitiesByDay = new ArrayList<>();
        long numOfDaysBetween = ChronoUnit.DAYS.between(fullPackageDto.getStart(), fullPackageDto.getEnd()) + 1;
        LocalDateTime dayDate = fullPackageDto.getStart();
        long iterator = numOfDaysBetween;
        List<itineraryWithLocationDto> itineraries = new ArrayList<>();
        List<HotelWithLocationDTO> hotels = new ArrayList<>();
        List<TransportationWithLocationDTO> transportation = new ArrayList<>();
        extractItineraryFromPackagePlaces(subPackage.getPackagePlaces(), itineraries, hotels, transportation);
        fullPackageDto.setHotelsWithLocations(hotels);
        fullPackageDto.setTransportationWithLocations(transportation);
        if (!itineraries.isEmpty())
            while (iterator > 0) {
                LocalDateTime constDate = dayDate;
                ActivityGroupDto activityByDay = new ActivityGroupDto();
                activityByDay.setDay(Integer.toString(dayDate.getDayOfMonth()));
                activityByDay.setMonth(dayDate.getMonth().name());
                List<itineraryWithLocationDto> dayActivity = itineraries
                        .stream()
                        .filter(activity -> spectrumCommon.activityCompare(activity, constDate))
                        .collect(Collectors.toList());

                activityByDay.setActivities(dayActivity);
                iterator--;
                dayDate = dayDate.plusDays(1);
                activitiesByDay.add(activityByDay);
            }

        fullPackageDto.setActivities(activitiesByDay);
        fullPackageDto.setPosts(Posts);

        fullPackageDto.setSubscribed(isSubscribed);
        // Extract hotels
        long OtherexecutedEnd = System.currentTimeMillis();
        long Otherexecuted = OtherexecutedEnd - OtherexecutedStart;
        logger.error("Other  executed in {} ms", Otherexecuted);
        return fullPackageDto;
    }

    @Override
    public searchResultDto getPostByPackageId(String id, int page, int limit) {
        if (page < 0) {
            limit = ALL_LIMIT;
            page = 0;
        }
        try {
            SubPackage sub = subPackageRepository.findById(id).orElseThrow(() -> new CustomException(404, "Not Found"));

            Criteria criteria = Criteria.where("Package._id").is(new ObjectId(sub.get_package().getId()));
            // Create pageable with descending sort on "updated" field.
            final Pageable pageable = PageRequest.of(page, limit, Sort.Direction.DESC, "updated");
            // Retrieve the search aggregation pipeline based on query and classFilter.
            Aggregation aggregation = queryNormalazieService.getSearch(null, SearchEnum.post, pageable, criteria);
            // Execute the aggregation query against the "search" collection and map the results to the searchResultDto class.
            var result = searchmongoTemplate.aggregate(aggregation, "search", searchResultDto.class);
            // Return a unique mapped result from the aggregation query.
            return result.getUniqueMappedResult();
        } catch (Exception e) {
            logger.error(e.getMessage());
            throw new RuntimeException(e);
        }

    }

    @Override
    public searchResultDto getStoriesByPackageId(String id, int page, int limit) {
        if (page < 0) {
            limit = ALL_LIMIT;
            page = 0;
        }
        SubPackage sub = subPackageRepository.findById(id).orElseThrow(() -> new CustomException(404, "Not Found"));
        Criteria criteria = Criteria.where("Package._id").is(new ObjectId(sub.get_package().getId()));
        // Create pageable with descending sort on "updated" field.
        final Pageable pageable = PageRequest.of(page, limit, Sort.Direction.DESC, "updated");
        // Retrieve the search aggregation pipeline based on query and classFilter.
        Aggregation aggregation = queryNormalazieService.getSearch(null, SearchEnum.story, pageable, criteria);
        // Execute the aggregation query against the "search" collection and map the results to the searchResultDto class.
        var result = searchmongoTemplate.aggregate(aggregation, "search", searchResultDto.class);
        // Return a unique mapped result from the aggregation query.
        return result.getUniqueMappedResult();
    }

    public PackageWithItenrary getPackageId(String id) {
        Criteria criteria = Criteria.where("_id").is(new ObjectId(id));
        try {
            // Create pageable with descending sort on "updated" field.
            final Pageable pageable = PageRequest.of(0, 1, Sort.Direction.DESC, "updated");
            // Retrieve the search aggregation pipeline based on query and classFilter.
            logger.info("Search Start");
            List<AggregationOperation> operations = new ArrayList<>();
            operations.add(match(criteria));
            Aggregation aggregation = newAggregation(operations);
            // Execute the aggregation query against the "search" collection and map the results to the searchResultDto class.
            var result = searchmongoTemplate.aggregate(aggregation, "search", searchPackage.class);
            logger.info("getting results");

            var baseEntity = result.getUniqueMappedResult();
            if (baseEntity != null) {
                searchPackage firstResult = baseEntity;
                List<ActivityGroupDto> activitiesByDay = new ArrayList<>();
                long numOfDaysBetween = ChronoUnit.DAYS.between(firstResult.getStart(), firstResult.getEnd()) + 1;
                LocalDateTime dayDate = firstResult.getStart();
                long iterator = numOfDaysBetween;
                List<itineraryWithLocationDto> itineraries = new ArrayList<>();
                List<HotelWithLocationDTO> hotels = new ArrayList<>();
                List<TransportationWithLocationDTO> transportation = new ArrayList<>();
                extractItineraryFromPackagePlaces(firstResult.getPackagePlaces(), itineraries, hotels, transportation);
                PackageWithItenrary item = mapper.convertToDto(firstResult);
                item.setHotelsWithLocations(hotels);
                item.setTransportationWithLocations(transportation);
                if (!itineraries.isEmpty())
                    while (iterator > 0) {
                        LocalDateTime constDate = dayDate;
                        ActivityGroupDto activityByDay = new ActivityGroupDto();
                        activityByDay.setDay(Integer.toString(dayDate.getDayOfMonth()));
                        activityByDay.setMonth(dayDate.getMonth().name());
                        List<itineraryWithLocationDto> dayActivity = itineraries
                                .stream()
                                .filter(activity -> spectrumCommon.activityCompare(activity, constDate))
                                .collect(Collectors.toList());

                        activityByDay.setActivities(dayActivity);
                        iterator--;
                        dayDate = dayDate.plusDays(1);
                        activitiesByDay.add(activityByDay);
                    }

                item.setActivities(activitiesByDay);
                return item;
            }
            logger.info("no results");

        } catch (Exception e) {
            logger.error(e.getMessage());
            throw new RuntimeException(e);
        }
        return null; // Return null if no result is found
    }

    public PackageHotels getPackageHotels(String id) {
        Criteria criteria = Criteria.where("_id").is(new ObjectId(id));
        try {
            // Create pageable with descending sort on "updated" field.
            final Pageable pageable = PageRequest.of(0, 1, Sort.Direction.DESC, "updated");
            // Retrieve the search aggregation pipeline based on query and classFilter.
            logger.info("Search Start");
            List<AggregationOperation> operations = new ArrayList<>();
            operations.add(match(criteria));
            Aggregation aggregation = newAggregation(operations);
            // Execute the aggregation query against the "search" collection and map the results to the searchResultDto class.
            var result = searchmongoTemplate.aggregate(aggregation, "search", searchPackage.class);
            logger.info("getting results");

            var baseEntity = result.getUniqueMappedResult();
            if (baseEntity != null) {
                searchPackage firstResult = baseEntity;
                List<itineraryWithLocationDto> itineraries = new ArrayList<>();
                List<HotelWithLocationDTO> hotels = new ArrayList<>();
                List<TransportationWithLocationDTO> transportation = new ArrayList<>();
                extractItineraryFromPackagePlaces(firstResult.getPackagePlaces(), itineraries, hotels, transportation);
                PackageHotels item = new PackageHotels();
                item.setHotelsWithLocations(hotels);
                return item;
            }
            logger.info("no results");

        } catch (Exception e) {
            logger.error(e.getMessage());
            throw new RuntimeException(e);
        }
        return null; // Return null if no result is found
    }


    @Override
    public FullPackageDto getActivePackage(PackageType packageType) {
        String id = userSessionData.getId();
        Query query = new Query();
        query.addCriteria(Criteria.where("id.user.id").is(id));

        // First get all subscriptions for the user
        List<Subscribe> subscriptions = mongoTemplate.find(query, Subscribe.class);

        // Extract all package IDs from subscriptions
        List<String> packageIds = subscriptions.stream()
                .filter(subscription -> subscription.getId().get_package() != null)
                .map(subscription -> subscription.getId().get_package().getId())
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (packageIds.isEmpty()) {
            return null;
        }

        // Fetch all SubPackages in one batch request
        Query packageQuery = new Query(Criteria.where("_id").in(packageIds));
        List<SubPackage> subPackages = mongoTemplate.find(packageQuery, SubPackage.class);

        // Create a map for quick lookup: packageId -> SubPackage
        Map<String, SubPackage> packageMap = subPackages.stream()
                .collect(Collectors.toMap(SubPackage::getId, Function.identity()));

        // Find the active package using the map
        SubPackage ActivePackage = subscriptions.stream()
                .filter(subscription -> subscription.getId().get_package() != null)
                .map(subscription -> packageMap.get(subscription.getId().get_package().getId()))
                .filter(subPackage -> subPackage != null && subPackage.getState().equals(State.OnGoing))
                .filter(subPackage -> subPackage.getPackageType() == packageType)
                .findFirst()
                .orElse(null);

        if (ActivePackage == null)
            return null;

        FullPackageDto fullPackageDto = mapper.convertToFullDto(ActivePackage);
        Criteria criteria = new Criteria();
        criteria.and("postStatus").is(PackageStatus.posted);
        criteria.and("Package._id").is(new ObjectId(fullPackageDto.get_Package().getId()));
        Sort sort = Sort.by(Sort.Direction.DESC, "PostedDate");
        Aggregation aggregation = queryNormalazieService.getPostFields(criteria, null, sort);
        List<Post> aggregateResult = mongoTemplate.aggregate(aggregation, "post", Post.class).getMappedResults();

        List<PostDto> Posts = aggregateResult
                .stream()
                .map(mapper::convertToDto)
                .collect(Collectors.toList());

        fullPackageDto.setPosts(Posts);
        List<ActivityGroupDto> activitiesByDay = new ArrayList<>();
        long numOfDaysBetween = ChronoUnit.DAYS.between(fullPackageDto.getStart(), fullPackageDto.getEnd()) + 1;
        LocalDateTime dayDate = fullPackageDto.getStart();
        long iterator = numOfDaysBetween;


        List<itineraryWithLocationDto> itineraries = new ArrayList<>();
        List<HotelWithLocationDTO> hotels = new ArrayList<>();
        List<TransportationWithLocationDTO> transportation = new ArrayList<>();
        extractItineraryFromPackagePlaces(ActivePackage.getPackagePlaces(), itineraries, hotels, transportation);

        if (!itineraries.isEmpty())
            while (iterator > 0) {
                LocalDateTime constDate = dayDate;
                ActivityGroupDto activityByDay = new ActivityGroupDto();
                activityByDay.setDay(Integer.toString(dayDate.getDayOfMonth()));
                activityByDay.setMonth(dayDate.getMonth().name());
                List<itineraryWithLocationDto> dayActivity = itineraries
                        .stream()
                        .filter(activity -> spectrumCommon.activityCompare(activity, constDate))
                        .collect(Collectors.toList());

                activityByDay.setActivities(dayActivity);
                iterator--;
                dayDate = dayDate.plusDays(1);
                activitiesByDay.add(activityByDay);
            }

        fullPackageDto.setActivities(activitiesByDay);


        return fullPackageDto;
    }

    private Criteria createSearchSpecification(Map<String, Object> obj) {
        Criteria query = new Criteria();
        List<Criteria> list = new ArrayList<>();
        for (Map.Entry<String, Object> entry : obj.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (Objects.equals(key, "maxPrice")) {
                list.add(Criteria.where("totalPrice").lte(value));
            } else if (Objects.equals(key, "minPrice")) {
                list.add(Criteria.where("totalPrice").gte(value));
            } else if (Objects.equals(key, "place")) {
                Criteria placeQuery = new Criteria();
                placeQuery.orOperator(
                        Criteria.where("_package.Place").regex(value.toString(), "i"),
                        Criteria.where("_package.ToPlace").regex(value.toString(), "i")
                );
            } else if (Objects.equals(key, "date")) {
                ArrayList valueArray = new ArrayList<>((Collection) value);
                List<Criteria> sublist = new ArrayList<>();
                Criteria date = new Criteria();
                for (Object item : valueArray) {
                    Map<String, Object> valueMap = (Map<String, Object>) item;
                    var startItem = valueMap.get("start");
                    var endItem = valueMap.get("end");
                    LocalDateTime startDateTime = LocalDateTime.parse(startItem.toString(), DateTimeFormatter.ISO_DATE_TIME);
                    LocalDateTime startOfDay = startDateTime.with(LocalTime.MIN);
                    Criteria sub = new Criteria();
                    LocalDateTime endDateTime = LocalDateTime.parse(endItem.toString(), DateTimeFormatter.ISO_DATE_TIME);
                    LocalDateTime endOfDay = endDateTime.with(LocalTime.MAX);
                    sub.orOperator(Criteria.where("start").gte(startOfDay), Criteria.where("end").lte(endOfDay));
                    sublist.add(sub);
                }
                date.orOperator(sublist);
                list.add(date);
                // Get the start of the day (
            } else if (isDateTimeString(value.toString())) {
                LocalDateTime startDateTime = LocalDateTime.parse(value.toString(), DateTimeFormatter.ISO_DATE_TIME);

                // Get the start of the day (00:00:00)
                LocalDateTime startOfDay = startDateTime.with(LocalTime.MIN);

                // Get the end of the day (23:59:59)
                LocalDateTime endOfDay = startDateTime.with(LocalTime.MAX);

                list.add(Criteria.where(key).gte(startOfDay).lte(endOfDay));

            } else if (!value.toString().isEmpty() && value.getClass().equals(Boolean.class)) {
                list.add(Criteria.where(key).is(value));
            } else if (isNumeric(value.toString())) {
                double numericValue = Double.parseDouble(value.toString());
                list.add(Criteria.where(key).is(numericValue));
            } else if (!value.toString().isEmpty()) {
                list.add(Criteria.where(key).regex(value.toString(), "i"));
            }
        }

        if (!list.isEmpty())
            query.andOperator(list);

        return query;
    }

    private boolean isDateTimeString(String value) {
        try {
            LocalDateTime.parse(value, DateTimeFormatter.ISO_DATE_TIME);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    private boolean isNumeric(String value) {
        try {
            Double.parseDouble(value);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }


    @Override
    public void AddPackageToFavourite(String PackageId) {
        String id = userSessionData.getId();
        Aggregation query = queryNormalazieService.getUserFields(Criteria.where("_id").is(new ObjectId(id)));
        User user = mongoTemplate.aggregate(query, "user", User.class).getMappedResults().stream().findAny().orElse(null);

        if (user != null) {
            List<Like> likes = user.getLike();

            if (likes == null)
                likes = new ArrayList<>();

            Like like = likes
                    .stream()
                    .filter(customer -> customer.getPackageref() != null && PackageId.equals(customer.getPackageref().getId()))
                    .findAny()
                    .orElse(null);

            if (like == null) {
                likes.add(new Like("", "", new SubPackage(PackageId)));
                user.setLike(likes);
                userRepository.save(user);
            }
        }
    }

    @Override
    public void RemovePackageFavourite(String PackageId) {
        String id = userSessionData.getId();
        Aggregation query = queryNormalazieService.getUserFields(Criteria.where("_id").is(new ObjectId(id)));
        User user = mongoTemplate.aggregate(query, "user", User.class).getMappedResults().stream().findAny().orElse(null);

        if (user != null) {
            List<Like> likes = user.getLike();

            if (likes == null)
                likes = new ArrayList<>();

            Like like = likes
                    .stream()
                    .filter(customer -> customer.getPackageref() != null && PackageId.equals(customer.getPackageref().getId()))
                    .findAny()
                    .orElse(null);

            if (like != null)
                likes.remove(like);

            user.setLike(likes);
            userRepository.save(user);
        }
    }

    @Override
    public void ManagePackageNotification(String PackageId, Boolean value) {
        String id = userSessionData.getId();
        SubPackage sub = mongoTemplate.findById(PackageId, SubPackage.class);

        if (sub == null)
            return;

        UserPackageKey SubscribeKey = new UserPackageKey(new User(id), sub.get_package());
        PackageNotification notification = new PackageNotification();
        notification.setId(SubscribeKey);
        notification.setState(value);
        packageNotificationRepository.save(notification);
    }

    @Override
    public void ManagePackageReaction(String PackageId, Boolean value) {
        String id = userSessionData.getId();
        SubPackage sub = mongoTemplate.findById(PackageId, SubPackage.class);

        if (sub == null)
            return;

        UserPackageKey SubscribeKey = new UserPackageKey(new User(id), sub.get_package());
        PackageReaction notification = new PackageReaction();
        notification.setId(SubscribeKey);
        notification.setLoveIt(value);
        packageReactionRepository.save(notification);
    }

    @Override
    public void SubscribePackage(String PackageId) {
        String id = userSessionData.getId();
        SubscribeKey SubscribeKey = new SubscribeKey(new User(id), new SubPackage(PackageId));
        Subscribe subscribe = mongoTemplate.findById(SubscribeKey, Subscribe.class);

        if (subscribe == null) {
            subscribe = new Subscribe();
            subscribe.setId(SubscribeKey);
            subscribe.setStatus(SubscribeStatus.Influencer);
            subscribeRepository.save(subscribe);
        }
    }

    @Override
    public String SubscribeTravelWithMePackage(SubscribeTravelWithMeDto subscribeDto) throws JsonProcessingException {
        String id = userSessionData.getId();
        if (id.isEmpty()) {
            throw new CustomException(500, "Something went wrong Invalid Token");

        }
        //getting user to check if exist
        Aggregation query = queryNormalazieService.getUsersList(Criteria.where("_id").is(new ObjectId(id)));
        User user = mongoTemplate.aggregate(query, "user", User.class).getMappedResults().stream().findAny().orElse(null);
        if (user == null) {
            throw new CustomException(500, "Something went wrong User Not Found");
        }

        SubscribeKey SubscribeKey = new SubscribeKey(new User(id), new SubPackage(subscribeDto.getPackageId()));

        // 4. Cancel any existing subscription for this user+package
        String userId = SubscribeKey.getUser().getId();
        String packageId = SubscribeKey.get_package().getId();

        List<Subscribe> existingSubs =
                subscribeRepository.findById_User_IdAndId__package_Id(userId, packageId);

        if (!existingSubs.isEmpty()) {
            for (Subscribe oldSub : existingSubs) {
                if (oldSub.getStatus() != SubscribeStatus.paid) {
                    oldSub.setStatus(SubscribeStatus.cancelled);
                    subscribeRepository.save(oldSub);
                }
            }
        }

        SubPackage subPackage = subPackageRepository.findById(subscribeDto.getPackageId()).orElseThrow(() -> new CustomException(500, "package not found"));
        int totalCount = subPackage.getSubscribeCount();
        int available = totalCount - subPackage.getCapacity();
        if (subscribeDto.getTravelers() != null && !subscribeDto.getTravelers().isEmpty()) {

            Subscribe subscribe;
            SubscribeKey.set_package(subPackage);
            if (!subscribeDto.getTravelers().isEmpty())
                subPackage.setSubscribeCount(totalCount + subscribeDto.getTravelers().size());
            subscribe = mapper.convertToSubscribe(subscribeDto);
            subscribe.setId(SubscribeKey);
            subscribe.setStatus(SubscribeStatus.submitted);
            subscribe.setDocuments(subscribeDto.getDocuments());
            subscribe.setDepartureFlight(subscribeDto.getDepartureFlight());
            subscribe.setDestinationFlight(subscribeDto.getDestinationFlight());
            subscribe.setTotal(subscribeDto.getTotal());

            subscribeRepository.save(subscribe);

//            // Send notification to user to inform him with status change
//            notificationService.sendAndStoreNotification(
//                        subPackage.getId(),
//                        NotificationType.SubmittedSubscribe,
//                        user,
//                        List.of(subPackage, subscribe), subPackage.get_package()
//                            .getMedias()
//                            .stream()
//                            .filter(image -> image.isMainImage())
//                            .findFirst()
//                            .get().getUrl(),
//                    null, NotificationEntityType.PACKAGE,
//                    subPackage.getSlug(),
//                    subPackage.get_package().getInfulancer().getUsername(), null);
//
//
//            subPackageRepository.save(subPackage);
////            ReactionSearch reactionSearch = commonUtils.getReaction(id, subscribeDto.getPackageId(), EntityType.Subscribe);
//            if (reactionSearch == null) {
//                commonUtils.CreateReaction(subscribeDto.getPackageId(), id, null, EntityName.Package, EntityType.Subscribe, null);
//            }
//
//            // Send the notification to the subscribed users, and favourite package users
//            PackageNotificationSetting packageNotificationSetting = packageNotificationSettingRepository
//                    .findByPackageId(subPackage.getId());
//
//            if (packageNotificationSetting != null) {
//                var subscribes = this.getPackageSubscribes(subPackage.getId());
//                var subscribesCount = subscribes.size();
//                int reservedSeatPercentage = subscribesCount * 100 / subPackage.getCapacity();
//                if (reservedSeatPercentage >= packageNotificationSetting.getPackageCapacityFullPercentage()) {
//                    // Send notification to subscribed users
//                    for (Subscribe subscribe1 : subscribes) {
//                        notificationService.sendAndStoreNotification(
//                                subPackage.getId(),
//                                NotificationType.PackageCapacitySubscribedUsers,
//                                subscribe1.getId().getUser(),
//                                List.of(subPackage, subscribe1), subPackage.get_package()
//                                        .getMedias()
//                                        .stream()
//                                        .filter(image -> image.isMainImage())
//                                        .findFirst()
//                                        .get().getUrl(),
//                                null,
//                                NotificationEntityType.PACKAGE,
//                                subPackage.getSlug(),
//                                subPackage.get_package().getInfulancer().getUsername(),
//                                null);
//                    }
//
//                    // Send notification to favourite package users
//                    // Find all reactions that are "Favourite" type for the package of the post
//                    List<ReactionSearch> favouriteReactions = reactionSearchRepository.findByEntityNameAndEntityTypeAndEntityId(
//                            EntityName.Package,
//                            EntityType.Favourite,
//                            subPackage.getId()
//                    );
//
//                    // Extract the user's ids
//                    List<String> usersIds = favouriteReactions
//                            .stream()
//                            .map(ReactionSearch::getUserId)
//                            .toList();
//
//                    // fetch users from the database
//                    List<User> users = userRepository.findAllById(usersIds);
//
//                    // Send notification to each user
//                    for (User favouritePackageUser : users) {
//                        notificationService.sendAndStoreNotification(
//                                subPackage.getId(),
//                                NotificationType.PackageCapacityFavouriteUsers,
//                                favouritePackageUser,
//                                List.of(subPackage), subPackage.get_package()
//                                        .getMedias()
//                                        .stream()
//                                        .filter(image -> image.isMainImage())
//                                        .findFirst()
//                                        .get().getUrl(),
//                                null,
//                                NotificationEntityType.PACKAGE,
//                                subPackage.getSlug(),
//                                subPackage.get_package().getInfulancer().getUsername(),
//                                null);
//                    }
//                }
//            }
//
//            // add the user to the group conversation of the subscribed package
//            List<GroupConversation> groupConversation = groupConversationRepository.findByPackageId(subscribeDto.getPackageId());
//            if (groupConversation.isEmpty()) {
//                groupConversation.add(chatMessageService.createGroupConversation(subscribeDto.getPackageId()));
//            }
//
//            chatMessageService.addUserToGroupConversation(groupConversation.getFirst().getId(), user.getId());
//
//            if (available >= 0) {
//                return "Your subscription request was submitted successfully! Our admin will contact you soon with further details";
//
//            } else {
//                return "Your subscription request is currently pending approval as we have a limited number of seats available. Thanks for your patience—we’ll get back to you as soon as possible!";
//
//
//            }
//
//
        } else if (subscribeDto.getTravelers() == null || subscribeDto.getTravelers().isEmpty()) {
            throw new CustomException(500, "travelers not defined");
        } else {
            throw new CustomException(500, "an error occurred");

        }
        return "Your subscription request is currently pending approval as we have a limited number of seats available. Thanks for your patience—we’ll get back to you as soon as possible!";

    }

    private boolean hasExpireDate(LocalDateTime subscribeDate) {
        return subscribeDate != null && subscribeDate.isAfter(LocalDateTime.now());
    }

    @Override
    public PriceAndCheckoutUrl getSubscribePrice(Subscribe subscribe, ArrayList<ConfirmPirceResponse> ConfirmPriceResponses) {
        SubPackage subPackage = subscribe.getId().get_package();
        FlightPrices flights = spectrumCommon.getFlightsPrices(subscribe, subPackage.getFlights(), ConfirmPriceResponses);

        logger.error("flights price is {}", flights.getDifferenceFlight());
        logger.error("Total flights price is {}", flights.getTotalFlight());

        BigDecimal hotels = spectrumCommon.CalculateHotelPrices(subscribe);
        logger.error("hotels price is {}", hotels);
        BigDecimal totalPrice = subPackage.getTotalPrice().multiply(BigDecimal.valueOf(subscribe.getTravelers().size()).add(flights.getDifferenceFlight()).add(hotels));

        logger.error("Travelers count {}", subscribe.getTravelers().size());
        logger.error("package total price is {}", subPackage.getTotalPrice());
        logger.error("total price price is {}", totalPrice);

        PriceAndCheckoutUrl PriceAndCheckoutUrl = new PriceAndCheckoutUrl();
        Price price = new Price();
        price.setTotalPrice(totalPrice);
        PriceAndCheckoutUrl.setPrice(price);
        subscribe.setTotal(totalPrice);
        subscribeRepository.save(subscribe);
        String url = paymentService.createCheckoutSession(price.getTotalPrice().longValue(), subscribe.getId(), true);
        PriceAndCheckoutUrl.setCheckOutUrl(url);
        return PriceAndCheckoutUrl;
    }

    @Override
    public void UnSubscribePackage(String PackageId) {
        String id = userSessionData.getId();
        SubscribeKey SubscribeKey = new SubscribeKey(new User(id), new SubPackage(PackageId));
        Subscribe subscribe = mongoTemplate.findById(SubscribeKey, Subscribe.class);

        if (subscribe != null)
            subscribeRepository.delete(subscribe);
    }

    /**
     * Retrieves all packages associated with a user, including both direct subscriptions and traveler subscriptions.
     *
     * @param id The user ID to fetch packages for
     * @return A combined list of {@link PackageDto} containing both direct subscriptions and traveler subscriptions,
     * with duplicates removed. Returns null if the operation fails.
     * The returned packages exclude any canceled packages.
     * @throws IllegalArgumentException if id is null
     * @throws MongoException           if there's an error executing the MongoDB aggregation
     */
    @Override
    public List<PackageDto> GetUserSubscribedPackages(String id) {
        return getPackageDto(id);
    }

    @Override
    public PackageDtoResultDto getSubscribePackageWithPagination(String id, String query, PackageType type, SubscribeStatus status, int page, int limit) {
        String userId = (id == null || id.isEmpty()) ? userSessionData.getId() : id;
        Criteria criteria = Criteria.where("_id.user.$id").is(new ObjectId(userId));
        if (status != null)
            criteria.and("status").is(status);
        final Pageable pageable = PageRequest.of(page, limit, Sort.Direction.DESC, "updated");
        Aggregation aggregation = queryNormalazieService.getSubscribePackageWithPagination(criteria, type, query, pageable);
        var result = mongoTemplate.aggregate(aggregation, "subscribe", PackageDtoResultDto.class).getUniqueMappedResult();
        return result;
    }

    @Override
    public boolean isSlugUnique(String slug) {
        return !subPackageRepository.existsBySlugIgnoreCase(slug);
    }

    /**
     * Retrieves all packages associated with a user, including both direct subscriptions and traveler subscriptions.
     *
     * @param id The user ID to fetch packages for
     * @return A combined list of {@link PackageDto} containing both direct subscriptions and traveler subscriptions,
     * with duplicates removed. Returns null if the operation fails.
     * The returned packages exclude any canceled packages.
     * @throws IllegalArgumentException if id is null
     * @throws MongoException           if there's an error executing the MongoDB aggregation
     */
    @Nullable
    private List<PackageDto> getPackageDto(String id) {
        // Get direct subscriptions using aggregation pipeline
        Aggregation aggregation = queryNormalazieService.getSubscribePackage(Criteria.where("_id.user._id").is(new ObjectId(id)));
        List<Subscribe> subscribes = mongoTemplate.aggregate(aggregation, "subscribe", Subscribe.class)
                .getMappedResults();

        // Filter out canceled packages and convert to PackageDto
        List<PackageDto> packages = subscribes.stream()
                .filter(z -> z.getId().get_package() != null && !z.getId().get_package().getState().equals(State.Canceled))
                .map(Subscribe::getId)
                .map(SubscribeKey::get_package)
                .map(mapper::convertToDto)
                .collect(Collectors.toList());

        // Get traveler subscriptions using separate aggregation
        Aggregation travellerAggregation = queryNormalazieService.getTravellerSubscribePackage(Criteria.where("user._id").is(new ObjectId(id)));
        List<TravelerSubscribe> travelerSubscribes = mongoTemplate.aggregate(travellerAggregation, "travelerSubscribe", TravelerSubscribe.class)
                .getMappedResults();

        // Filter out canceled packages and convert traveler subscriptions to PackageDto
        List<PackageDto> travelerPackages = travelerSubscribes.stream()
                .filter(z -> z.getSubscribe() != null &&
                        z.getSubscribe().getId().get_package() != null &&
                        !z.getSubscribe().getId().get_package().getState().equals(State.Canceled))
                .map(TravelerSubscribe::getSubscribe)
                .map(Subscribe::getId)
                .map(SubscribeKey::get_package)
                .map(mapper::convertToDto)
                .collect(Collectors.toList());

        // Combine both lists and remove any duplicates
        packages = removeDuplicatesById(packages, travelerPackages);
        return packages;
    }


    private List<Subscribe> getPackageSubscribes(String packageId) {
        String id = userSessionData.getId();
        logger.error("the package id is {}", packageId);
        Aggregation aggregation = queryNormalazieService.getSubscribePackage(
                Criteria.where("_id.user._id").is(new ObjectId(id))
                        .and("_id._package._package._id").is(new ObjectId(packageId))
        );
        /// get traveler subscribe;
        return mongoTemplate.aggregate(aggregation, "subscribe", Subscribe.class)
                .getMappedResults();
    }


    private List<PackageDto> removeDuplicatesById(List<PackageDto> list1, List<PackageDto> list2) {
        Map<String, PackageDto> packageMap = new HashMap<>();

        // Add instances from list1 to the map
        for (PackageDto packageInstance : list1) {
            if (packageInstance.get_package() != null && packageInstance.get_package().getId() != null)
                packageMap.put(packageInstance.get_package().getId(), packageInstance);
        }

        // Add instances from list2 to the map (overwriting duplicates)
        for (PackageDto packageInstance : list2) {
            if (packageInstance.get_package() != null && packageInstance.get_package().getId() != null)
                packageMap.put(packageInstance.get_package().getId(), packageInstance);
        }

        // Return the values (unique instances) from the map as a list
        return new ArrayList<>(packageMap.values());
    }

    @Override
    public List<searchPackage> getSubscribedPackages() {
        // Get all subscribed packages for the current user without search filtering
        PageDto<searchPackage> result = getSubscribedPackages(userSessionData.getId(), 0, Integer.MAX_VALUE, null, null);
        return result.getItems();
    }

    /**
     * Retrieves a paginated list of packages that a user has subscribed to, combining both direct
     * and traveler subscriptions. The results can be optionally filtered using a search query.
     *
     * @param userId          The ID of the user whose subscriptions should be retrieved. If null or empty,
     *                        the current user's ID is used
     * @param page            Zero-based page index for pagination
     * @param size            Number of items per page
     * @param query           Optional search term to filter packages. If provided, performs a text search
     *                        across package attributes
     * @param subscribeStatus Filter criterion for subscription status
     * @return PageDto containing filtered searchPackage objects and total count
     * @throws IllegalArgumentException if page or size are negative
     * @throws MongoException           if there's an error executing the MongoDB operations
     */
    @Override
    public PageDto<searchPackage> getSubscribedPackages(String userId, int page, int size, String query,
                                                        SubscribeStatus subscribeStatus) {
        // Default to current user if no ID provided
        if (userId == null || userId.isEmpty()) {
            userId = userSessionData.getId();
        }

        // Configure pagination with descending sort by updateDate
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "updateDate"));

        // Retrieve direct subscriptions with pagination
        Page<Subscribe> subscribePage = subscribeRepository.findSubscribedPackagesByUserId(new ObjectId(userId), subscribeStatus, pageable);
        Set<searchPackage> packages = subscribePage.getContent().stream()
                .map(Subscribe::getId)
                .map(SubscribeKey::get_package)
                .filter(Objects::nonNull)
                .map(mapper::convertPackageToSearchPackage)
                .collect(Collectors.toSet());

        // Retrieve and process traveler subscriptions
        Long travelerSubscribeCount = travelerSubscribeRepository.countTravelerSubscribesByUserId(new ObjectId(userId),
                subscribeStatus);
        List<TravelerSubscribe> travelerList = travelerSubscribeRepository.findTravelerSubscribesByUserId(
                userId,
                subscribeStatus,
                pageable.getOffset(),
                pageable.getPageSize()
        );

        // Add traveler subscriptions to the result set
        packages.addAll(travelerList.stream()
                .filter(ts -> ts.getSubscribe() != null && ts.getSubscribe().getId().get_package() != null)
                .map(TravelerSubscribe::getSubscribe)
                .map(Subscribe::getId)
                .map(SubscribeKey::get_package)
                .map(mapper::convertPackageToSearchPackage)
                .collect(Collectors.toSet()));

        PageDto<searchPackage> pageDto = new PageDto<>();

        // Apply text search if query parameter is provided
        if (query != null && !query.trim().isEmpty()) {
            List<String> packageIds = packages.stream()
                    .map(searchPackage::getId)
                    .collect(Collectors.toList());

            // Return empty result if no packages found
            if (packageIds.isEmpty()) {
                pageDto.setItems(new ArrayList<>());
                pageDto.setTotalNoOfItems(0);
                return pageDto;
            }

            // Apply pagination parameters
            int skip = pageable.getPageNumber() * pageable.getPageSize();
            int limit = pageable.getPageSize();

            // Perform text search on filtered package IDs
            List<searchPackage> searchResults = searchPackageRepository.searchInPackageIds(query, packageIds, skip, limit);
            Long countResult = searchPackageRepository.countSearchInPackageIds(query, packageIds);

            pageDto.setItems(searchResults);
            pageDto.setTotalNoOfItems(countResult != null ? countResult : 0L);

            return pageDto;
        }

        // Return all packages if no search query
        pageDto.setItems(packages.stream().toList());
        pageDto.setTotalNoOfItems(subscribePage.getTotalElements() +
                (travelerSubscribeCount != null ? travelerSubscribeCount : 0L));

        return pageDto;
    }

    @Override
    public List<PackageDto> GetUserOwnedPackages(String id) {
        final Criteria searchQuery = new Criteria();
        if (userSessionData.getId() != null) {
            searchQuery.orOperator(
                    Criteria.where("packageStatus").is(PackageStatus.posted),
                    Criteria.where("_package.infulancer._id").is(new ObjectId(id))
                            .and("_package.infulancer._id").is(userSessionData.getId())
            );
        } else {
            searchQuery.and("packageStatus").is(PackageStatus.posted);
        }

        Aggregation aggregation = queryNormalazieService.getPackage(searchQuery, null);

        return mongoTemplate.aggregate(aggregation, "subPackage", SubPackage.class).getMappedResults()
                .stream()
                .map(mapper::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public PageDto<InfluencerPackageDto> GetRequestedPackages(int page, int size, String searchQuery) {
        if (page < 0) {
            size = ALL_LIMIT;
            page = 0;
        }

        PageDto<InfluencerPackageDto> pageDto = new PageDto<>();
        final Pageable pageable = PageRequest.of(page, size, Sort.Direction.DESC, "updated");

        final Criteria criteria = new Criteria();
        criteria.orOperator(
                Criteria.where("packageStatus").is(PackageStatus.accepted),
                Criteria.where("packageStatus").is(PackageStatus.draft)
        );
        criteria.and("_package.infulancer._id").is(new ObjectId(userSessionData.getId()));

        Aggregation aggregation = queryNormalazieService.getPackage(criteria, pageable, searchQuery);
        AggregationResults<PackageResultDto> results = mongoTemplate.aggregate(aggregation, "subPackage", PackageResultDto.class);

        List<InfluencerPackageDto> aggregationResults = Objects.requireNonNull(results.getUniqueMappedResult()).getFilteredResults()
                .stream()
                .map(mapper::convertToInfluenceDto)
                .collect(Collectors.toList());

        pageDto.setItems(aggregationResults);
        long totalCount = results.getUniqueMappedResult() != null ? results.getUniqueMappedResult().getTotalCount() : 0;
        pageDto.setTotalNoOfItems(totalCount);
        pageDto.setPageNumber(page);
        pageDto.setItemsPerPage(size);

        return pageDto;
    }

    @Override
    public FlightAndHotels getRoomAndFlights(GetRoomForHotelPackageDto getRoomHotels) {
        FlightAndHotels flightAndHotels = new FlightAndHotels();
        Optional<SubPackage> subPackage = subPackageRepository.findById(getRoomHotels.getPackageId());

        if (subPackage.isEmpty())
            return null;

        ArrayList<HotelAvilabiltyResponse> hotels = new ArrayList<>();

        try {
            hotels = spectrumCommon.getRoomsForHotels(getRoomHotels, subPackage.get());
        } catch (Exception ignored) {
        }

        flightAndHotels.setHotels(hotels);
        ArrayList<FlightsDirections> flights = new ArrayList<>();

        try {
            flights = spectrumCommon.getFlights(getRoomHotels, subPackage.get());
        } catch (Exception ignored) {
        }

        flightAndHotels.setFlights(flights);
        flightAndHotels.setCreationTime(LocalDateTime.now());
        // clone package
        SubPackage sub = Clone(getRoomHotels);

        //Subscribing Done
        assert sub != null;
        flightAndHotels.setPackageId(sub.getId());
        cashService.storeData("flightAndHotels-" + flightAndHotels.getId(), flightAndHotels, 604800000L);
        return flightAndHotels;
    }

    @Override
    public FollowMePackage getFollowMe(String packageId) {
        String id = userSessionData.getId();

        FollowMePackage followMePackage = new FollowMePackage();
        FullPackageDto fullPackageDto = getFullPackageById(packageId);
        followMePackage.set_package(fullPackageDto);
        FlightAndHotels flightAndHotels = cashService.getAllFlightAndHotels()
                .stream()
                .filter(item -> Objects.equals(item.getPackageId(), packageId))
                .findFirst()
                .get();

        followMePackage.setFlightAndHotels(flightAndHotels);
        followMePackage.setFlights(fullPackageDto.getFlights());
        SubscribeKey SubscribeKey = new SubscribeKey(new User(id), new SubPackage(packageId));
        Subscribe subscribe = subscribeRepository.findById(SubscribeKey).orElse(null);
        if (subscribe != null) {
            followMePackage.setRooms(subscribe.getRoomReservations());
            followMePackage.setTotalPrice(subscribe.getTotal());
        }
        return followMePackage;
    }

    @Override
    public PackageDto saveSelectedData(BookPackage savedBookPackage) throws JsonProcessingException {
        String id = userSessionData.getId();

        FlightAndHotels flightAndHotels = cashService.getFlightAndHotelsById(savedBookPackage.getEntityId());
        if (flightAndHotels == null)
            return null;

        //getting Subscribe info
        SubscribeKey SubscribeKey = new SubscribeKey(new User(id), new SubPackage(flightAndHotels.getPackageId()));
        Subscribe subscribe = subscribeRepository.findById(SubscribeKey).get();
        Optional<SubPackage> subPackageOptional = subPackageRepository.findById(flightAndHotels.getPackageId());
        if (subPackageOptional.isEmpty())
            return null;
        SubPackage subPackage = subPackageOptional.get();
        List<Flight> flights = new ArrayList<>();
        List<BookingFlightResponse> bookingFlightResponses = new ArrayList<>();
        List<BookFlightRequst> BookFlightRequests = confirmFlightPrices(flightAndHotels, savedBookPackage.getConfirmPrices());
        for (BookFlightRequst FlightReq : BookFlightRequests) {
            FlightsDirections direction = flightAndHotels.getFlights().
                    stream()
                    .filter(z -> z.getId().equals(FlightReq.getFlightId()))
                    .findFirst()
                    .get();

            var flight = direction.getFlights()
                    .stream()
                    .filter(z -> z.getSearchIdentifier().equals(FlightReq.getSearchIdentifier()))
                    .findFirst()
                    .get();

            BookingFlightResponse bookingFlightResponse = new BookingFlightResponse();
            bookingFlightResponse.setFlightId(FlightReq.getFlightId());
            bookingFlightResponse.setFlightReferenceId(flight.getSearchIdentifier());
            bookingFlightResponses.add(bookingFlightResponse);
            flights.add(flight);
        }
        subscribe.setFlights(bookingFlightResponses);
        subPackage.setFlights(flights);
        // storing Hotels note not room
        List<Hotel> hotels = new ArrayList<>();
        for (RoomReservation room : savedBookPackage.getRoomReservations()) {
            var s = flightAndHotels.getHotels().stream().filter(z -> z.getPropertyReferenceId().equals(room.getHotelId())).findFirst().orElse(null);
            Hotel hotel = mapper.convertToHotel(s);
            hotels.add(hotel);
        }
        subPackage.setHotels(hotels);
        subscribe.setRoomReservations(savedBookPackage.getRoomReservations());
        subscribeRepository.save(subscribe);
        subPackageRepository.save(subPackage);

        return mapper.convertToDto(subPackage);
    }

    private SubPackage Clone(GetRoomForHotelPackageDto getRoomHotels) {
        Optional<SubPackage> opPackage = subPackageRepository.findById(getRoomHotels.getPackageId());

        if (opPackage.isEmpty() || opPackage.get().get_package() == null)
            return null;

        Package _package = opPackage.get().get_package();
        new SubPackage();
        SubPackage _sub;
        _sub = opPackage.get();
        _sub.setPackageType(PackageType.FollowMe);
        //getting days difference
        long numOfDaysBetweenStartAndEnd = ChronoUnit.DAYS.between(_sub.getStart().toLocalDate(), getRoomHotels.getCheckIn()) + 1;

//        if (_package.getActivities() != null && !_package.getActivities().isEmpty()) {
//            LocalDateTime startDate = _sub.getStart().plusDays(numOfDaysBetweenStartAndEnd);
//            LocalDateTime endDate = _sub.getEnd().plusDays(numOfDaysBetweenStartAndEnd);
//            _sub.setStart(startDate);
//            _sub.setEnd(endDate);
//            long numOfDaysBetween = ChronoUnit.DAYS.between(_package.getStart(), _sub.getStart()) + 1;
//            List<ActivityClones> activities = new ArrayList<>();
//
//            for (Activity activity : _package.getActivities()) {
//                ActivityClones act = new ActivityClones();
//                act.setActivity(activity);
//                act.setStart(activity.getStart().plusDays(numOfDaysBetween));
//                act.setEnd(activity.getEnd().plusDays(numOfDaysBetween));
//                activityCloneRepository.save(act);
//                activities.add(act);
//            }
//
//            _sub.setState(State.NotStarted);
//            _sub.setPackageStatus(PackageStatus.posted);
//            _sub.setActivities(activities);
//            _sub.set_package(_package);
//        }

        _sub.setCreationDate(DateFormatter.formatLocalDateTime(LocalDateTime.now()));
        _sub.setUpdateDate(DateFormatter.formatLocalDateTime(LocalDateTime.now()));
        _sub.setId(null);
        subPackageRepository.save(_sub);
        return _sub;
    }

    @Override
    public List<BookFlightRequst> confirmFlightPrices(FlightAndHotels flightAndHotels, ArrayList<ConfirmBookFlightRequstDto> confirmPrices) throws JsonProcessingException {

        List<BookFlightRequst> bookFlightRequests = new ArrayList<>();
        ArrayList<com.hb.crm.core.beans.Flight.Currency> currencies = new ArrayList<>();
        com.hb.crm.core.beans.Flight.Currency currency = new com.hb.crm.core.beans.Flight.Currency();
        currency.setCode("USD");
        currency.setValue("US DOLLAR");
        currencies.add(currency);
        for (ConfirmBookFlightRequstDto confirmBook : confirmPrices) {
            FlightsDirections flightsDirections = flightAndHotels.getFlights().stream().filter(z -> z.getId().equals(confirmBook.getFlightId())).findAny().orElse(null);

            assert flightsDirections != null;
            Flight flight = flightsDirections.getFlights()
                    .stream().filter(z -> z.getSearchIdentifier().equals(confirmBook.getSearchIdentifier()))
                    .findAny()
                    .orElse(null);

            confermPriceRequst confermPriceRequst = new confermPriceRequst();
            ConfirmPriceData confirmPriceData = new ConfirmPriceData();
            confirmPriceData.setCurrencies(currencies);

            List<Flight> flightsOffers = new ArrayList<>();
            flightsOffers.add(flight);
            // adding flight data

            confirmPriceData.setFlightOffers(flightsOffers);
            confirmPriceData.setDestination(flightsDirections.getDestination());
            confirmPriceData.setOrigin(flightsDirections.getOrigin());
            confirmPriceData.setUpgradeCabin(confirmBook.getUpgradeCabin());
            confirmPriceData.setLocations(flightsDirections.getLocations());
            confirmPriceData.setAircrafts(flightsDirections.getAircrafts());
            confirmPriceData.setCarriers(flightsDirections.getCarriers());
            confermPriceRequst.setUpgradeCabin(confirmBook.getUpgradeCabin());
            confermPriceRequst.setData(confirmPriceData);
            // execute confirm Api
            ConfirmPirceResponse confirmResponse = spectrum.requestConfirmPrice(confermPriceRequst);
            BookFlightRequst BookFlightRequst = new BookFlightRequst();
            BookFlightRequst.setFlightId(flightsDirections.getId());
            BookFlightRequst.setSearchIdentifier(confirmBook.getSearchIdentifier());
            BookFlightRequst.setConfirmPriceReferenceId(confirmResponse.getData().getConfirmPriceReferenceId());
            BookFlightRequst.setConfirmPriceReferenceExpiry(confirmResponse.getData().getConfirmPriceReferenceExpiry());
            bookFlightRequests.add(BookFlightRequst);
        }
        return bookFlightRequests;
    }

    @Override
    public BookFlightRequst confirmFlightPrice(ConfrimBookFlight confirmPrices) throws JsonProcessingException {
        FlightAndHotels flightAndHotels = cashService.getFlightAndHotelsById(confirmPrices.getSearchId());
        ArrayList<com.hb.crm.core.beans.Flight.Currency> currencies = new ArrayList<>();
        com.hb.crm.core.beans.Flight.Currency currency = new com.hb.crm.core.beans.Flight.Currency();
        currency.setCode("USD");
        currency.setValue("US DOLLAR");
        currencies.add(currency);

        if (flightAndHotels == null)
            return null;

        ConfirmBookFlightRequstDto confirmBook = confirmPrices.getConfirmPrice();
        ArrayList<FlightsDirections> flightsDirectionsList = flightAndHotels.getFlights();
        FlightsDirections flightsDirections = flightsDirectionsList
                .stream()
                .filter(z -> z.getId().equals(confirmBook.getFlightId()))
                .findAny()
                .orElse(null);

        int directionIndex = flightsDirectionsList.indexOf(flightsDirections);
        assert flightsDirections != null;
        List<Flight> flights = flightsDirections.getFlights();
        Flight flight = flights
                .stream()
                .filter(z -> z.getSearchIdentifier().equals(confirmBook.getSearchIdentifier()))
                .findAny()
                .orElse(null);

        int flightIndex = flights.indexOf(flight);
        confermPriceRequst confermPriceRequst = new confermPriceRequst();
        ConfirmPriceData confirmPriceData = new ConfirmPriceData();
        confirmPriceData.setCurrencies(currencies);
        List<Flight> flightsOffers = new ArrayList<>();
        flightsOffers.add(flight);
        // adding flight data
        confirmPriceData.setFlightOffers(flightsOffers);
        confirmPriceData.setDestination(flightsDirections.getDestination());
        confirmPriceData.setOrigin(flightsDirections.getOrigin());
        confirmPriceData.setUpgradeCabin(confirmBook.getUpgradeCabin());
        confirmPriceData.setLocations(flightsDirections.getLocations());
        confermPriceRequst.setUpgradeCabin(confirmBook.getUpgradeCabin());
        confermPriceRequst.setData(confirmPriceData);
        // execute confirm Api
        ConfirmPirceResponse confirmResponse = spectrum.requestConfirmPrice(confermPriceRequst);
        BookFlightRequst BookFlightRequst = new BookFlightRequst();

        if (confirmBook.getUpgradeCabin().equals("BUSINESS")) {
            var price = confirmResponse.getData().getData().getFlightOffers().getFirst().getPrice();
            assert flight != null;
            flight.setUpgradeCabin("BUSINESS");
            flight.setBusinesPrice(price);
            flights.add(flightIndex, flight);
            flightsDirections.setFlights(flights);
            flightsDirectionsList.add(directionIndex, flightsDirections);
            flightAndHotels.setFlights(flightsDirectionsList);

            // store flightAndHotels in cache
            cashService.storeFlightAndHotels(flightAndHotels, 604800000L);

            BookFlightRequst.setTotalPrice(price.getTotal());
        } else if (confirmBook.getUpgradeCabin().equals("FIRSTCLASS")) {
            var price = confirmResponse.getData().getData().getFlightOffers().getFirst().getPrice();
            assert flight != null;
            flight.setUpgradeCabin("FIRSTCLASS");
            flight.setFirstClassPrice(price);
            flights.add(flightIndex, flight);
            flightsDirections.setFlights(flights);
            flightsDirectionsList.add(directionIndex, flightsDirections);
            flightAndHotels.setFlights(flightsDirectionsList);

            // store flightAndHotels in cache
            cashService.storeData("flightAndHotels-" + flightAndHotels.getId(), flightAndHotels, 604800000L);

            BookFlightRequst.setTotalPrice(price.getTotal());
        } else {
            assert flight != null;
            flight.setUpgradeCabin(null);
        }

        BookFlightRequst.setFlightId(flightsDirections.getId());
        BookFlightRequst.setSearchIdentifier(confirmBook.getSearchIdentifier());
        BookFlightRequst.setConfirmPriceReferenceId(confirmResponse.getData().getConfirmPriceReferenceId());
        return BookFlightRequst;
    }


    @Override
    public PriceAndCheckoutUrl getPrice(BookPackage bookPackage) throws Exception {
        String id = userSessionData.getId();
        FlightAndHotels flightAndHotels = cashService.getFlightAndHotelsById(bookPackage.getEntityId());

        if (flightAndHotels == null)
            throw new CustomException(500, "Entity Not Found");

        Subscribe subscribe = new Subscribe();
        subscribe.setTravelers(bookPackage.getTravelers());
        SubscribeKey SubscribeKey = new SubscribeKey(new User(id), new SubPackage(flightAndHotels.getPackageId()));
        subscribe.setId(SubscribeKey);
        SubPackage subPackage = subPackageRepository.findById(SubscribeKey.get_package().getId()).orElse(null);

        if (subPackage == null)
            throw new CustomException(500, "Package Not Found");

        BigDecimal flightPrice;
        List<ConfirmPirceResponse> confirmPriceResponses = new ArrayList<>();
        List<String> Confirms = new ArrayList<>();

        if (bookPackage.getConfirmPriceTravelRequst() != null) {
            List<confermPriceRequst> conformPriceRequests = spectrumCommon.getConfirmPriceTravelRequests(bookPackage.getConfirmPriceTravelRequst());

            for (confermPriceRequst confermpricerequst : conformPriceRequests) {
                ConfirmPirceResponse confirmPirceResponse = spectrum.requestConfirmPrice(confermpricerequst);
                Confirms.add(confirmPirceResponse.getData().getConfirmPriceReferenceId());
                confirmPirceResponse.setFlightStep(confermpricerequst.getFlightStep());
                confirmPriceResponses.add(confirmPirceResponse);
            }
        }

        List<BookFlightRequst> BookFlightRequests = confirmFlightPrices(flightAndHotels, bookPackage.getConfirmPrices());
        flightPrice = spectrumCommon.getFlightsPrice(BookFlightRequests, confirmPriceResponses, flightAndHotels, bookPackage.getTravelers());
        BigDecimal hotelPrices = BigDecimal.ZERO;  // Initialize hotelsPrice to zero

        for (RoomReservation room : bookPackage.getRoomReservations()) {
            var hotel = flightAndHotels.getHotels().stream()
                    .filter(z -> z.getPropertyReferenceId().equals(room.getHotelId()))
                    .findFirst()
                    .orElse(null);

            for (ReservedRoom reserved : room.getRooms()) {
                assert hotel != null;
                var objectRoom = hotel.getRooms()
                        .stream()
                        .filter(z -> z.getId().equals(reserved.getRoomId()))
                        .findFirst()
                        .orElse(null);

                assert objectRoom != null;
                var option = objectRoom.getOptions()
                        .stream()
                        .filter(z -> z.getId().equals(reserved.getOptionId()))
                        .findFirst()
                        .orElse(null);
                assert option != null;
                hotelPrices = hotelPrices.add(option.getTotalPrice().getGrossPrice());
            }
        }

        subscribe.setRoomReservations(bookPackage.getRoomReservations());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
        List<LocalDateTime> expireDates = new ArrayList<>();

        for (BookFlightRequst bookFlight : BookFlightRequests) {
            Confirms.add(bookFlight.getConfirmPriceReferenceId());
            LocalDateTime localDateTime = LocalDateTime.parse(bookFlight.getConfirmPriceReferenceExpiry().substring(0, 19), formatter);
            expireDates.add(localDateTime);
        }

        if ((long) expireDates.size() > 0) {
            LocalDateTime minDateTime = Collections.min(expireDates, LocalDateTime::compareTo);
            subscribe.setExpireDate(minDateTime);
        } else {
            String delay = settingService.getByName("OfflinePackageExpierDate").getValue();
            LocalDateTime expireDate = LocalDateTime.now().plusDays(Long.parseLong(delay));
            subscribe.setExpireDate(expireDate);
        }

        subscribe.setTravelers(bookPackage.getTravelers());
        subscribe.setConfirmPriceRefraceId(Confirms);
        // subscribe.setEmail(bookPackage.getEmail());

        logger.error("Total Flight Price{}", flightPrice);
        logger.error("Package Discount is {}", subPackage.getTotalPrice().multiply(BigDecimal.valueOf(subPackage.getFollowMeDiscount() / 100)));
        logger.error("Total Package before Discount is {}", subPackage.getTotalPrice());
        logger.error("Total Package After Discount is {}", subPackage.getTotalPrice().subtract(subPackage.getTotalPrice().multiply(BigDecimal.valueOf(subPackage.getFollowMeDiscount() / 100))));
        logger.error("Hotel Price is {}", hotelPrices);

        subscribe.setTotal(hotelPrices.add(flightPrice.add(subPackage.getTotalPrice().subtract(subPackage.getTotalPrice().multiply(BigDecimal.valueOf(subPackage.getFollowMeDiscount() / 100))))));
        subscribeRepository.save(subscribe);
        Price price = new Price();
        price.setTotalPrice(subscribe.getTotal());
        PriceAndCheckoutUrl PriceAndCheckoutUrl = new PriceAndCheckoutUrl();
        PriceAndCheckoutUrl.setPrice(price);
        String url = paymentService.createCheckoutSession(price.getTotalPrice().longValue(), subscribe.getId(), false);
        PriceAndCheckoutUrl.setCheckOutUrl(url);

        cashService.deleteData(flightAndHotels.getId());

        return PriceAndCheckoutUrl;
    }


    /**
     * Retrieves all packages marked as favorite by the current user.
     *
     * <p>This method performs the following steps:
     * <ol>
     *   <li>Retrieves the current user's ID from the session</li>
     *   <li>Queries all "Favourite" type reactions for packages associated with the user</li>
     *   <li>Extracts package IDs from the reactions</li>
     *   <li>Fetches the corresponding packages from the repository</li>
     *   <li>Converts the packages to DTOs</li>
     * </ol>
     * </p>
     *
     * @return List<PackageDto> A list of favorite packages converted to DTOs.
     * Returns an empty list if no favorites are found.
     * @see PackageDto
     * @see ReactionSearch
     * @see SubPackage
     */
    @Override
    public List<PackageDto> GetFavouritePackages() {
        return GetFavouritePackages(null);
    }

    @Override
    public List<PackageDto> GetFavouritePackages(String searchTerm) {
        // Get current user's ID from session
        String id = userSessionData.getId();

        // Find all reactions that are "Favourite" type for Packages for this user
        List<ReactionSearch> favouriteReactions = reactionSearchRepository.findByEntityNameAndEntityTypeAndUserId(
                EntityName.Package,
                EntityType.Favourite,
                id
        );

        // Return an empty list if no favorites found
        if (favouriteReactions.isEmpty()) {
            return new ArrayList<>();
        }

        // Extract package IDs from reactions
        List<String> packageIds = favouriteReactions.stream()
                .map(ReactionSearch::getEntityId)
                .filter(Objects::nonNull)
                .toList();

        List<SubPackage> packages;

        // If a search term is provided, use fuzzy search within favorite packages
        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            packages = subPackageRepository.findBySearchTermAndPackageIds(searchTerm.trim(), packageIds);
        } else {
            // Otherwise, fetch all favorite packages
            packages = subPackageRepository.findAllById(packageIds);
        }

        // Convert packages to DTOs and return
        return packages.stream()
                .map(mapper::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public searchResultDto GetFavouritePackagesv2(int page, int size, PackageType packageType, String searchTerm) {
        // Get current user's ID from session
        final Pageable pageable = PageRequest.of(page, size, Sort.Direction.DESC, "updated");

        String id = userSessionData.getId();
        // Find all reactions that are "Favourite" type for Packages for this user
        List<ReactionSearch> favouriteReactions = new ArrayList<>();

        if (packageType == null) {
            favouriteReactions = reactionSearchRepository.findByEntityNameInAndEntityTypeAndUserId(
                    List.of(EntityName.Package, EntityName.FollowPackage),
                    EntityType.Favourite,
                    id
            );
        } else if (packageType == PackageType.FollowMe) {
            favouriteReactions = reactionSearchRepository.findByEntityNameInAndEntityTypeAndUserId(
                    List.of(EntityName.FollowPackage),
                    EntityType.Favourite,
                    id
            );
        } else {
            favouriteReactions = reactionSearchRepository.findByEntityNameInAndEntityTypeAndUserId(
                    List.of(EntityName.Package),
                    EntityType.Favourite,
                    id
            );
        }


        // Return an empty list if no favorites found
        if (favouriteReactions.isEmpty()) {
            return new searchResultDto();
        }

        // Extract package IDs from reactions
        List<ObjectId> packageIds = favouriteReactions.stream()
                .map(ReactionSearch::getEntityId)
                .filter(Objects::nonNull)
                .map(ObjectId::new)
                .toList();

        Criteria criteria = Criteria.where("_id").in(packageIds);

        Aggregation aggregation = queryNormalazieService.getSearch(searchTerm, null, pageable, criteria);
        // Execute the aggregation query against the "search" collection and map the results to the searchResultDto class.
        return searchmongoTemplate.aggregate(aggregation, "search", searchResultDto.class).getUniqueMappedResult();

    }


    /**
     * <h>Retrieves the subscription status for a given package ID and the current user <h>.
     *
     * <p>This method uses the provided package ID and the current user's ID
     * (retrieved from the token) to construct a composite subscription ID. The subscription
     * status is then fetched based on this composite ID. If either the package or subscription
     * is not found, appropriate exceptions are thrown </p>.
     *
     * @param id the unique identifier of the subscription package.
     * @return the status of the subscription associated with the given package ID and the current user.
     * @throws CustomException if the subscription package is not found in the repository
     *                         (with a 404 status code and message "Package not found!"),
     *                         or if the subscription itself is not found
     */
    public SubscribeStatus getSubscriptionStatus(String id) {
        long startTime = System.nanoTime(); // Start timer
        if (id == null)
            return null;

        String userId = userSessionData.getId();

        SubscribeKey SubscribeKey = new SubscribeKey(new User(userId), new SubPackage(id));
        Subscribe subscribe = mongoTemplate.findById(SubscribeKey, Subscribe.class);

        if (subscribe == null)
            return SubscribeStatus.NotSubscribed;
        long endTime = System.nanoTime(); // End timer
        long executionTime = endTime - startTime;
        logger.error("Execution time: " + executionTime / 1_000_000.0 + " ms");
        return subscribe.getStatus();
    }


    @Override
    public PackageDto getPackageBySlug(String slug) {
        var pkg = this.subPackageRepository.findBySlug(slug).orElse(null);
        return mapper.convertToDto(pkg);
    }


    private ActivityCategory getActivityCategoryById(String activityCategoryId) {
        return activityCategoryRepository.findById(activityCategoryId).orElse(null);
    }

    private void extractItineraryFromPackagePlaces(List<PackageCountry> packageCountries, List<itineraryWithLocationDto> result, List<HotelWithLocationDTO> hotels, List<TransportationWithLocationDTO> transportation) {

        if (packageCountries == null) return;
        for (PackageCountry country : packageCountries) {

            String countryName = country.getCountryName(); // Assuming getName() returns country name

            // Process country-level itineraries
            Optional.ofNullable(country.getItinerary())
                    .ifPresent(itineraries -> itineraries.forEach(itinerary -> addItineraryWithLocation(itinerary, country.getHotels(), country.getTransportation(), result, countryName, null, null)));

            // Process cities
            Optional.ofNullable(country.getCities()).ifPresent(cities ->
                    cities.forEach(city -> {

                        String cityName = city.getCityName(); // Assuming getName() returns city name
                        Optional.ofNullable(city.getItinerary())
                                .ifPresent(itineraries -> itineraries.forEach(itinerary -> addItineraryWithLocation(itinerary, city.getHotels(), city.getTransportation(), result, countryName, cityName, null)));


                        // Process areas
                        Optional.ofNullable(city.getAreas()).ifPresent(areas ->
                                areas.forEach(area -> {
                                    String areaName = area.getAreaName(); // Assuming getName() returns area name
                                    Optional.ofNullable(area.getItinerary())
                                            .ifPresent(itineraries -> itineraries.forEach(itinerary -> addItineraryWithLocation(itinerary, area.getHotels(), area.getTransportation(), result, countryName, cityName, areaName)));
                                })
                        );
                    })
            );
        }
    }


    private void addItineraryWithLocation(PackageItinerary itinerary, List<PackageHotel> hotels, List<PackageTransportation> transportations, List<itineraryWithLocationDto> result,
                                          String countryName, String cityName, String areaName) {
        itineraryWithLocationDto dto = new itineraryWithLocationDto();
        //   dto.setPropertyId(itinerary.getPropertyId());
        if (itinerary.getReferenceId() != null) {
            if (itinerary.getReferenceId().startsWith("trans")) {
                String transId = itinerary.getReferenceId();
                PackageTransportation PackageTransportation = transportations.stream().filter(z -> z.getPropertyId().equals(transId)).findFirst().orElse(null);
                if (PackageTransportation != null) {
                    dto.setFromAirport(PackageTransportation.getFromAirport());
                    dto.setToAirport(PackageTransportation.getToAirport());
                }
                dto.setType("Transportation");
            } else if (itinerary.getReferenceId().startsWith("hotel")) {
                String hotelID = itinerary.getReferenceId().replaceAll("(_in|_out)", "");
                PackageHotel PackageHotel = hotels.stream().filter(z -> z.getPropertyId().equals(hotelID)).findFirst().orElse(null);
                if (PackageHotel != null) {
                    dto.setHotel(PackageHotel.getHotel());
                    if (PackageHotel.getHotel() == null) {
                        simpleHotelInfo simpleHotelInfo = new simpleHotelInfo();
                        simpleHotelInfo.setName(PackageHotel.getName());
                    }
                }
                dto.setType("Hotel");

            }
        } else {
            dto.setType("Content");
        }
        dto.setName(itinerary.getName());
        dto.setDetails(itinerary.getDetails());
        dto.setStart(itinerary.getStart());
        dto.setEnd(itinerary.getEnd());
        dto.setReferenceId(itinerary.getReferenceId());
        dto.setActivityContent(itinerary.getActivityContent());
        if (itinerary.getCategory() != null && itinerary.getCategory().getId() != null && !itinerary.getCategory().getId().isEmpty()) {
            ActivityCategory activityCategory = getActivityCategoryById(itinerary.getCategory().getId());
            dto.setCategory(activityCategory);

        }
        dto.setOtherDetail(itinerary.getOtherDetail());

        // Set location details
        dto.setCountryName(countryName);
        dto.setCityName(cityName);
        dto.setAreaName(areaName);

        result.add(dto);
    }

    @Override
    public Page<searchPackage> searchExplore(String search, int page, int size, PackageType type) {
        final Pageable pageable = PageRequest.of(page, size, Sort.Direction.DESC, "updated");

        if (search == null || search.trim().isEmpty()) {
            if (type == null)
                return searchPackageRepository.findAllPackages(pageable);
            else
                return searchPackageRepository.findAllPackagesByType(type, pageable);
        }

        // Find matching cities and countries
        List<City> matchedCities = cityRepository.findByNameRegex(search);
        List<Country> matchedCountries = countryRepository.findByNameRegex(search);

        List<String> cityIds = matchedCities.stream()
                .map(City::getId)
                .collect(Collectors.toList());

        List<String> countryIds = matchedCountries.stream()
                .map(Country::getId)
                .collect(Collectors.toList());

        return searchPackageRepository.explorePackages(search, type, countryIds, cityIds, pageable);
    }


    

    public List<User> findFollowers(String influencerId) {
        // Get followed influencers
        var followedInfluencersReactions = reactionSearchRepository.findByEntityNameAndEntityTypeAndEntityId(
                EntityName.User,
                EntityType.Follow,
                influencerId
        );

        var followedInfluencersIds = followedInfluencersReactions
                .stream()
                .map(ReactionSearch::getUserId)
                .filter(Objects::nonNull)
                .toList();

        return userRepository.findByIdIn(followedInfluencersIds);
    }


    @Override
    public List<SimplePackageDto> getInfluencerPackages(String influencerId, String searchTerm) {
        if (influencerId == null || influencerId.trim().isEmpty()) {
            throw new IllegalArgumentException("Influencer ID is required");
        }

        // If no search term provided, return all packages for the influencer
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            List<SimplePackageDto> SimplePackageDto = packageRepository.findByInfulancerId(influencerId.trim()).stream()
                    .map(item -> modelMapper.map(item, SimplePackageDto.class))
                    .toList();

            return SimplePackageDto;
        }

        return packageRepository.findBySearchTermAndInfluencerId(searchTerm.trim(), influencerId.trim())
                .stream()
                .map(item -> modelMapper.map(item, SimplePackageDto.class))
                .toList();
    }

    public List<MediaWrapper> generateMediaWithWrapper(List<CreateMediaDto> mediasRequest) {
        if (mediasRequest == null)
            return new ArrayList<>();
        List<MediaWrapper> createdMedias = new ArrayList<>();
        for (CreateMediaDto createMediaDto : mediasRequest) {
            Media newMedia = new Media();
            newMedia.setMediaType(createMediaDto.getMediaType());
            newMedia.setSource(createMediaDto.getSource());
            newMedia.setVideoUrl(createMediaDto.getVideoUrl());
            newMedia.setVideoSize(createMediaDto.getVideoSize());
            newMedia.setCreationDate(DateTime.now().toDate());
            newMedia.setLastUpdate(DateTime.now().toDate());
            newMedia.setThumbnailCaptureUrl(createMediaDto.getThumbnailCaptureUrl());
            newMedia.setThumbnailClipUrl(createMediaDto.getThumbnailClipUrl());
            newMedia.setOverlays(createMediaDto.getOverlays());
            newMedia.setTitle(createMediaDto.getTitle());
            newMedia.setLatitude(createMediaDto.getLatitude());
            newMedia.setLongtuid(createMediaDto.getLongtuid());
            newMedia.setVideoDuration(createMediaDto.getVideoDuration());
            newMedia.setVideoDurationMS(createMediaDto.getVideoDurationMS());
            newMedia.setDescription(createMediaDto.getDescription());
            newMedia.setClipStartTimecode(createMediaDto.getClipStartTimecode());
            newMedia.setClipEndTimecode(createMediaDto.getClipEndTimecode());
            newMedia.setStartTime(createMediaDto.getStartTime());
            newMedia.setEndTime(createMediaDto.getEndTime());
            newMedia.setTags(createMediaDto.getTags());


            if(createMediaDto.getTaggedUsers()!=null && !createMediaDto.getTaggedUsers().isEmpty()) {
                List<ObjectId> usersId = createMediaDto.getTaggedUsers().stream().map(RefranceModelDto::getId).map(ObjectId::new).collect(Collectors.toList());
                Aggregation taggedquery = queryNormalizeService.getUsersList(Criteria.where("_id").in(usersId));
                List<User> users = mongoTemplate.aggregate(taggedquery, "user", User.class).getMappedResults();
                newMedia.setTaggedUsers(users);
            }

            if (createMediaDto.getMediaType() == MediaType.image) {
                try {
                    String category = createMediaDto.getSource().split("/")[1];
                    ImageCategory imageCategory = ImageCategory.valueOf(category);
                    newMedia.setImageCategory(imageCategory);
                } catch (Exception ignored) {
                }
            }


            var savedMedia = mediaRepository.save(newMedia);

            MediaWrapper mediaWrapper = new MediaWrapper();
            mediaWrapper.setMedia(savedMedia);
            mediaWrapper.setUrl(newMedia.getMediaType() == MediaType.video ? newMedia.getVideoUrl() : newMedia.getSource());
            mediaWrapper.setType(createMediaDto.getMediaType());
            mediaWrapper.setMainImage(createMediaDto.isMainImage());
            createdMedias.add(mediaWrapper);
        }

        return createdMedias;
    }


    private void sendNotificationForCreatePackage(SubPackage savedPackage) {
        // Create group conversation for the package
        chatMessageService.createGroupConversation(savedPackage.getId());

        // Send notification to users that have the same moods as the package
        var packageMoods = savedPackage.get_package().getMoods();
        List<User> matchedMoodsUsers = userRepository.findByMoodsIn(packageMoods);

        var packageImageMedia = savedPackage.get_package().getMedias() != null
                && !savedPackage.get_package().getMedias().isEmpty() ?
                savedPackage.get_package().getMedias()
                        .stream()
                        .filter(MediaWrapper::isMainImage)
                        .toList()
                        .getFirst()
                : null;

        String packageImageMediaUrl = null;
        if (packageImageMedia != null)
            packageImageMediaUrl = packageImageMedia.getUrl();


        for (User mathcedUser : matchedMoodsUsers) {
            if (mathcedUser.getFcmTokens() != null && !mathcedUser.getFcmTokens().isEmpty()) {
                notificationService.sendAndStoreNotification(
                        savedPackage.getId(),
                        NotificationType.MatchedMoodsNewPackage,
                        mathcedUser,
                        List.of(savedPackage, savedPackage.get_package().getInfulancer()),
                        savedPackage.get_package().getInfulancer().getProfileImage(),
                        packageImageMediaUrl,
                        NotificationEntityType.PACKAGE,
                        savedPackage.get_package().getInfulancer(),
                        savedPackage.getSlug(),
                        savedPackage.get_package().getInfulancer().getUsername(), null);
            }
        }

        // Find the user that follows the influencer of the package and send them notification.
        var packageInfluencerFollowers = findFollowers(savedPackage.get_package().getInfulancer().getId());

        for (User follower : packageInfluencerFollowers) {
            notificationService.sendAndStoreNotification(
                    savedPackage.getId(),
                    NotificationType.FollowedInfluencerNewPackage,
                    follower,
                    List.of(savedPackage, savedPackage.get_package().getInfulancer()),
                    savedPackage.get_package().getInfulancer().getProfileImage(),
                    packageImageMediaUrl,
                    NotificationEntityType.PACKAGE,
                    savedPackage.get_package().getInfulancer(),
                    savedPackage.getSlug(),
                    savedPackage.get_package().getInfulancer().getUsername(), null);
        }

        // Send notification to the influencer
        sendNotificationToInfluencerWithCreatePackage(savedPackage.get_package().getInfulancer(), savedPackage,
                List.of(savedPackage), packageImageMediaUrl);

    }

    private void sendNotificationToInfluencerWithCreatePackage(User influencer, SubPackage _package,
                                                               List<Object> entities,
                                                               String packageImage) {
        notificationService.sendAndStoreNotification(_package.getId(), NotificationType.InfluencerNewPackage,
                influencer, entities, packageImage,
                null,
                NotificationEntityType.PACKAGE,
                influencer,
                _package.getSlug(),
                influencer.getUsername(), null);
    }

}


