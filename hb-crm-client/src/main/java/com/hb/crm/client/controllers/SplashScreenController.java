package com.hb.crm.client.controllers;

import com.hb.crm.client.dto.PageDto;
import com.hb.crm.client.dto.ResultDto;
import com.hb.crm.client.dto.SplashScreenDto;
import com.hb.crm.client.dto.splashScreen.splashFollowPackage;
import com.hb.crm.client.dto.splashScreen.splashTravelPackages;
import com.hb.crm.client.dto.splashScreen.splashUserInfoDto;
import com.hb.crm.client.dto.splashScreen.splashUserMoods;
import com.hb.crm.client.services.interfaces.MoodService;
import com.hb.crm.client.services.interfaces.SearchService;
import com.hb.crm.client.services.interfaces.UserService;
import com.hb.crm.core.Enums.SearchEnum;
import org.modelmapper.ModelMapper;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "/v1/SplashScreen" ,  produces = {MediaType.APPLICATION_JSON_VALUE})
public class SplashScreenController {

    private final UserService userService;
    private final SearchService searchService;
    private final MoodService moodService;

    public SplashScreenController(UserService userService, SearchService searchService, MoodService moodService) {
        this.userService = userService;
        this.searchService = searchService;
        this.moodService = moodService;
    }

    @GetMapping("getTopRated")

    @ResponseBody
    public ResultDto<splashUserInfoDto>  getTopRated(@RequestParam int page, @RequestParam int size) {
        ModelMapper modelMapper = new ModelMapper();
        var  topRateUsersPage=  userService.topRatedv2(new HashMap<>(), page, size);
        ResultDto<splashUserInfoDto> resultDto = new ResultDto<>();
        ArrayList<splashUserInfoDto> topRateUsers = topRateUsersPage.getFilteredResults()
                .stream()
                .map(z -> modelMapper.map(z, splashUserInfoDto.class))
                .collect(Collectors.toCollection(ArrayList::new));
        resultDto.setFilteredResults(topRateUsers);
        resultDto.setTotalCount(topRateUsersPage.getTotalCount());
        return resultDto;
    }
    @GetMapping("getRecommenderInfluencers")

    @ResponseBody
    public PageDto<splashUserInfoDto> getRecommenderInfluencers(@RequestParam int page, @RequestParam int size) {
        ModelMapper modelMapper = new ModelMapper();
        var  userRecommended=  userService.recommended(new HashMap<>(), page, size);
        PageDto<splashUserInfoDto> resultDto = new PageDto<>();
        resultDto.setItems(userRecommended.getItems().stream().map(z -> {
            splashUserInfoDto dto = modelMapper.map(z, splashUserInfoDto.class);
            // Copy live stream fields
            dto.setHasLiveStream(z.isHasLiveStream());
            dto.setLiveStreamId(z.getLiveStreamId());
            dto.setLiveStreamPlaybackUrl(z.getLiveStreamPlaybackUrl());
            return dto;
        }).collect(Collectors.toList()));
        resultDto.setTotalNoOfItems(userRecommended.getTotalNoOfItems());
        return resultDto;
    }
    @GetMapping("getFollowPackage")

    @ResponseBody
    public ResultDto<splashTravelPackages>  getFollowPackage(@RequestParam int page, @RequestParam int size,@RequestParam(required = false) String query) {
        ModelMapper modelMapper = new ModelMapper();
        var  FollowPackages=  searchService.search(query, SearchEnum.followPackage, page, size);
        ResultDto<splashTravelPackages> resultDto = new ResultDto<>();
        ArrayList<splashTravelPackages> followPackages = FollowPackages.getFilteredResults()
                .stream()
                .map(z -> modelMapper.map(z, splashTravelPackages.class))
                .collect(Collectors.toCollection(ArrayList::new));
        resultDto.setFilteredResults(followPackages);
        resultDto.setTotalCount(FollowPackages.getTotalCount());
        return resultDto;
    }

    @GetMapping("getTravelPackage")

    @ResponseBody
    public ResultDto<splashTravelPackages>  getTravelPackage(@RequestParam int page, @RequestParam int size ,@RequestParam(required = false) String query) {
        ModelMapper modelMapper = new ModelMapper();
        var  TravelPackages=  searchService.search(query, SearchEnum.Package, page, size);
        ResultDto<splashTravelPackages> resultDto = new ResultDto<>();
        ArrayList<splashTravelPackages> travelPackages = TravelPackages.getFilteredResults()
                .stream()
                .map(z -> modelMapper.map(z, splashTravelPackages.class))
                .collect(Collectors.toCollection(ArrayList::new));
        resultDto.setFilteredResults(travelPackages);
        resultDto.setTotalCount(TravelPackages.getTotalCount());
        return resultDto;
    }


    @GetMapping("GetUserMoods")
    @ResponseBody
    public List<splashUserMoods> GetUserMoods() {
        ModelMapper modelMapper = new ModelMapper();
        return moodService.GetUserMood().stream().map(z->modelMapper.map(z,splashUserMoods.class)).toList();
    }
    @GetMapping("getSplashScreen")
    @ResponseBody
    public SplashScreenDto getSplashScreen( ) {
        int page=0;
        int size=20;
        ModelMapper modelMapper = new ModelMapper();
        List<splashUserMoods> userMood= moodService.GetUserMood().stream().map(z->modelMapper.map(z,splashUserMoods.class)).toList();
        SplashScreenDto splashScreenDto = new SplashScreenDto();
        splashScreenDto.setRecommendedInfluencers(userService.recommended(new HashMap<>(), page, size).getItems().stream().map(z -> {
            splashUserInfoDto dto = modelMapper.map(z, splashUserInfoDto.class);
            // Copy live stream fields
            dto.setHasLiveStream(z.isHasLiveStream());
            dto.setLiveStreamId(z.getLiveStreamId());
            dto.setLiveStreamPlaybackUrl(z.getLiveStreamPlaybackUrl());
            return dto;
        }).toList());
        var searchUsers=  userService.topRatedv2(new HashMap<>(), page, size).getFilteredResults().stream().map(z -> modelMapper.map(z, splashUserInfoDto.class)).toList();
        splashScreenDto.setTopInfluencers(searchUsers);
        splashScreenDto.setFollowPackage(searchService.search(null, SearchEnum.followPackage, page, size).getFilteredResults().stream().map(z->modelMapper.map(z, splashFollowPackage.class)).toList());
        splashScreenDto.setTravelPackage(searchService.search(null, SearchEnum.Package, page, size).getFilteredResults().stream().map(z->modelMapper.map(z, splashTravelPackages.class)).toList());
        splashScreenDto.setUserStories(userService.getUserStoriesSplash(null,page, size).getItems());
        splashScreenDto.setUserMood(userMood);
         return splashScreenDto;
    }
}
