package com.hb.crm.client.services;

import org.springframework.beans.factory.annotation.Value;
import software.amazon.awssdk.services.medialive.MediaLiveClient;
import software.amazon.awssdk.services.medialive.model.*;

import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.UUID;

public class MediaLiveSetupServiceImpl {

    @Value("${cloud.aws.bucketName}")
    private String bucketName;

    private final MediaLiveClient mediaLiveClient;

    // Inject MediaLiveClient using constructor injection
    public MediaLiveSetupServiceImpl(MediaLiveClient mediaLiveClient) {
        this.mediaLiveClient = mediaLiveClient;
    }

    // Create an Input Security HighLight to restrict access to a specific IP
    
    // Create the RTMP Input
    public CreateInputResponse createRTMPInput(String inputName) {

        CreateInputRequest inputRequest = CreateInputRequest.builder()
                .name(inputName)
                .type(InputType.RTMP_PUSH)
                .build();
        return mediaLiveClient.createInput(inputRequest);
    }

    // Create MediaLive Channel
    public CreateChannelResponse createMediaLiveChannel(String inputId) {
        // Generate a unique destination reference ID
        String uniqueDestinationRefId = generateUniqueId();  // Use your GUID + date generator

        // Define the HLS group settings, referencing the unique output destination by its ID
        HlsGroupSettings hlsGroupSettings = HlsGroupSettings.builder()
                .destination(OutputLocationRef.builder()
                        .destinationRefId(uniqueDestinationRefId) // Unique reference ID
                        .build())
                .build();

        // Define the output group settings for HLS
        OutputGroupSettings outputGroupSettings = OutputGroupSettings.builder()
                .hlsGroupSettings(hlsGroupSettings)
                .build();

        // Define the output group
        OutputGroup outputGroup = OutputGroup.builder()
                .name("HLS HighLight")
                .outputGroupSettings(outputGroupSettings)
                .build();

        // Define the input attachment for the channel
        InputAttachment inputAttachment = InputAttachment.builder()
                .inputId(inputId) // Attach the RTMP input
                .build();

        // Define the actual output destination (e.g., S3 bucket) using the unique ID in the path
        String uniqueS3Url = "s3://"+bucketName + "/live/" + uniqueDestinationRefId + "/";  // Unique path in S3 bucket

        OutputDestination outputDestination = OutputDestination.builder()
                .id(uniqueDestinationRefId) // Must match the destinationRefId used earlier
                .settings(Collections.singletonList(
                        OutputDestinationSettings.builder()
                                .url(uniqueS3Url) // S3 bucket URL with unique path
                                .build()))
                .build();

        // Build the CreateChannel request
        CreateChannelRequest createChannelRequest = CreateChannelRequest.builder()
                .name("LiveStream-" + uniqueDestinationRefId) // Unique channel name
                .inputAttachments(Collections.singletonList(inputAttachment))
                .destinations(Collections.singletonList(outputDestination))
                .encoderSettings(EncoderSettings.builder()
                        .outputGroups(Collections.singletonList(outputGroup))
                        .build())
                .build();

        return mediaLiveClient.createChannel(createChannelRequest);
    }
    // Start MediaLive Channel
    public void startChannel(String channelId) {
        StartChannelRequest startChannelRequest = StartChannelRequest.builder()
                .channelId(channelId)
                .build();
        mediaLiveClient.startChannel(startChannelRequest);
    }
    private static String generateUniqueId() {
        // Generate a new GUID
        UUID guid = UUID.randomUUID();

        // Get the current date in the format "yyyyMMdd"
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String date = sdf.format(new Date());

        // Combine the GUID and the date
        String uniqueId = date + "-" + guid.toString();

        return uniqueId;
    }
}
