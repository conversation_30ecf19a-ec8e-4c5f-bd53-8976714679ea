package com.hb.crm.client.services.interfaces;

import com.hb.crm.client.dto.Groups.HighLightDTO;
import com.hb.crm.client.dto.Groups.HighLightListDTO;
import com.hb.crm.client.dto.Groups.HighLightViewDto;
import com.hb.crm.client.dto.PageDto;
import com.hb.crm.client.dto.Results.searchResultDto;

import java.util.List;

public interface HighLightService {
    HighLightViewDto createOrUpdateHighLight(HighLightDTO groupDTO);

    // Add stories to the group (replace existing ones)
    HighLightViewDto addStoryToHighLight(String groupId, List<String> storyIds);
    PageDto<HighLightListDTO> getHighLightsForInfluencer(String username, String influencerId, String keyword, int page, int size);
    // Get stories by group ID with a keyword search
    searchResultDto getPostsByHighLightId(String id, String query, int page, int limit);
    searchResultDto getStoriesByInfulancer( String query, int page, int limit);
}

