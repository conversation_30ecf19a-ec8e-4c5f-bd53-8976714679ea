package com.hb.crm.client.controllers;

import com.hb.crm.client.beans.UserSessionData;
import com.hb.crm.core.Enums.ConversationMessageType;
import com.hb.crm.core.Enums.chat.ChatMessageType;
import com.hb.crm.core.Enums.chat.DeletionType;
import com.hb.crm.core.beans.chat.OneChat.Conversation;
import com.hb.crm.core.dtos.PageDto;
import com.hb.crm.core.dtos.UserDto;
import com.hb.crm.core.dtos.chat.response.*;
import com.hb.crm.core.services.chat.ChatMessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/v1/chat", produces = {MediaType.APPLICATION_JSON_VALUE})
public class ChatController {

    private final UserSessionData userSessionData;
    private final ChatMessageService chatMessageService;


    @PostMapping("/group-conversation/{conversationId}/users/{userId}")
    @Operation(summary = "Add user to group conversation", 
              description = "Adds a user to an existing group conversation and returns conversation details with recent messages")
    public ResponseEntity<UserConversationWithMassagesDto> addUserToGroupConversation(
            @Parameter(description = "HighLight conversation ID", required = true)
            @PathVariable String conversationId,
            
            @Parameter(description = "User ID to add")
            @PathVariable String userId
    ) {
        if(userId == null)
            userId = userSessionData.getId();
        UserConversationWithMassagesDto result = chatMessageService.addUserToGroupConversation(conversationId, userId);
        return ResponseEntity.ok(result);
    }

    @DeleteMapping("/group-conversation/{conversationId}/users/{userId}")
    @Operation(summary = "Remove user from group conversation",
              description = "Removes a user from a group conversation")
    public ResponseEntity<Void> removeUserFromGroupConversation(
            @Parameter(description = "HighLight conversation ID", required = true)
            @PathVariable String conversationId,
            
            @Parameter(description = "User ID to remove")
            @PathVariable String userId
    ) {
        if(userId == null)
            userId = userSessionData.getId();
        chatMessageService.removeUserFromGroupConversation(conversationId, userId);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/conversation/{conversationId}/history")
    @Operation(summary = "Get conversation history", description = "Retrieves paginated chat messages for a one-to-one conversation")
    public Page<ChatMessageResponseDto> getConversationHistory(
            @Parameter(description = "Conversation ID", required = true)
            @PathVariable String conversationId,

            @Parameter(description = "Page number (0-based)", schema = @Schema(type = "integer", minimum = "0"))
            @RequestParam(defaultValue = "0") int page,

            @Parameter(description = "Number of items per page", schema = @Schema(type = "integer", minimum = "1", maximum = "100"))
            @RequestParam(defaultValue = "20") int size,

            @Parameter(description = "Type of the messages")
            @RequestParam(required = false) List<ChatMessageType> types
    ) {
        return chatMessageService.getConversationHistory(conversationId, page, size, types);
    }

    @GetMapping("/group-conversation/{conversationId}/history")
    @Operation(summary = "Get group conversation history", description = "Retrieves paginated chat messages for a group conversation")
    public Page<GroupChatMessageResponseDto> getGroupConversationHistory(
            @Parameter(description = "HighLight conversation ID", required = true)
            @PathVariable String conversationId,

            @Parameter(description = "Page number (0-based)", schema = @Schema(type = "integer", minimum = "0"))
            @RequestParam(defaultValue = "0") int page,

            @Parameter(description = "Number of items per page", schema = @Schema(type = "integer", minimum = "1", maximum = "100"))
            @RequestParam(defaultValue = "20") int size,

            @Parameter(description = "Type of the messages")
            @RequestParam(required = false) List<ConversationMessageType> types
            ) {
        return chatMessageService.getGroupConversationHistory(conversationId, page, size, types);
    }

    @GetMapping("/group-conversation/{conversationId}/members")
    @Operation(summary = "Get group conversation members",
            description = "Retrieves paginated list of members in a group conversation. Filter by member status using includeInactive parameter.")
    public ResponseEntity<PageDto<UserDto>> getGroupMembers(
            @Parameter(description = "HighLight conversation ID", required = true)
            @PathVariable String conversationId,

            @Parameter(description = "Include inactive members", schema = @Schema(type = "boolean"))
            @RequestParam(defaultValue = "false") boolean includeInactive,

            @Parameter(description = "Page number (0-based)", schema = @Schema(type = "integer", minimum = "0"))
            @RequestParam(defaultValue = "0") int page,

            @Parameter(description = "Number of items per page", schema = @Schema(type = "integer", minimum = "1", maximum = "100"))
            @RequestParam(defaultValue = "20") int size
    ) {
        PageDto<com.hb.crm.core.dtos.UserDto> members = chatMessageService.getGroupMembers(conversationId, includeInactive, page, size);
        return ResponseEntity.ok(members);
    }

    @PutMapping("/group-conversation/{userConversationId}/mute")
    @Operation(summary = "Mute/Unmute group conversation",
            description = "Toggles mute status for the current user in a group conversation")
    public ResponseEntity<Void> muteGroupConversation(
            @Parameter(description = "HighLight conversation ID", required = true)
            @PathVariable String userConversationId,

            @Parameter(description = "Mute status (true to mute, false to unmute)", required = true)
            @RequestParam boolean mute
    ) {
        chatMessageService.muteGroup(userConversationId, mute);
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/messages/batch")
    @Operation(summary = "Delete multiple messages",
            description = "Delete multiple messages either for the current user only or for everyone")
    public ResponseEntity<Void> deleteMessages(
            @Parameter(description = "List of message IDs to delete", required = true)
            @RequestBody List<String> messageIds,

            @Parameter(description = "If true, deletes for everyone (sender only). If false, deletes only for current user")
            @RequestParam(defaultValue = "ForMe") DeletionType deletionType,

            @Parameter(description = "User ID")
            @RequestParam(required = false) String userId
            ) {
        if(userId == null)
            userId = userSessionData.getId();
        chatMessageService.deleteMessage(messageIds, userId, deletionType);
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/group-messages/batch")
    @Operation(summary = "Delete multiple group messages",
            description = "Delete multiple group messages either for the current user only or for everyone")
    public ResponseEntity<Void> deleteGroupMessages(
            @Parameter(description = "List of message IDs to delete", required = true)
            @RequestBody List<String> messageIds,

            @Parameter(description = "If true, deletes for everyone (sender only). If false, deletes only for current user")
            @RequestParam(defaultValue = "ForMe") DeletionType deletionType,

            @Parameter(description = "User ID")
            @RequestParam(required = false) String userId
    ) {
        if(userId == null)
            userId = userSessionData.getId();
        chatMessageService.deleteGroupMessage(messageIds, userId, deletionType);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/user-chat/conversation")
    @Operation(summary = "Create new conversation",
            description = "Creates a new conversation and associates it with a user")
    public ResponseEntity<ConversationDto> createConversation(
            @Parameter(description = "User ID", required = true)
            @RequestParam String userId,

            @Parameter(description = "package id")
            @RequestParam String packageId
    ) {
        var result = chatMessageService.createConversation(userId, packageId);
        return ResponseEntity.ok(result);
    }

    @PutMapping("/user-chat/{conversationId}/settings")
    @Operation(summary = "Update conversation settings",
            description = "Updates mute and closed status for a conversation")
    public ResponseEntity<ConversationDto> updateConversationSettings(
            @Parameter(description = "Conversation ID", required = true)
            @PathVariable String conversationId,

            @Parameter(description = "Mute status (true to mute, false to unmute)")
            @RequestParam(defaultValue = "false") Boolean isMuted,

            @Parameter(description = "Closed status (true to close, false to open)")
            @RequestParam(defaultValue = "false") Boolean isClosed
    ) {
        ConversationDto result = chatMessageService.updateConversationSettings(conversationId, isMuted, isClosed);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/chats/unread")
    @Operation(summary = "Get all chats with unread counts",
            description = "Retrieves all chats (both one-to-one and group) with their unread message counts")
    public ResponseEntity<List<ChatWithUnreadCountDto>> getAllChatsWithUnreadCount() {
        String userId = userSessionData.getId();
        List<ChatWithUnreadCountDto> chats = chatMessageService.getAllChatsWithUnreadCount(userId);
        return ResponseEntity.ok(chats);
    }

    @GetMapping("/messages/search")
    @Operation(summary = "Search messages",
              description = "Search messages across all conversations with text and date filters")
    public ResponseEntity<PageDto<MessageSearchResultDto>> searchMessages(
            @Parameter(description = "Search text")
            @RequestParam(required = false) String searchText,
            
            @Parameter(description = "Start date (ISO format)")
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            
            @Parameter(description = "End date (ISO format)")
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            
            @Parameter(description = "Page number (0-based)")
            @RequestParam(defaultValue = "0") int page,
            
            @Parameter(description = "Number of items per page")
            @RequestParam(defaultValue = "20") int size
    ) {
        PageDto<MessageSearchResultDto> results = chatMessageService.searchMessages(searchText, startDate,
                endDate, page, size
        );
        return ResponseEntity.ok(results);
    }

    @GetMapping("/search")
    public ResponseEntity<PageDto<ChatWithUnreadCountDto>> searchConversations(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            @RequestParam(required = false) String readStatus,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "desc") String sortDirection
    ) {
        PageDto<ChatWithUnreadCountDto> result = chatMessageService.searchConversations(
                startDate,
                endDate,
                readStatus,
                page,
                size,
                sortDirection
        );
        return ResponseEntity.ok(result);
    }
}
