package com.hb.crm.client.controllers;

import com.hb.crm.client.beans.UserSessionData;
import com.hb.crm.client.dto.notification.FilterUserNotificationDto;
import com.hb.crm.core.beans.Notification.CrmNotification;
import com.hb.crm.core.dtos.notification.CreateCrmNotificationDto;
import com.hb.crm.core.services.interfaces.CrmNotificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/v1/crm-notification")
@RequiredArgsConstructor
@Tag(name = "Notification Management", description = "Endpoints for managing user notifications")
public class CrmNotificationController {

    private final CrmNotificationService crmNotificationService;
    private final UserSessionData userSessionData;

    @Operation(
            summary = "Create new notification entry"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Notification created",
                    content = @Content(schema = @Schema(implementation = CrmNotification.class))),
    })
    @PostMapping("/new")
    public ResponseEntity<CrmNotification> createNotification(@RequestBody CreateCrmNotificationDto dto) {
        return ResponseEntity.ok(crmNotificationService.createNotification(dto));
    }

    @GetMapping("/seed")
    public ResponseEntity<List<CrmNotification>> seed() {
        return ResponseEntity.ok(crmNotificationService.seed());
    }

    @Operation(
            summary = "Get a notification by ID",
            description = "Retrieves a specific notification using its unique identifier"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Notification found and returned",
                    content = @Content(schema = @Schema(implementation = CrmNotification.class))),
    })
    @GetMapping("/get/{id}")
    public ResponseEntity<CrmNotification> getNotification(@PathVariable("id") String id) {
        var notifications = crmNotificationService.getNotificationItem(id);
        return ResponseEntity.ok(notifications);
    }


    @Operation(
            summary = "Update notification read status",
            description = "Updates the read status of a specific notification"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Notification status updated successfully",
                    content = @Content(schema = @Schema(implementation = CrmNotification.class))),
    })
    @PostMapping("/update")
    public ResponseEntity<CrmNotification> setReadStatus(@RequestBody boolean read, @RequestBody String notificationId) {
        var notification = crmNotificationService.setReadStatus(read, notificationId);
        return ResponseEntity.ok(notification);
    }


    @Operation(
            summary = "Search/filter notifications",
            description = "Retrieves a list of notifications filtered by various criteria"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Notifications retrieved successfully",
                    content = @Content(schema = @Schema(implementation = CrmNotification[].class))),
    })
    @PostMapping("/search")
    public ResponseEntity<List<CrmNotification>> filterNotifications(@RequestBody FilterUserNotificationDto filter) {
        var username = userSessionData.getUsername();
        var notifications = crmNotificationService.filterUserNotification(
                filter.getSearchText(),
                filter.getFromDate(),
                filter.getToDate(),
                filter.getRead(),
                username,
                filter.getType(),
                filter.getPageNumber(),
                filter.getPageSize()
        );
        return ResponseEntity.ok(notifications);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete Notification Item")
    public ResponseEntity<Void> deleteNotificationChannel(@PathVariable String id) {
        crmNotificationService.delete(id);
        return ResponseEntity.noContent().build();
    }


}
