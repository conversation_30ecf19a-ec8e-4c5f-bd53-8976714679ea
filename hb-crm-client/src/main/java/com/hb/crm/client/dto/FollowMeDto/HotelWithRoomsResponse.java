package com.hb.crm.client.dto.FollowMeDto;

import java.util.List;
import com.hb.crm.core.beans.Hotel.Hotel;

/**
 * Response DTO for hotel with rooms operations in FollowMe
 */
public class HotelWithRoomsResponse {
    
    private boolean success;
    private String message;
    private String packageId;
    private List<Hotel> hotelsWithRooms;
    private Integer totalHotels;
    private Integer totalRooms;
    private String subscriptionStatus;

    public HotelWithRoomsResponse() {}

    public HotelWithRoomsResponse(boolean success, String message) {
        this.success = success;
        this.message = message;
    }

    public HotelWithRoomsResponse(boolean success, String message, String packageId, 
                                List<Hotel> hotelsWithRooms, String subscriptionStatus) {
        this.success = success;
        this.message = message;
        this.packageId = packageId;
        this.hotelsWithRooms = hotelsWithRooms;
        this.subscriptionStatus = subscriptionStatus;
        
        // Calculate totals
        this.totalHotels = hotelsWithRooms != null ? hotelsWithRooms.size() : 0;
        this.totalRooms = hotelsWithRooms != null 
            ? hotelsWithRooms.stream()
                .mapToInt(hotel -> hotel.getRooms() != null ? hotel.getRooms().size() : 0)
                .sum() 
            : 0;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getPackageId() {
        return packageId;
    }

    public void setPackageId(String packageId) {
        this.packageId = packageId;
    }

    public List<Hotel> getHotelsWithRooms() {
        return hotelsWithRooms;
    }

    public void setHotelsWithRooms(List<Hotel> hotelsWithRooms) {
        this.hotelsWithRooms = hotelsWithRooms;
        this.totalHotels = hotelsWithRooms != null ? hotelsWithRooms.size() : 0;
        this.totalRooms = hotelsWithRooms != null 
            ? hotelsWithRooms.stream()
                .mapToInt(hotel -> hotel.getRooms() != null ? hotel.getRooms().size() : 0)
                .sum() 
            : 0;
    }

    public Integer getTotalHotels() {
        return totalHotels;
    }

    public void setTotalHotels(Integer totalHotels) {
        this.totalHotels = totalHotels;
    }

    public Integer getTotalRooms() {
        return totalRooms;
    }

    public void setTotalRooms(Integer totalRooms) {
        this.totalRooms = totalRooms;
    }

    public String getSubscriptionStatus() {
        return subscriptionStatus;
    }

    public void setSubscriptionStatus(String subscriptionStatus) {
        this.subscriptionStatus = subscriptionStatus;
    }
}
