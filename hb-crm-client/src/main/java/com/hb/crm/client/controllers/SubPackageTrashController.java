package com.hb.crm.client.controllers;

import com.hb.crm.client.dto.PageDto;
import com.hb.crm.client.services.interfaces.SubPackageTrashService;
import com.hb.crm.core.Enums.PackageType;
import com.hb.crm.core.beans.SubPackage;
import com.hb.crm.core.beans.Trash.TrashedSubPackage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/client/trash/subpackages")
@Tag(name = "User SubPackage Trash", description = "APIs for users to manage their own trashed SubPackages")
public class SubPackageTrashController {

    @Autowired
    private SubPackageTrashService subPackageTrashService;

    @Operation(summary = "Soft delete a pending SubPackage", 
               description = "Users can only delete their own pending/draft packages. Posted packages cannot be deleted.")
    @PostMapping("/soft-delete")
    public ResponseEntity<Map<String, Object>> softDeletePendingSubPackage(
            @RequestBody Map<String, String> request) {
        try {
            String subPackageId = request.get("subPackageId");
            String deletionReason = request.get("deletionReason");
            
            TrashedSubPackage trashedSubPackage = subPackageTrashService.softDeletePendingSubPackage(
                subPackageId, 
                deletionReason
            );
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Package moved to trash successfully");
            response.put("trashedSubPackageId", trashedSubPackage.getId());
            response.put("deletedAt", trashedSubPackage.getDeletedAt());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("message", "Error moving package to trash: " + e.getMessage()));
        }
    }

    @Operation(summary = "Restore a SubPackage from trash", 
               description = "Users can only restore their own trashed packages")
    @PostMapping("/restore/{trashedSubPackageId}")
    public ResponseEntity<Map<String, Object>> restoreSubPackage(
            @Parameter(description = "ID of the trashed SubPackage to restore") 
            @PathVariable String trashedSubPackageId) {
        try {
            SubPackage restoredSubPackage = subPackageTrashService.restoreUserSubPackage(trashedSubPackageId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Package restored successfully");
            response.put("restoredSubPackageId", restoredSubPackage.getId());
            response.put("restoredAt", LocalDateTime.now());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("message", "Error restoring package: " + e.getMessage()));
        }
    }

    @Operation(summary = "Get user's trashed SubPackages with pagination", 
               description = "Retrieves all trashed SubPackages belonging to the current user")
    @GetMapping
    public ResponseEntity<PageDto<TrashedSubPackage>> getUserTrashedPackages(
            @Parameter(description = "Page number (zero-based)") 
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Number of items per page") 
            @RequestParam(defaultValue = "10") int size) {
        
        PageDto<TrashedSubPackage> result = subPackageTrashService.getUserTrashedPackages(page, size);
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "Get user's trashed SubPackages by type with pagination", 
               description = "Retrieves user's trashed SubPackages filtered by package type")
    @GetMapping("/by-type/{packageType}")
    public ResponseEntity<PageDto<TrashedSubPackage>> getUserTrashedPackagesByType(
            @Parameter(description = "Package type filter") 
            @PathVariable PackageType packageType,
            @Parameter(description = "Page number (zero-based)") 
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Number of items per page") 
            @RequestParam(defaultValue = "10") int size) {
        
        PageDto<TrashedSubPackage> result = subPackageTrashService.getUserTrashedPackagesByType(packageType, page, size);
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "Search user's trashed SubPackages", 
               description = "Searches user's trashed SubPackages using text search with pagination")
    @GetMapping("/search")
    public ResponseEntity<PageDto<TrashedSubPackage>> searchUserTrashedPackages(
            @Parameter(description = "Search query") 
            @RequestParam(required = false) String query,
            @Parameter(description = "Package type filter (optional)") 
            @RequestParam(required = false) PackageType packageType,
            @Parameter(description = "Page number (zero-based)") 
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Number of items per page") 
            @RequestParam(defaultValue = "10") int size) {
        
        PageDto<TrashedSubPackage> result;
        if (packageType != null) {
            result = subPackageTrashService.searchUserTrashedPackages(query, packageType, page, size);
        } else {
            result = subPackageTrashService.searchUserTrashedPackages(query, page, size);
        }
        
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "Get a specific user's trashed SubPackage by ID", 
               description = "Retrieves details of a specific trashed SubPackage belonging to the user")
    @GetMapping("/{trashedSubPackageId}")
    public ResponseEntity<TrashedSubPackage> getUserTrashedPackageById(
            @Parameter(description = "ID of the trashed SubPackage") 
            @PathVariable String trashedSubPackageId) {
        
        TrashedSubPackage trashedSubPackage = subPackageTrashService.getUserTrashedPackageById(trashedSubPackageId);
        return ResponseEntity.ok(trashedSubPackage);
    }

    @Operation(summary = "Permanently delete a user's trashed SubPackage", 
               description = "Permanently deletes a user's trashed SubPackage (cannot be undone)")
    @DeleteMapping("/permanent/{trashedSubPackageId}")
    public ResponseEntity<Map<String, String>> permanentlyDeleteUserTrashedPackage(
            @Parameter(description = "ID of the trashed SubPackage to permanently delete") 
            @PathVariable String trashedSubPackageId) {
        try {
            subPackageTrashService.permanentlyDeleteUserTrashedPackage(trashedSubPackageId);
            return ResponseEntity.ok(Map.of("message", "Package permanently deleted"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("message", "Error permanently deleting package: " + e.getMessage()));
        }
    }

    @Operation(summary = "Get user's trash statistics", 
               description = "Returns count statistics for user's trashed SubPackages")
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getUserTrashStats(
            @Parameter(description = "Package type filter (optional)") 
            @RequestParam(required = false) PackageType packageType) {
        
        Map<String, Object> stats = new HashMap<>();
        
        if (packageType != null) {
            stats.put("count", subPackageTrashService.getUserTrashedPackagesCount(packageType));
            stats.put("packageType", packageType);
        } else {
            stats.put("totalCount", subPackageTrashService.getTotalUserTrashedPackagesCount());
            stats.put("travelWithMeCount", subPackageTrashService.getUserTrashedPackagesCount(PackageType.TravelWithMe));
            stats.put("followMeCount", subPackageTrashService.getUserTrashedPackagesCount(PackageType.FollowMe));
        }
        
        return ResponseEntity.ok(stats);
    }

    @Operation(summary = "Check if user's SubPackage is in trash", 
               description = "Checks if a specific user's SubPackage is currently in trash")
    @GetMapping("/check/{subPackageId}")
    public ResponseEntity<Map<String, Object>> checkIfUserSubPackageInTrash(
            @Parameter(description = "Original SubPackage ID to check") 
            @PathVariable String subPackageId) {
        
        boolean inTrash = subPackageTrashService.isUserSubPackageInTrash(subPackageId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("subPackageId", subPackageId);
        response.put("inTrash", inTrash);
        
        return ResponseEntity.ok(response);
    }
}
