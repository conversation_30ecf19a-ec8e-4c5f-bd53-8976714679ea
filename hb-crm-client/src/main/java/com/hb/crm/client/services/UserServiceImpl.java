package com.hb.crm.client.services;

import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseAuthException;
import com.hb.crm.client.beans.CustomUser;
import com.hb.crm.client.beans.CustomUserDetails;
import com.hb.crm.client.beans.ObjectBoxEntity.InfluencerSyncEntity;
import com.hb.crm.client.beans.ObjectBoxEntity.PackageSyncEntity;
import com.hb.crm.client.beans.ObjectBoxEntity.TravelerInfoSyncEntity;
import com.hb.crm.client.beans.UserSessionData;
import com.hb.crm.client.config.CustomHundlerar.CustomException;
import com.hb.crm.client.dto.MediaWrapperDto;
import com.hb.crm.client.dto.PackageDtos.PlaceDto;
import com.hb.crm.client.dto.PageDto;
import com.hb.crm.client.dto.Results.*;
import com.hb.crm.client.dto.UserRegistrationDto;
import com.hb.crm.client.dto.mood.MoodDto;
import com.hb.crm.client.dto.posts.PostDto;
import com.hb.crm.client.dto.posts.PostPackageDto;
import com.hb.crm.client.dto.splashScreen.splashUserStories;
import com.hb.crm.client.dto.users.*;
import com.hb.crm.client.services.interfaces.*;
import com.hb.crm.core.CashService.CashService;
import com.hb.crm.core.CombinedKeys.FollowsKey;
import com.hb.crm.core.Enums.*;
import com.hb.crm.core.beans.*;
import com.hb.crm.core.beans.LiveStream.LiveStream;
import com.hb.crm.core.beans.Notification.NotificationSetting;
import com.hb.crm.core.dtos.MediaWithUserDto;
import com.hb.crm.core.repositories.*;
import com.hb.crm.core.repositories.LiveStream.LiveStreamRepository;
import com.hb.crm.core.searchBeans.ReactionSearch;
import com.hb.crm.core.searchBeans.SearchUser;
import com.hb.crm.core.searchBeans.simpleUserInfo;
import com.hb.crm.core.searchRepositories.ReactionSearchRepository;
import com.hb.crm.core.searchRepositories.SearchPackageRepository;
import com.hb.crm.core.searchRepositories.SearchUserRepository;
import com.hb.crm.core.services.interfaces.TwilioService;
import com.hb.crm.core.util.ApplicationUtil;
import com.hb.crm.core.util.UpdateUserUtil;
import com.hb.crm.core.util.UsernameUtil;
import io.objectbox.Box;
import io.objectbox.BoxStore;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class UserServiceImpl implements UserService {

    private static final int ALL_LIMIT = 99999;
    private final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);
    private final UserRepository userRepository;
    private final ModelMapper modelMapper;
    private final PackageService packageService;
    private final PostService postService;
    private final FollowsRepository followsRepository;
    private final ReactionSearchRepository reactionSearchRepository;
    private final MongoTemplate mongoTemplate;
    private final MongoTemplate mongoTemplate2;
    private final EmailService emailService;
    private final TwilioService twilioService;
    private final QueryNormalizeService queryNormalizeService;
    private final ReplyReactionRepository replyReactionRepository;
    private final CommentReactionRepository commentReactionRepository;
    private final PackageReactionRepository packageReactionRepository;
    private final PostReactionRepository postReactionRepository;
    private final CommentRepository commentsRepository;
    private final ReplyRepository replyRepository;
    private final CashService cashService;
    private final SettingService settingService;
    private final SearchUserRepository searchUserRepository;
    private final SearchPackageRepository searchPackageRepository;
    private final SettingRepository settingRepository;
    private final SubPackageRepository subPackageRepository;
    private final UpdateUserUtil updateUserUtil;
    private final NotificationSettingRepository notificationSettingRepository;
    private final MediaRepository mediaRepository;
    private final BoxStore boxStore;

    private final LiveStreamRepository liveStreamRepository;

    @Autowired
    private UserSessionData userSessionData;

    @Autowired
    public UserServiceImpl(UserRepository userRepository,
                           PackageService packageService, PostService postService,
                           FollowsRepository followsRepository,
                           @Qualifier("mongoTemplate1") MongoTemplate mongoTemplate,
                           ModelMapper modelMapper, ReactionSearchRepository reactionSearchRepository,
                           @Qualifier("mongoTemplate2") MongoTemplate mongoTemplate2,
                           EmailService emailService, TwilioService twilioService,
                           QueryNormalizeService queryNormalizeService,
                           ReplyReactionRepository replyReactionRepository,
                           CommentReactionRepository commentReactionRepository,
                           PackageReactionRepository packageReactionRepository,
                           PostReactionRepository postReactionRepository, CommentRepository commentsRepository,
                           ReplyRepository replyRepository, CashService cashService, SettingService settingService,
                           SearchUserRepository searchUserRepository, SearchPackageRepository searchPackageRepository,
                           SettingRepository settingRepository, SubPackageRepository subPackageRepository,
                           UpdateUserUtil updateUserUtil, NotificationSettingRepository notificationSettingRepository,
                           MediaRepository mediaRepository, BoxStore boxStore,
                           LiveStreamRepository liveStreamRepository) {
        this.userRepository = userRepository;
        this.packageService = packageService;
        this.postService = postService;
        this.followsRepository = followsRepository;
        this.mongoTemplate = mongoTemplate;
        this.modelMapper = modelMapper;
        this.reactionSearchRepository = reactionSearchRepository;
        this.mongoTemplate2 = mongoTemplate2;
        this.emailService = emailService;
        this.twilioService = twilioService;
        this.queryNormalizeService = queryNormalizeService;
        this.replyReactionRepository = replyReactionRepository;
        this.commentReactionRepository = commentReactionRepository;
        this.packageReactionRepository = packageReactionRepository;
        this.postReactionRepository = postReactionRepository;
        this.commentsRepository = commentsRepository;
        this.replyRepository = replyRepository;
        this.cashService = cashService;
        this.settingService = settingService;
        this.searchUserRepository = searchUserRepository;
        this.searchPackageRepository = searchPackageRepository;
        this.settingRepository = settingRepository;
        this.subPackageRepository = subPackageRepository;
        this.updateUserUtil = updateUserUtil;
        this.notificationSettingRepository = notificationSettingRepository;
        this.mediaRepository = mediaRepository;
        this.boxStore = boxStore;
        this.liveStreamRepository = liveStreamRepository;
    }

    @Override
    @Transactional
    public void syncInfluencerFollowersCount(InfluencerSyncEntity syncedInfluencerInfo) {
        User user = userRepository.findById(syncedInfluencerInfo.getInfluencerId())
                .orElseThrow(() -> new CustomException(404, "Influencer not found!"));

        if(user.getUsertype() != UserType.Influencer)
            throw new CustomException(400, "The user with this id is not influencer!");

        user.setFollwerscount(syncedInfluencerInfo.getFollowersCount());
        userRepository.save(user);
    }

    @Override
    public User save(UserRegistrationDto userDto) throws Exception {
        if (userDto == null)
            throw new Exception("Error in update admin User Cannot be null");

        User user = convertToEntity(userDto);
        if (user.getId() == null) {
            User travelerUser = getUserByEmail(userDto.getEmail());

            if (travelerUser == null) {
                travelerUser = getUserByMobile(userDto.getMobile());
                if (travelerUser != null && travelerUser.getPassword() != null)
                    user.setId(travelerUser.getId());
            }

            if (userDto.getMobile() == null || userDto.getMobile().isEmpty())
                user.setUsername(userDto.getEmail());
            else
                user.setUsername(userDto.getMobile());

            var saveduser = userRepository.save(user);

            // create search user for search
            searchUserRepository.save(new SearchUser(saveduser));

            // create notification setting for user
            notificationSettingRepository.save(new NotificationSetting(saveduser));

            return saveduser;
        }

        final Optional<User> byId = userRepository.findById(user.getId());
        if (byId.isEmpty())
            throw new Exception("Error in update admin User Cannot update");

        final User userBean = byId.get();
        userBean.setUsername(user.getUsername());
        userBean.setCity(user.getCity());
        userBean.setAccountLocked(user.isAccountLocked());
        userBean.setFailedLoginAttempts(0);
        userBean.setFirstName(user.getFirstName());
        userBean.setLastName(user.getLastName());
        userBean.setRole(user.getRole());

        if (userBean.getUserInfo() != null && !userBean.getUserInfo().getMobile().equals(userDto.getMobile())) {
            String code = GenerateRandomCode();
            userBean.setPhoneCode(code);
            userBean.setPhoneActiviated(false);

            if (!user.isEmailActivated()) {
                LocalDateTime lookupDate = LocalDateTime.now().plusMonths(1);
                userBean.setAccountLockDate(lookupDate);
            }
            UserInfo userInfo = user.getUserInfo();
            userInfo.setMobile(userDto.getMobile());
        }

        if (userBean.getUserInfo() == null) {
            UserInfo userInfo = new UserInfo();
            if (userDto.getMobile() != null)
                userInfo.setMobile(userDto.getMobile());
            if (userDto.getEmail() != null)
                userInfo.setEmail(userDto.getEmail());
            userBean.setUserInfo(userInfo);
        }

        if (userBean.getUserInfo() != null && !userBean.getUserInfo().getEmail().equals(userDto.getEmail())) {
            String code = GenerateRandomCode();
            userBean.setEmailCode(code);
            userBean.setEmailActivated(false);

            if (!user.isPhoneActiviated()) {
                LocalDateTime lookupDate = LocalDateTime.now().plusMonths(1);
                userBean.setAccountLockDate(lookupDate);
            }

            UserInfo userInfo = user.getUserInfo();
            userInfo.setEmail(userDto.getEmail());
        }

        userBean.setCode(user.getCode());
        userBean.setCollectionDeviceId(user.getCollectionDeviceId());

        var saveduser = userRepository.save(userBean);

        // create search user for search
        searchUserRepository.save(new SearchUser(saveduser));

        // create notification setting for user if it does not exist
        var userSetting = notificationSettingRepository.findByUser(saveduser);
        if (userSetting.isEmpty())
            notificationSettingRepository.save(new NotificationSetting(saveduser));

        return saveduser;
    }

    @Override
    public User save(User user) {
        return userRepository.save(user);
    }


    @Override
    public PageDto<UserInfoDto> getUsers() {
        return search(new HashMap<>(), -1, 999);
    }


    @Override
    public Optional<UserInfoDto> findById(String id) {
        return userRepository.findById(id).map(this::convertToDto);
    }

    @Override
    public UserInfoDto findByUsername(String username) {
        var user = userRepository.findByUsername(username);
        return modelMapper.map(user, UserInfoDto.class);
    }

    @Override
    public PageDto<UserStories> getUserStories(int pageNumber, int itemsPerPage) {
        Setting check = settingService.getByName("StoryCheckLifeTime");
        Setting storySetting = settingService.getByName("user-story-setting");

        final Pageable pageable = ApplicationUtil.createPageRequest(pageNumber, itemsPerPage,
                "latestUpdate", "DESC");

        Aggregation query;
        AggregationResults<UserStoriesResultDto> Results;

        if (Objects.equals(storySetting.getValue(), "all")) {
            query = queryNormalizeService.getUsersWithRecentStories(pageable,
                    Boolean.parseBoolean(check.getValue()));
            Results = mongoTemplate.aggregate(query, "user",
                    UserStoriesResultDto.class);
        } else {
            String id = userSessionData.getId();
            query = queryNormalizeService.getUsersWithRecentStories(id, pageable,
                    Boolean.parseBoolean(check.getValue()));
            Results = mongoTemplate.aggregate(query, "follows",
                    UserStoriesResultDto.class);
        }

        long totalCount = Results.getUniqueMappedResult() != null ? Results.getUniqueMappedResult().getTotalCount() : 0;
        List<UserStories> users = Results.getUniqueMappedResult().getFilteredResults();
        return new PageDto<>(itemsPerPage, totalCount, pageNumber, users);
    }

    @Override
    public PageDto<splashUserStories> getUserStoriesSplash(String username, int pageNumber, int itemsPerPage) 
    {
        Setting check = settingService.getByName("StoryCheckLifeTime");

        Setting storySetting = settingService.getByName("Story-Limit-PerUser");
        int limit;
        if (storySetting.getValue() == null || storySetting.getValue().isEmpty()) 
        {
            limit = -1;
        }
        else 
        {
            limit = Integer.parseInt(storySetting.getValue());
        }

        final Pageable pageable = ApplicationUtil.createPageRequest(pageNumber, itemsPerPage,
                "latestUpdate", "DESC");

        Aggregation query;
        AggregationResults<SplashUserStoriesResultDto> Results;

        String id = userSessionData.getId();
        Criteria criteria;
        List<ObjectId> infulancers=getUserObjectIdsByFollowerId(id);
        
        if (username != null && !username.isEmpty()) 
        {
            criteria = Criteria.where("username").is(username);

        } 
        else 
        {
            // List<ObjectId> infulancers = getUserObjectIdsByFollowerId(id);
            infulancers.add(new ObjectId(id));
            criteria = Criteria.where("_id").in(infulancers);
        }

        query = queryNormalizeService.getUsersWithRecentStoriesSplash(criteria, id, limit, pageable, Boolean.parseBoolean(check.getValue()));
        Results = mongoTemplate.aggregate(query, "user",
                SplashUserStoriesResultDto.class);

        long totalCount = Results.getUniqueMappedResult() != null ? Results.getUniqueMappedResult().getTotalCount() : 0;
        List<splashUserStories> users = Results.getUniqueMappedResult() != null ? Results.getUniqueMappedResult().getFilteredResults() : new ArrayList<>();
        // Extract user IDs from users who have stories
        Set<String> userIdsWithStories = users.stream()
        .map(splashUserStories::getId)
        .collect(Collectors.toSet());

        // Find influencer IDs that don't have stories
        List<String> userIdsNotHaveStories = infulancers.stream()
        .map(ObjectId::toString)
        .filter(influencerId -> !userIdsWithStories.contains(id))
        .collect(Collectors.toList());
        
        // Find users with live streams who don't have stories
        if (!userIdsNotHaveStories.isEmpty()) {
            // Convert to ObjectIds for live stream query
            List<ObjectId> userObjectIds = userIdsNotHaveStories.stream()
                .map(ObjectId::new)
                .collect(Collectors.toList());
            
            // Find active live streams
            Criteria liveStreamCriteria = Criteria.where("infulancer.$id").in(userObjectIds)
                .and("status").is(LiveStreamStatus.LIVE);
            List<LiveStream> activeLiveStreams = mongoTemplate.find(new Query(liveStreamCriteria), LiveStream.class);
            
            // HighLight live streams by user and get the most recent one for each user
            Map<String, LiveStream> latestLiveStreamPerUser = activeLiveStreams.stream()
                .filter(stream -> stream.getInfulancer() != null && stream.getInfulancer().getId() != null)
                .collect(Collectors.groupingBy(
                    stream -> stream.getInfulancer().getId(),
                    Collectors.collectingAndThen(
                        Collectors.maxBy(Comparator.comparing(LiveStream::getCreatedAt)),
                        Optional::get
                    )
                ));
            
            // Create splash user stories for users with latest live streams
            for (Map.Entry<String, LiveStream> entry : latestLiveStreamPerUser.entrySet()) {
                String userId = entry.getKey();
                LiveStream liveStream = entry.getValue();
                
                User user = userRepository.findById(userId).orElse(null);
                if (user != null) {
                    splashUserStories liveStreamUser = new splashUserStories();
                    liveStreamUser.setId(userId);
                    liveStreamUser.setFirstName(user.getFirstName());
                    liveStreamUser.setLastName(user.getLastName());
                    liveStreamUser.setUsername(user.getUsername());
                    liveStreamUser.setProfileImage(user.getProfileImage());
                    liveStreamUser.setHasLiveStream(true);
                    liveStreamUser.setLiveStreamId(liveStream.getId());
                    liveStreamUser.setPlayBackUrl(liveStream.getPlaybackUrl());
                    liveStreamUser.setStories(new ArrayList<>()); // Empty stories list
                    
                    users.add(liveStreamUser);
                }
            }
        }
        
        setLiveStreamStatus(users);

        // Prioritize: 1) Logged-in user first, 2) Users with live streams, 3) Others
        users.sort((user1, user2) -> 
        {
            boolean user1IsCurrentUser = id.equals(user1.getId());
            boolean user2IsCurrentUser = id.equals(user2.getId());
            boolean user1HasLiveStream = user1.isHasLiveStream();
            boolean user2HasLiveStream = user2.isHasLiveStream();
            
            // Logged-in user always comes first
            if (user1IsCurrentUser && !user2IsCurrentUser) 
            {
                return -1; // user1 (current user) comes before user2
            } 
            else if (!user1IsCurrentUser && user2IsCurrentUser) 
            {
                return 1; // user2 (current user) comes before user1
            }
            
            // If neither or both are current user, then prioritize by live stream status
            if (user1HasLiveStream && !user2HasLiveStream)
            {
                return -1; // user1 comes before user2
            } 
            else if (!user1HasLiveStream && user2HasLiveStream) 
            {
                return 1; // user2 comes before user1
            } 
            else 
            {
                return 0; // Keep original order for users with same status
            }
        });
        
        return new PageDto<>(itemsPerPage, totalCount, pageNumber, users);
    }
    
    private void setLiveStreamStatus(List<splashUserStories> users) 
    {
        // Early return for null or empty input
        if (users == null || users.isEmpty()) {
            return;
        }
        
        try 
        {
            // Extract all unique user IDs from the splash user stories 
            Set<String> userIds = users.stream()
                    .filter(Objects::nonNull)
                    .map(splashUserStories::getId)
                    .filter(Objects::nonNull)
                    .filter(id -> !id.trim().isEmpty())
                    .collect(Collectors.toSet());
            
            if (userIds.isEmpty()) 
            {
                return;
            }
            // Convert user IDs to ObjectIds 
            List<ObjectId> userObjectIds = userIds.stream()
                    .map(userId -> 
                    {
                        try 
                        {
                            return new ObjectId(userId);
                        }
                        catch (IllegalArgumentException e) 
                        {
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            
            if (userObjectIds.isEmpty()) 
            {
                return;
            }
            
            // Create criteria to find active live streams for these users
            Criteria criteria = Criteria.where("infulancer.$id").in(userObjectIds)
                    .and("status").is(LiveStreamStatus.LIVE);
            
            // Query for all active live streams using MongoTemplate
            Query query = new Query(criteria);
            List<LiveStream> activeLiveStreams = mongoTemplate.find(query, LiveStream.class);
                        
            Map<String, LiveStream> userToLiveStreamMap = activeLiveStreams.stream()
                    .filter(Objects::nonNull)
                    .filter(stream -> stream.getInfulancer() != null)
                    .filter(stream -> stream.getInfulancer().getId() != null)
                    .collect(Collectors.toMap(
                            stream -> stream.getInfulancer().getId(),
                            stream -> stream,
                            (existing, duplicate) -> 
                            {
                                if (existing.getCreatedAt() != null && duplicate.getCreatedAt() != null) 
                                {
                                    return existing.getCreatedAt().isAfter(duplicate.getCreatedAt()) ? existing : duplicate;
                                }
                                return existing;
                            }
                    ));
            
            // Set the live stream related fields for each user story 
            users.forEach(userStories -> 
            {
                if (userStories == null || userStories.getId() == null)
                {
                    return; 
                }
                
                try 
                {
                    String userId = userStories.getId();
                    LiveStream userLiveStream = userToLiveStreamMap.get(userId);
                    
                    if (userLiveStream != null) 
                    {
                        userStories.setHasLiveStream(true);
                        userStories.setLiveStreamId(userLiveStream.getId());
                        userStories.setPlayBackUrl(userLiveStream.getPlaybackUrl());
                    } 
                    else 
                    {
                        userStories.setHasLiveStream(false);
                        userStories.setLiveStreamId(null);
                        userStories.setPlayBackUrl(null);
                    }
                } 
                catch (Exception e) 
                {
                    userStories.setHasLiveStream(false);
                    userStories.setLiveStreamId(null);
                    userStories.setPlayBackUrl(null);
                }
            });
                        
        } 
        catch (Exception e) 
        {
            users.forEach(userStories -> 
            {
                if (userStories != null) 
                {
                    userStories.setHasLiveStream(false);
                    userStories.setLiveStreamId(null);
                    userStories.setPlayBackUrl(null);
                }
            });
        }
    }
    public List<ObjectId> getUserObjectIdsByFollowerId(String followerId) {
        ObjectId followerObjId = new ObjectId(followerId);

        AggregationOperation match = context -> new Document("$match",
                new Document("_id.follower.$id", followerObjId)
        );

        AggregationOperation project = context -> new Document("$project",
                new Document("userId", "$_id.user.$id")
        );

        Aggregation agg = Aggregation.newAggregation(match, project);

        AggregationResults<Document> results = mongoTemplate.aggregate(agg, "follows", Document.class);

        return results.getMappedResults().stream()
                .map(doc -> doc.get("userId", ObjectId.class))
                .collect(Collectors.toList());
    }


    /**
     * Retrieves paginated media content for a specific user filtered by media type.
     *
     * @param userId          The unique identifier of the user whose media is being retrieved
     * @param mediaTypeFilter The type of media to filter (e.g., IMAGE, VIDEO, etc.)
     * @param pageNumber      The page number for pagination (zero-based)
     * @param itemsPerPage    The number of items to display per page
     * @return PageDto<Media> A paginated list of media items with total count and page information
     */
    @Override
    public PageDto<MediaWithUserDto> getUserMedia(String userId, MediaType mediaTypeFilter, int pageNumber, int itemsPerPage) {
        // Create pageable object with sorting by latestUpdate in descending order
        final Pageable pageable = ApplicationUtil.createPageRequest(pageNumber, itemsPerPage,
                "latestUpdate", "DESC");

        // Build and execute the aggregation query
        Aggregation query = queryNormalizeService.getUserMedia(userId, mediaTypeFilter, pageable);
        var Results = mongoTemplate.aggregate(query, "media",
                UserMediaResultDto.class);

        // Extract total count from results, default to 0 if no results
        long totalCount = Results.getUniqueMappedResult() != null ? Results.getUniqueMappedResult().getTotalCount() : 0;

        // Get the filtered results list
        List<MediaWithUserDto> users = Results.getUniqueMappedResult().getFilteredResults();

        // Return paginated results wrapped in PageDto
        return new PageDto<>(itemsPerPage, totalCount, pageNumber, users);
    }


    @Override
    public List<PostDto> getUserStories(String userId) {
        return getUserStoriesByCriteria(Criteria.where("user._id").is(new ObjectId(userId)));
    }

    @Override
    public List<PostDto> getUserStoriesByUsername(String username) {
        return getUserStoriesByCriteria(Criteria.where("user.username").is(username));
    }


    private List<PostDto> getUserStoriesByCriteria(Criteria criteria) {
        Aggregation aggregation = queryNormalizeService.getPostFields(criteria, null, null);

        return mongoTemplate.aggregate(aggregation, "post", PostDto.class).getMappedResults();
    }

    // Mapper function for Post to PostDto
    private PostPackageDto mapToPostDto(Post post) {

        PostPackageDto postDto = new PostPackageDto();
        postDto.setId(post.getId());
        postDto.setText(post.getText());
        postDto.setMedia(post.getMedia().stream().map(z -> modelMapper.map(z, MediaWrapperDto.class)).collect(Collectors.toList()));
        postDto.setLatitude(post.getLatitude());
        postDto.setLongtuid(post.getLongtuid());
        postDto.setPlace(post.getPlace());
        postDto.setPostType(post.getPostType());
        postDto.setCreated(post.getCreated());
        postDto.setUpdate(post.getUpdate());
        postDto.setDescription(post.getDescription());
        postDto.setCommentsCount(post.getCommentsCount());
        postDto.setReactsCount(post.getReactsCount());
        postDto.setViewsCount(post.getViewsCount());
        postDto.setRejectionNote(post.getRejectionNote());

        // Map user to UserInfoDto
        if (post.getUser() != null) {
            UserInfoDto userInfoDto = new UserInfoDto();
            userInfoDto.setId(post.getUser().getId());
            userInfoDto.setFirstName(post.getUser().getFirstName());
            userInfoDto.setLastName(post.getUser().getLastName());
            userInfoDto.setProfileImage(post.getUser().getProfileImage());
            userInfoDto.setGender(post.getUser().getGender());
            userInfoDto.setGuestEmail(post.getUser().getGuestEmail());
            userInfoDto.setCoverImage(post.getUser().getCoverImage());
            userInfoDto.setPrivateProfile(post.getUser().isPrivateProfile());
            postDto.setUser(userInfoDto);
            postDto.setMoods(post.getUser().getMoods().stream().map(z -> {
                return modelMapper.map(z, MoodDto.class);
            }).toList());
        }

        // Map mentions to SimpleUserinfoDto
        if (post.getTaggedUsers() != null) {
            List<SimpleUserinfoDto> mentions = post.getTaggedUsers().stream()
                    .map(mention -> {
                        SimpleUserinfoDto mentionDto = new SimpleUserinfoDto();
                        mentionDto.setId(mention.getId());
                        mentionDto.setFirstName(mention.getFirstName());
                        mentionDto.setLastName(mention.getLastName());
                        return mentionDto;
                    })
                    .toList();
            postDto.setMentions(mentions);
        }

        // Additional mappings for moods, tags, etc.
        postDto.setTags(post.getTags());

        return postDto;
    }


    @Override
    public void UnFollowInfluence(String Influence) {
        String id = userSessionData.getId();
        Criteria criteria = new Criteria();
        criteria.orOperator(Criteria.where("_id").is(new ObjectId(id)),
                Criteria.where("_id").is(new ObjectId(Influence)));
        Aggregation query = queryNormalizeService.getUserFields(criteria);
        List<User> users = mongoTemplate.aggregate(query, "user", User.class).getMappedResults();
        Optional<User> byId = users.stream().filter(z -> z.getId().equals(id)).findAny();
        Optional<User> InfluenceOp = users.stream().filter(z -> z.getId().equals(Influence)).findAny();
        if (InfluenceOp.isPresent()) {
            if (byId.isPresent()) {
                User user = byId.get();
                User Influence1 = InfluenceOp.get();

                Criteria FollowCriteria = new Criteria();
                FollowCriteria.orOperator(Criteria.where("_id.user._id").is(new ObjectId(Influence)),
                        Criteria.where("_id.follower._id").is(new ObjectId(id)));

                Aggregation followAggregation = queryNormalizeService.getFollowsFields(FollowCriteria, null, null);
                List<Follows> Follows = mongoTemplate.aggregate(followAggregation, "follows", Follows.class).getMappedResults();
                Optional<Follows> follow = Follows.stream()
                        .filter(z ->
                                z.getId() != null && z.getId().getUser() != null && z.getId().getUser().getId() != null &&
                                        z.getId().getFollower() != null && z.getId().getFollower().getId() != null &&
                                        z.getId().getUser().getId().equals(Influence)
                                        && z.getId().getFollower().getId().equals(id))
                        .findAny();

                if (follow.isPresent()) {
                    followsRepository.delete(follow.get());

                    long InfluencedFollowers = Follows.stream()
                            .filter(z ->
                                    z.getId() != null && z.getId().getUser() != null && z.getId().getUser().getId() != null &&
                                            z.getId().getFollower() != null && z.getId().getFollower().getId() != null &&
                                            z.getId().getUser()
                                                    .getId().equals(Influence)).count();
                    InfluencedFollowers--;
                    Influence1.setFollwerscount((int) InfluencedFollowers);
                    userRepository.save(Influence1);
                    long followingCounts = Follows.stream()
                            .filter(z -> z.getId() != null && z.getId().getFollower() != null
                                    && z.getId().getFollower().getId() != null
                                    && z.getId().getFollower().getId().equals(id)
                            ).count();
                    followingCounts--;
                    user.setFollowingcount((int) followingCounts);
                    userRepository.save(user);
                }
            }
        }

    }

    @Override
    public void FollowInfluence(String Influence) {
        String id = userSessionData.getId();
        logger.info("user Id {}", id);
        logger.info("Influence Id {}", Influence);

        Criteria criteria = new Criteria();
        criteria.orOperator(Criteria.where("_id").is(new ObjectId(id)),
                Criteria.where("_id").is(new ObjectId(Influence)));
        Aggregation query = queryNormalizeService.getUserFields(criteria);
        List<User> users = mongoTemplate.aggregate(query, "user", User.class).getMappedResults();
        logger.info("searching for users Id");

        Optional<User> byId = users.stream().filter(z -> z.getId().equals(id)).findAny();
        Optional<User> InfluenceOp = users.stream().filter(z -> z.getId().equals(Influence)).findAny();
        logger.info("find both users ");

        if (InfluenceOp.isPresent()) {
            if (byId.isPresent()) {
                logger.info("getting user by ID ");

                User user = byId.get();
                logger.info("getting user done ");

                Criteria FollowCriteria = new Criteria();
                FollowCriteria.orOperator(Criteria.where("_id.user._id").is(new ObjectId(Influence)),
                        Criteria.where("_id.follower._id").is(new ObjectId(id)));
                Aggregation followAggregation = queryNormalizeService.getFollowsFields(FollowCriteria, null, null);
                List<Follows> Follows = mongoTemplate.aggregate(followAggregation, "follows", Follows.class).getMappedResults();
                logger.info("the fowllows getting done ");
                FollowsKey followsKey = new FollowsKey();
                followsKey.setFollower(user);
                User Influence1 = InfluenceOp.get();
                followsKey.setUser(Influence1);
                logger.info("Follow searching");

                Optional<Follows> follow = Follows.stream()
                        .filter(z -> z.getId() != null && z.getId().getUser() != null && z.getId().getUser().getId() != null &&
                                z.getId().getUser().getId().equals(Influence)
                                && z.getId().getFollower() != null && z.getId().getFollower().getId() != null
                                && z.getId().getFollower().getId().equals(id))
                        .findAny();
                logger.info("Follow searching done");

                if (follow.isEmpty()) {
                    logger.info("Follow not found");

                    Follows follows = new Follows();
                    follows.setId(followsKey);
                    logger.info("counting follows");

                    long InfluencedFollowers = Follows.stream()
                            .filter(z -> z.getId() != null && z.getId().getUser() != null && z.getId().getUser().getId() != null
                                    && z.getId().getFollower() != null && z.getId().getFollower().getId() != null
                                    && z.getId().getUser()
                                    .getId().equals(Influence)).count();
                    logger.info("counting follows done");

                    InfluencedFollowers++;
                    Influence1.setFollwerscount((int) InfluencedFollowers);
                    long followingCounts = Follows.stream()
                            .filter(z -> z.getId() != null && z.getId().getFollower() != null
                                    && z.getId().getFollower().getId() != null
                                    && z.getId().getFollower().getId().equals(id)
                            ).count();
                    followingCounts++;
                    user.setFollowingcount((int) followingCounts);

                    followsRepository.save(follows);
                    userRepository.save(user);
                    userRepository.save(Influence1);

                    // update search entity
                    var searchUserFollowerOpt = searchUserRepository.findById(user.getId());
                    var searchUserInfluencerOpt = searchUserRepository.findById(Influence1.getId());
                    SearchUser searchUserFollower;
                    SearchUser searchUserInfluencer;

                    searchUserFollower = searchUserFollowerOpt.orElseGet(() -> new SearchUser(user));
                    searchUserInfluencer = searchUserInfluencerOpt.orElseGet(() -> new SearchUser(Influence1));

                    searchUserFollower.setFollowingcount(user.getFollowingcount());
                    searchUserInfluencer.setFollwerscount(Influence1.getFollwerscount());

                    searchUserRepository.save(searchUserFollower);
                    searchUserRepository.save(searchUserInfluencer);
                }
            }
        }
    }

    @Override
    public String CheckAndMigrateUser(String uid, String email, String phone) throws FirebaseAuthException {
        Map<String, Object> claims = new HashMap<>();
        com.hb.crm.core.beans.User user = new User();
        com.hb.crm.core.beans.User ExistUser = new User();
        if (email == null || email.isEmpty()) {
            user = getUserByMobile(uid);
            ExistUser = getUserByEmail(phone);
        } else {
            user = getUserByEmail(uid);
            ExistUser = getUserByEmail(email);
        }

        if (user != null && ExistUser == null) {
            UserInfo userInfo = new UserInfo();
            userInfo.setEmail(email);
            user.setUserInfo(userInfo);
            user.setUsertype(UserType.Traveler);
            claims.put("appId", user.getId());
            claims.put("role", UserType.Traveler.toString());
            FirebaseAuth.getInstance().setCustomUserClaims(uid, claims);

            userRepository.save(user);
            return user.getId();

        } else if (user == null && ExistUser != null) {
            return ExistUser.getId();
        } else if (user != null) {
            if (user.getId().equals(ExistUser.getId())) {
                return ExistUser.getId();
            }
            migrateUser(user, ExistUser);
            return ExistUser.getId();
        } else {
            com.hb.crm.core.beans.User user1 = new com.hb.crm.core.beans.User();
            user1.setFirebaseId(uid);
            user1.setUsertype(UserType.Traveler);
            user1.setUsername(uid);
            user1.setEmailActivated(true);
            userRepository.save(user1);
            claims.put("appId", user1.getId());
            claims.put("role", user1.getUsertype().toString());
            claims.put("moodFlag", false);
            FirebaseAuth.getInstance().setCustomUserClaims(uid, claims);
            return user1.getId();
        }
    }

    private void migrateUser(User Gust, User user) {
        List<Like> likes = user.getLike();
        likes.addAll(Gust.getLike());
        user.setLike(likes);
        List<Post> posts = user.getPostBookMark();
        if (Gust.getPostBookMark() != null)
            posts.addAll(Gust.getPostBookMark());
        user.setPostBookMark(posts);
        userRepository.save(user);

        userRepository.delete(Gust);
    }

    @Override
    public List<UserInfoDto> GetAllInfluence() {
        Criteria FollowCriteria = new Criteria("usertype").is(UserType.Influencer);
        final Pageable pageable = ApplicationUtil.createPageRequest(0, 9999, "creationDate", "DESC");
        Aggregation aggregation = queryNormalizeService.getUsersList(FollowCriteria);
        List<User> users = mongoTemplate.aggregate(aggregation, "user", User.class).getMappedResults();
        
        return users.stream().map(user -> {
            UserInfoDto dto = convertToDto(user);
            LiveStream liveStream = liveStreamRepository.findByInfulancerIdAndStatus(user.getId(), LiveStreamStatus.LIVE);
            if (liveStream != null) {
                dto.setHasLiveStream(true);
                dto.setLiveStreamId(liveStream.getId());
                dto.setLiveStreamPlaybackUrl(liveStream.getPlaybackUrl());
            }
            return dto;
        }).collect(Collectors.toList());
    }

    public Page<UserInfoDto> GetAllInfluence(int page, int size) {
        Criteria followCriteria = Criteria.where("usertype").is(UserType.Influencer);

        // Create pageable with sorting (e.g., by creationDate descending)
        final Pageable pageable = ApplicationUtil.createPageRequest(page, size, "creationDate", "DESC");

        // Get the paginated aggregation pipeline
        Aggregation aggregation = queryNormalizeService.getUsersList(followCriteria);

                // Execute the aggregation to get the current page data
        List<User> users = mongoTemplate.aggregate(aggregation, "user", User.class).getMappedResults();
        
        List<UserInfoDto> influences = users.stream().map(user -> {
            UserInfoDto dto = convertToDto(user);
            LiveStream liveStream = liveStreamRepository.findByInfulancerIdAndStatus(user.getId(), LiveStreamStatus.LIVE);
            if (liveStream != null) {
                dto.setHasLiveStream(true);
                dto.setLiveStreamId(liveStream.getId());
                dto.setLiveStreamPlaybackUrl(liveStream.getPlaybackUrl());
            }
            return dto;
        }).collect(Collectors.toList());


        // Get total count of documents matching criteria (without pagination)
        long total = mongoTemplate.count(new Query(followCriteria), User.class);

        return new PageImpl<>(influences, pageable, total);
    }


    @Override
    public PageDto<UserInfoDto> search(Map<String, Object> obj, int page, int limit) {

        if (page < 0) {
            limit = ALL_LIMIT;
            page = 0;
        }
        PageDto<UserInfoDto> pageDto = new PageDto<>();
        final Pageable pageable = PageRequest.of(page, limit);
        final Query searchQuery = createSearchSpecification(obj);
        searchQuery.with(Sort.by(Sort.Direction.DESC, "creationDate"));
        searchQuery.with(pageable);
        final Query CountQuery = createSearchSpecification(obj);
        List<UserInfoDto> users = mongoTemplate.find(searchQuery, User.class).stream().map(user -> {
            UserInfoDto dto = convertToDto(user);
            LiveStream liveStream = liveStreamRepository.findByInfulancerIdAndStatus(user.getId(), LiveStreamStatus.LIVE);
            if (liveStream != null) {
                dto.setHasLiveStream(true);
                dto.setLiveStreamId(liveStream.getId());
                dto.setLiveStreamPlaybackUrl(liveStream.getPlaybackUrl());
            }
            return dto;
        }).collect(Collectors.toList());
        long count = mongoTemplate.count(CountQuery, User.class);
        pageDto.setTotalNoOfItems(count);

        pageDto.setItems(users);
        return pageDto;
    }

    @Override
    public SearchUserScoredResultDto recommendedv2(Map<String, Object> obj, int page, int limit) {
        if (page < 0) page = 0;

        // Enforce a maximum limit of 10 records
        final int MAX_LIMIT = 10;

        // Adjust limit logic
        if (limit > MAX_LIMIT) limit = MAX_LIMIT;

        List<ObjectId> followedInfluencersObjectIds;
        List<String> moods = new ArrayList<>();

        // Get current user
        Aggregation userQuery = queryNormalizeService.getUserFields(Criteria.where("_id").is(new ObjectId(userSessionData.getId())));
        User user = mongoTemplate.aggregate(userQuery, "user", User.class)
                .getMappedResults()
                .stream()
                .findAny()
                .orElse(null);

        // Extract moods from the user
        if (user != null && user.getMoods() != null) {
            moods = user.getMoods().stream().map(Mood::getTitle).sorted().toList();
        }

        // Get followed influencers
        assert user != null;
        var followedInfluencers = reactionSearchRepository.findByEntityNameAndEntityTypeAndUserId(
                EntityName.User,
                EntityType.Follow,
                user.getId()
        );

        followedInfluencersObjectIds = followedInfluencers.stream()
                .map(ReactionSearch::getEntityId)
                .filter(Objects::nonNull)
                .map(ObjectId::new)
                .toList();

        // Calculate the correct pagination logic
        int skip = page * limit;
        int remainingRecords = MAX_LIMIT - skip;
        if (remainingRecords <= 0) {
            // No records should be returned if skip exceeds max records
            var emptyResult = new SearchUserScoredResultDto();
            emptyResult.setFilteredResults(new ArrayList<>());
            emptyResult.setTotalCount(0);
            return emptyResult;
        }

        int effectiveLimit = Math.min(limit, remainingRecords);

        // Create the query with the adjusted pagination logic
        Pageable pageable = PageRequest.of(0, effectiveLimit);
           Criteria criteria =    Criteria.where("usertype").is(UserType.Influencer).and("_id").ne(new ObjectId(userSessionData.getId()));

        var query = queryNormalizeService.GetRecommendedInfluencers(criteria, moods, followedInfluencersObjectIds, pageable);
        var result = mongoTemplate2.aggregate(query, "searchUser", SearchUserScoredResultDto.class).getUniqueMappedResult();

        if (result != null && result.getTotalCount() > MAX_LIMIT) {
            result.setTotalCount(MAX_LIMIT);
        }

        return result;
    }

    @Override
    public PageDto<UserInfoDto> recommended(Map<String, Object> obj, int page, int limit) {
        String UserID= userSessionData.getId();

        if (page < 0) {
            limit = 10;
            page = 0;
        }
        PageDto<UserInfoDto> pageDto = new PageDto<>();
        final Pageable pageable = PageRequest.of(page, limit);
        final Query searchQuery = createSearchSpecification(obj);
        searchQuery.addCriteria(Criteria.where("usertype").is(UserType.Influencer).and("_id").ne(new ObjectId(UserID)));

        final Query CountQuery = createSearchSpecification(obj);
        CountQuery.addCriteria(Criteria.where("usertype").is(UserType.Influencer).and("_id").ne(new ObjectId(UserID)));

        searchQuery.with(Sort.by(Sort.Direction.DESC, "creationDate"));
        searchQuery.with(pageable);
        List<UserInfoDto> users = mongoTemplate.find(searchQuery, User.class).stream().map(this::convertToDto).collect(Collectors.toList());

        long count = mongoTemplate.count(CountQuery, User.class);
        pageDto.setTotalNoOfItems(count);

        pageDto.setItems(users);
        return pageDto;
    }


    @Override
    public searchUserResultDto topRatedv2(Map<String, Object> obj, int page, int limit) {
        if (page < 0) page = 0;
        String UserID= userSessionData.getId();
        // Enforce a maximum limit of 10 records
        final int MAX_LIMIT = 10;

        // Adjust limit logic
        if (limit > MAX_LIMIT) limit = MAX_LIMIT;

        // Calculate the correct pagination logic
        int skip = page * limit;
        int remainingRecords = MAX_LIMIT - skip;
        if (remainingRecords <= 0) {
            // No records should be returned if skip exceeds max records
            var emptyResult = new searchUserResultDto();
            emptyResult.setFilteredResults(new ArrayList<>());
            emptyResult.setTotalCount(0);
            return emptyResult;
        }

        int effectiveLimit = Math.min(limit, remainingRecords);

        final Pageable pageable = PageRequest.of(0, effectiveLimit, Sort.Direction.DESC, "follwerscount");
        var query = queryNormalizeService.getSearchUsers(
                Criteria.where("usertype").is(UserType.Influencer).and("_id").ne(new ObjectId(UserID)),
                null,
                pageable
        );

        var result = mongoTemplate2.aggregate(query, "searchUser", searchUserResultDto.class).getUniqueMappedResult();

        if (result != null && result.getTotalCount() > MAX_LIMIT) {
            result.setTotalCount(MAX_LIMIT);
        }

        return result;
    }


    @Override
    public PageDto<UserInfoDto> topRated(Map<String, Object> obj, int page) {

        int limit = Integer.parseInt(this.settingRepository.findByName("MaxTopRatedNumber").getValue());
        if (page < 0) {
            limit = ALL_LIMIT;
            page = 0;
        }
        PageDto<UserInfoDto> pageDto = new PageDto<>();
        final Pageable pageable = PageRequest.of(page, limit);
        final Query searchQuery = createSearchSpecification(obj);
        searchQuery.addCriteria(Criteria.where("usertype").is(UserType.Influencer));
        searchQuery.with(Sort.by(Sort.Direction.DESC, "follwerscount"));
        searchQuery.with(pageable);
        final Query CountQuery = createSearchSpecification(obj);
        CountQuery.addCriteria(Criteria.where("usertype").is(UserType.Influencer));
        List<UserInfoDto> users = mongoTemplate.find(searchQuery, User.class).stream().map(this::convertToDto).collect(Collectors.toList());

        long count = mongoTemplate.count(CountQuery, User.class);
        pageDto.setTotalNoOfItems(count);
        pageDto.setItems(users);
        pageDto.setItemsPerPage(limit);
        return pageDto;
    }

    private Query createSearchSpecification(Map<String, Object> obj) {
        Query query = new Query();
        for (Map.Entry<String, Object> entry : obj.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (value != null && !value.toString().isEmpty()) {
                query.addCriteria(Criteria.where(key).is(value.toString()));
            }
        }
        return query;
    }

    private Criteria createSearchSpecificationWithCobinedresults(Map<String, Object> obj) {
        List<Criteria> criteriaList = new ArrayList<>();

        // Global search term criteria (if provided)
        String searchTerm = obj.get("searchTerm") != null ? obj.get("searchTerm").toString() : "";
        if (!searchTerm.isEmpty()) {
            // Create an OR criteria across multiple fields
            Criteria searchCriteria = new Criteria().orOperator(
                    Criteria.where("firstName").regex(searchTerm, "i"),
                    Criteria.where("lastName").regex(searchTerm, "i")

            );
            criteriaList.add(searchCriteria);
        }

        // Optional filter by user type (if provided)
        String usertype = obj.get("usertype") != null ? obj.get("usertype").toString() : "";
        if (!usertype.isEmpty()) {
            criteriaList.add(Criteria.where("usertype").is(usertype));
        }

        // Combine all provided criteria using AND
        Criteria query = new Criteria();
        if (!criteriaList.isEmpty()) {
            query.andOperator(criteriaList.toArray(new Criteria[0]));
        }

        System.out.println("Constructed Criteria: " + query.getCriteriaObject().toJson());
        return query;
    }


    @Override
    public searchResultDto searchUserItems(String query, String InfluencerId, String InfulancerUserName, SearchEnum classFilter, int page, int size) {
        // Create pageable with descending sort on "updated" field.
        final Pageable pageable = PageRequest.of(page, size, Sort.Direction.DESC, "updated");
        Criteria data = new Criteria();
        if ((InfluencerId == null || InfluencerId.isEmpty()) && (InfulancerUserName == null || InfulancerUserName.isEmpty())) {
            throw new CustomException(400, "InfluencerId or InfluencerUserName one of them should not be empty");
        }
        if (InfluencerId == null) {
            InfluencerId = "";
        }
        if (classFilter.equals(SearchEnum.followPackage) || classFilter.equals(SearchEnum.Package)) {
            if (!InfluencerId.isEmpty()) {
                data = new Criteria("infulancer._id").is(new ObjectId(InfluencerId));
                ;
            } else {
                data = new Criteria("infulancer.username").is(InfulancerUserName);
                ;
            }
        }
        if (classFilter.equals(SearchEnum.story) || classFilter.equals(SearchEnum.post)) {
            if (!InfluencerId.isEmpty()) {
                data = new Criteria("user._id").is(new ObjectId(InfluencerId));
                ;
            } else {
                data = new Criteria("user.username").is(InfulancerUserName);
            }
        }
        // Retrieve the search aggregation pipeline based on query and classFilter.
        Aggregation aggregation = queryNormalizeService.getSearch(query, classFilter, pageable, data);
        // Execute the aggregation query against the "search" collection and map the results to the searchResultDto class.
        var result = mongoTemplate2.aggregate(aggregation, "search", searchResultDto.class);
        // Return a unique mapped result from the aggregation query.
        return result.getUniqueMappedResult();
    }


    @Override
    public UserCompleteRegiserationDto getUserinfo() {
        if (userSessionData.getId() == null)
            return null;
        String id = userSessionData.getId();
        final Optional<User> byId = userRepository.findById(id);
        if (byId.isEmpty())
            throw new CustomException(401, "user Not found");
        var MainUser = byId.get();
        var user = modelMapper.map(byId.get(), UserCompleteRegiserationDto.class);
        if (byId.get().getUserInfo() != null) {
            user.setMobile(byId.get().getUserInfo().getMobile());
        }


        return user;
    }

    @Override
    public UserFullInfoDto getFullUserinfo() {
        if (userSessionData.getId() == null)
            return null;
        String id = userSessionData.getId();
        final Optional<User> byId = userRepository.findById(id);
        if (byId.isEmpty())
            throw new CustomException(401, "user Not found");
        var user = modelMapper.map(byId.get(), UserFullInfoDto.class);

        if (byId.get().getUserInfo() != null) {
            user.setMobile(byId.get().getUserInfo().getMobile());
            user.setEmail(byId.get().getUserInfo().getEmail());
        }


        return user;
    }

    @Override
    public UserCompleteRegiserationDto completeUserRegistration(UserCompleteRegiserationDto user, String id) {
        final Optional<User> byId = userRepository.findById(id);
        if (byId.isEmpty())
            throw new CustomException(401, "user Not found");
        User userBean = byId.get();

        if (user.getBirthDate() != null)
            userBean.setBirthDate(user.getBirthDate());
        if (user.getCity() != null && !user.getCity().isEmpty())
            userBean.setCity(user.getCity());
        if (user.getGender() != null)
            userBean.setGender(user.getGender());
        if (user.getCoverImage() != null && !user.getCoverImage().isEmpty())
            userBean.setCoverImage(user.getCoverImage());
        if (user.getProfileImage() != null && !user.getProfileImage().isEmpty())
            userBean.setProfileImage(user.getProfileImage());

        if (user.getBirthDate() != null) {
            userBean.setBirthDate(user.getBirthDate());
        }
        if (user.getFirstName() != null && !user.getFirstName().isEmpty()) {

            userBean.setFirstName(user.getFirstName());
        }
        if (user.getLastName() != null && !user.getLastName().isEmpty()) {

            userBean.setLastName(user.getLastName());
        }
        if (user.getAbout() != null && !user.getAbout().isEmpty()) {

            userBean.setAbout(user.getAbout());
        }
        if ((userBean.getUsername() == null || userBean.getUsername().isEmpty())
                && user.getFirstName() != null
                && user.getLastName() != null) {
            userBean.setUsername(UsernameUtil.generateUniqueUsername(user.getFirstName(), user.getLastName()));
        }


        if (user.getCountry() != null && !user.getCountry().isEmpty())
            userBean.setCountry(user.getCountry());


        if (user.getPrivateProfile() != null)
            userBean.setPrivateProfile(user.getPrivateProfile());

        UserInfo userInfo = userBean.getUserInfo();

        if (userInfo == null) {
            userInfo = new UserInfo();
            if (user.getMobile() != null) {
                userInfo.setMobile(user.getMobile());
            }
        } else {
            if (user.getMobile() != null && (userBean.getUserInfo().getMobile() == null || (userBean.getUserInfo().getMobile() != null && userBean.getUserInfo().getMobile().isEmpty()))) {
                userInfo.setMobile(user.getMobile());
            }
        }
        userRepository.save(userBean);
        UserCompleteRegiserationDto dto = modelMapper.map(userBean, UserCompleteRegiserationDto.class);
        simpleUserInfo simpleUserInfo = new simpleUserInfo();
        simpleUserInfo.setFirstName(userBean.getFirstName());
        simpleUserInfo.setUsername(userBean.getUsername());
        simpleUserInfo.setLastName(userBean.getLastName());
        simpleUserInfo.setCoverImage(user.getCoverImage());
        simpleUserInfo.setProfileImage(user.getProfileImage());
        searchUserRepository.save(new SearchUser(userBean));

        updateUserUtil.updateSimpleUserInfoInSearchPosts(userBean.getId(),simpleUserInfo);

        return dto;
    }

    @Override
    public GuestInfo completeGustRegistration(GuestInfo user) {
        String id = userSessionData.getId();
        final Optional<User> byId = userRepository.findById(id);
        User userBean = new User();
        if (byId.isPresent())
            userBean = byId.get();
        userBean.setFirstName(user.getFirstName());
        userBean.setLastName(user.getLastName());

        UserInfo userInfo = userBean.getUserInfo();

        if (user.getBirthDate() != null) {
            userBean.setBirthDate(user.getBirthDate());
        }


        if (userInfo == null) {
            userInfo = new UserInfo();
            if (user.getMobile() != null) {
                userInfo.setMobile(user.getMobile());
            }
        } else {
            if (user.getMobile() != null && (userBean.getUserInfo().getMobile() == null || (userBean.getUserInfo().getMobile() != null && userBean.getUserInfo().getMobile().isEmpty()))) {
                userInfo.setMobile(user.getMobile());
            }
        }
        userInfo.setMobile(user.getMobile());
        userBean.setUserInfo(userInfo);
        userBean.setGuestEmail(user.getGuestEmail());

        userRepository.save(userBean);
        GuestInfo guestInfo = modelMapper.map(userBean, GuestInfo.class);
        guestInfo.setMobile(user.getMobile());
        return guestInfo;
    }

    public String GenerateRandomCode() {
        return RandomStringUtils.randomNumeric(5);
    }

    /**
     * Retrieves detailed information about a traveler user, including their subscribed packages
     * and followed influencers.
     *
     * @param id The ID of the traveler to fetch information for.
     *           If null or empty, the current user's session ID will be used.
     * @return {@link TravelerUserDto} containing:
     * - Basic user information
     * - List of subscribed packages
     * - List of followed influencers
     * @throws CustomException       with status 401 if the user is not found
     * @throws IllegalStateException if the model mapper fails to map the user data
     */
    @Override
    public TravelerUserDto getTravelerInfo(String id) {
        // Use current user's ID if none provided
        String userId = (id == null || id.isEmpty()) ? userSessionData.getId() : id;

        // Fetch user from repository
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new CustomException(401, "User not found"));

        // Map basic user information to DTO
        TravelerUserDto traveler = modelMapper.map(user, TravelerUserDto.class);
        if (user.getUserInfo() != null) {
            traveler.setEmail(user.getUserInfo().getEmail());
            traveler.setMobile(user.getUserInfo().getMobile());
        }

        // Populate subscribed packages
        traveler.setPackages(packageService.GetUserSubscribedPackages(userId));

        // Populate followed influencers
        traveler.setInfluencers(getFollowers(userId));

        return traveler;
    }


    @Override
    public TravelerUserDto getTravelerInfoByUsername(String username) {
        // Fetch user from repository
        User user = userRepository.findByUsernameAndUsertype(username, UserType.Traveler)
                .orElseThrow(() -> new CustomException(401, "User not found"));
        // Map basic user information to DTO
        TravelerUserDto traveler = modelMapper.map(user, TravelerUserDto.class);
        if (user.getUserInfo() != null) {
            traveler.setEmail(user.getUserInfo().getEmail());
            traveler.setMobile(user.getUserInfo().getMobile());
        }

        // Populate subscribed packages
        traveler.setPackages(packageService.GetUserSubscribedPackages(user.getId()));
        // Populate followed influencers
        traveler.setInfluencers(getFollowers(user.getId()));
        return traveler;
    }


    public void triggerAction() {
        String userId = userSessionData.getId();
        final Optional<User> byId = userRepository.findById(userId);
        if (byId.isEmpty())
            throw new CustomException(500, "User Not Found");
        User user = byId.get();
        PackageSyncEntity traveler = new PackageSyncEntity();
        traveler.setPackageId("67f50d3b92d8152941ad90a5");
        traveler.setCapacity(10);
        traveler.setSubscribeCount(10);

        Box<PackageSyncEntity> travelerBox = boxStore.boxFor(PackageSyncEntity.class);
        travelerBox.put(traveler);
    }


    public List<TravelerInfoSyncEntity> findActions() {
        return boxStore.boxFor(TravelerInfoSyncEntity.class)
                .query()
                .build()
                .find();
    }

    public void deleteAllTravelerInfo() {
        Box<TravelerInfoSyncEntity> box = boxStore.boxFor(TravelerInfoSyncEntity.class);
        box.removeAll();  // deletes all records in this box
        System.out.println("All TravelerInfoSyncEntity records deleted.");
    }


    /**
     * Retrieves a list of influencers that the specified user follows.
     *
     * @param userid The ID of the user whose followed influencers are to be retrieved.
     *               If null or empty, the current user's session ID will be used.
     * @return List of {@link InfluencerUserDto} containing information about the followed influencers.
     * Returns an empty list if no influencers are found.
     * @throws CustomException if there's an error accessing the repository or mapping the data
     */
    private List<InfluencerUserDto> getFollowers(String userid) {
        if (userid == null || userid.isEmpty())
            userid = userSessionData.getId();

        // Get followed influencers
        var followedInfluencers = reactionSearchRepository.findByEntityNameAndEntityTypeAndUserId(
                EntityName.User,
                EntityType.Follow,
                userid
        );

        // Extract influencer id from the reaction search object
        var influencersIds = followedInfluencers.stream().map(ReactionSearch::getEntityId).toList();

        // Find influencers by extracted ids
        return userRepository.findAllById(influencersIds)
                .stream()
                .map(user -> modelMapper.map(user, InfluencerUserDto.class))
                .toList();
    }


    /**
     * Retrieves detailed information about an influencer user, including their posts, subscribed packages,
     * media, stories, places visited, follower information, and execution times for different operations.
     *
     * @param id The ID of the user to fetch. If null or empty, the method fetches the ID from the current user session.
     * @return An {@link InfluencerUserDto} object containing all the gathered information about the influencer.
     * @throws CustomException if the user is not found.
     */
    @Override
    public InfluencerUserDto getInfluenceInfo(String id) {
        // If the provided ID is null or empty, use the current user session ID.
        if (id == null || id.isEmpty())
            id = userSessionData.getId();

        Aggregation query = queryNormalizeService.getUserFields(Criteria.where("_id").is(new ObjectId(id)));

        return getInfluencerInfo(query);

    }


    @Override
    public InfluencerUserDto getInfluenceInfoByUsername(String username) {

        Aggregation query = queryNormalizeService.getUserFields(Criteria.where("username")
                .is(username)
                .and("usertype").is(UserType.Influencer));

        return getInfluencerInfo(query);

    }


    private InfluencerUserDto getInfluencerInfo(Aggregation query) {

        // Start timing for user fetch operation.
        long UserFetchStart = System.currentTimeMillis();

        User user = mongoTemplate.aggregate(query, "user", User.class)
                .getMappedResults()
                .stream()
                .findAny()
                .orElseThrow(() -> new CustomException(404, "user not found"));

        logger.error("the user is {}  ", user.getId());

        // Map the fetched user data to an InfluencerUserDto object.
        InfluencerUserDto influence = modelMapper.map(user, InfluencerUserDto.class);
        if (user.getUserInfo() != null) {
            influence.setEmail(user.getUserInfo().getEmail());
            influence.setMobile(user.getUserInfo().getMobile());
        }

        // Calculate and log the time taken to fetch the user.
        long UserFetchEnd = System.currentTimeMillis();
        long executionTime = UserFetchEnd - UserFetchStart;
        logger.error("User executed in {} ms", executionTime);

        // Fetch subscribed packages for the user.
//        List<PackageDto> SubscribedPackages = packageService.GetUserSubscribedPackages(id);

        // calculate package count
        ObjectId influencerObjectId = new ObjectId(user.getId());
        long packageCount = searchPackageRepository.countByInfulancer_Id(influencerObjectId);
        influence.setPackagesCount(packageCount);

        // Calculate and log the time taken to fetch subscribed packages.
        long UserSubscribeEnd = System.currentTimeMillis();
        long SubscribeExecutionTime = UserSubscribeEnd - UserFetchEnd;
        logger.error("get Subscribe executed in {} ms", SubscribeExecutionTime);

        // Fetch posts owned by the user.
        List<PostDto> posts = postService.GetUserOwnedPosts(user.getId());

        // Calculate and log the time taken to fetch posts.
        long PostsExecutionEnd = System.currentTimeMillis();
        long PostsExecutionTime = PostsExecutionEnd - UserSubscribeEnd;
        logger.error("get Posts executed in {} ms", PostsExecutionTime);

        // Set fetched posts in the influencer DTO.
        influence.setPosts(posts);

        // Initialize a list to hold all media from posts and packages.
        List<MediaWrapperDto> AllMedia = new ArrayList<>();

        // Process posts to separate them into posts and stories, and gather their media.
        if (posts != null) {
            influence.setPostsOnly(posts.stream().filter(z -> z.getPostType() == PostType.Post).collect(Collectors.toList()));
            influence.setStories(posts.stream().filter(z -> z.getPostType() == PostType.Story).collect(Collectors.toList()));

            List<MediaWrapperDto> postMedia = posts.stream()
                    .filter(z -> z != null && z.getMedia() != null)
                    .map(PostDto::getMedia)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());

            postMedia = new ArrayList<>(new HashSet<>(postMedia)); // Remove duplicates.
            AllMedia.addAll(postMedia);
        }

        influence.setMedias(new ArrayList<>(new HashSet<>(AllMedia))); // Set all unique media in influencer DTO.
//        influence.setPackages(packages);

        // Log the time taken for processing other operations.
        long OtherOperationExecutionEnd = System.currentTimeMillis();
        long OtherOperationExecutionTime = OtherOperationExecutionEnd - PostsExecutionEnd;
        logger.error("Operation executed in {} ms", OtherOperationExecutionTime);


        // Log the time taken to match followers.
        long followerEnd = System.currentTimeMillis();
        long followerExecutionTime = followerEnd - OtherOperationExecutionEnd;
        logger.error("Match followers executed in {} ms", followerExecutionTime);

        // Log the final execution time.
        long done = System.currentTimeMillis();
        long ExecutionTime = done - followerEnd;
        logger.error("Execution Time  in {} ms", ExecutionTime);

        // Set followers of the influencer in the DTO.
        influence.setInfluencers(getFollowers(user.getId()));

                LiveStream liveStream = liveStreamRepository.findByInfulancerIdAndStatus(user.getId(), LiveStreamStatus.LIVE);
        if(liveStream != null)
        {
            influence.setHasLiveStream(true);
            influence.setLiveStreamId(liveStream.getId());
            influence.setLiveStreamPlaybackUrl(liveStream.getPlaybackUrl());
        }
        return influence;
    }

    @Override
    public fcmToken AddFcmToken(fcmToken fcm) throws FirebaseAuthException {
        if (userSessionData == null)
            throw new CustomException(500, " User session data is null! ");

        String id = userSessionData.getId();
        final Optional<User> byId = userRepository.findById(id);

        if (byId.isEmpty())
            throw new CustomException(404, " User Not Found ");

        // Remove the FCM token from all other users first
        removeTokenFromOtherUsers(fcm.getToken(), id);

        User user = byId.get();
        List<String> fcmTokens = user.getFcmTokens() == null ? new ArrayList<>() : user.getFcmTokens();
        if (!fcmTokens.contains(fcm.getToken()))
            fcmTokens.add(fcm.getToken());
        user.setFcmTokens(fcmTokens);
        userRepository.save(user);
          //      Map<String, Object> claims = new HashMap<>();
          //    claims.put("fcmTokens", user.getFcmTokens());
         //  FirebaseAuth.getInstance().setCustomUserClaims(user.getFirebaseId(), claims);

        cashService.storeUserFCMToken(id, fcm.getToken());

        return fcm;
    }


    @Override
    public void deleteAllFcmTokens(String userId) throws FirebaseAuthException {
        if (userId == null || userId.isEmpty()) {
            userId = userSessionData.getId();
        }

        final Optional<User> byId = userRepository.findById(userId);

        if (byId.isEmpty())
            throw new CustomException(500, "User Not Found");

        User user = byId.get();

        // Clear all FCM tokens
        user.setFcmTokens(new ArrayList<>());
        userRepository.save(user);

//        // Update Firebase custom claims
//        Map<String, Object> claims = new HashMap<>();
//        claims.put("fcmTokens", user.getFcmTokens());
//        FirebaseAuth.getInstance().setCustomUserClaims(user.getFirebaseId(), claims);

        cashService.deleteUserFCMToken(userId);
    }

    @Override
    public String getUserFCMToken(String userId) {
        if (userId == null)
            userId = userSessionData.getId();

        final Optional<User> byId = userRepository.findById(userId);

        if (byId.isEmpty())
            throw new CustomException(500, "User Not Found");

        String token = cashService.getUserFcmToken(userId);

        if (token == null)
            if (!byId.get().getFcmTokens().isEmpty())
                token = byId.get().getFcmTokens().getFirst();

        return token;
    }

    /**
     * Removes the specified FCM token from all users except the target user
     *
     * @param fcmToken     The FCM token to remove
     * @param targetUserId The user ID that should keep the token
     */
    private void removeTokenFromOtherUsers(String fcmToken, String targetUserId) throws FirebaseAuthException {
        List<User> usersWithToken = userRepository.findByFcmTokensContaining(fcmToken);

        for (User user : usersWithToken) {
            if (!user.getId().equals(targetUserId)) {
                List<String> fcmTokens = user.getFcmTokens();
                if (fcmTokens != null && fcmTokens.contains(fcmToken)) {
                    fcmTokens.remove(fcmToken);
                    user.setFcmTokens(fcmTokens);
                    userRepository.save(user);



                    // Remove from cache as well
                    cashService.deleteUserFCMToken(user.getId());
                }
            }
        }
    }


    @Override
    public com.hb.crm.core.dtos.searchUserResultDto getFollowing(String userid, int pageNumber, int itemsPerPage, String search) {
        if (userid == null || userid.isEmpty()) {
            userid = userSessionData.getId();
        }

        // Get followed influencers
        var followedInfluencers = reactionSearchRepository.findByEntityNameAndEntityTypeAndUserId(
                EntityName.User,
                EntityType.Follow,
                userid
        );

        var followedInfluencersObjectIds = followedInfluencers
                .stream()
                .map(ReactionSearch::getEntityId)
                .filter(Objects::nonNull)
                .map(ObjectId::new)
                .toList();

        // Return empty result if no following
        if (followedInfluencersObjectIds.isEmpty()) {
            return new com.hb.crm.core.dtos.searchUserResultDto(0L, new ArrayList<>());
        }

        // Handle single result case
        if (followedInfluencersObjectIds.size() == 1) {
            var result = search == null || search.isEmpty()
                    ? searchUserRepository.findByIdIn(
                    followedInfluencersObjectIds,
                    0,
                    1)
                    : searchUserRepository.findByIdInAndNameContainingIgnoreCase(
                    search,
                    followedInfluencersObjectIds,
                    0,
                    1);

            // Ensure we always return a valid result
            if (result == null) {
                return new com.hb.crm.core.dtos.searchUserResultDto(0L, new ArrayList<>());
            }
            return result;
        }

        // Handle multiple results case
        final int offset = pageNumber * itemsPerPage;
        return search == null || search.isEmpty()
                ? searchUserRepository.findByIdIn(
                followedInfluencersObjectIds,
                offset,
                itemsPerPage)
                : searchUserRepository.findByIdInAndNameContainingIgnoreCase(
                search,
                followedInfluencersObjectIds,
                offset,
                itemsPerPage);
    }

    @Override
    public PageDto<MediaWithUserDto> getUserSavedReels(String userId, int pageNumber, int itemsPerPage) {
        if (userId == null)
            userId = userSessionData.getId();

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new CustomException(404, "User not found"));

        PageDto<MediaWithUserDto> pageDto = new PageDto<>();
        final Pageable pageable = PageRequest.of(pageNumber, itemsPerPage);

        var savedReels = mediaRepository.findByIdIn(user.getSavedReelsIds(), pageable);
        List<MediaWithUserDto> medias = savedReels.getContent().stream()
                .filter(Objects::nonNull)
                .map(item -> modelMapper.map(item, MediaWithUserDto.class)).collect(Collectors.toList());

        long totalCount = savedReels.getTotalElements();

        pageDto.setItems(medias);
        pageDto.setTotalNoOfItems(totalCount);

        return pageDto;
    }


    @Override
    public PageDto<SimpleUserinfoDto> getFollowers(String userid, int pageNumber, int itemsPerPage, String search) {
        if (userid == null || userid.isEmpty())
            userid = userSessionData.getId();
        PageDto<SimpleUserinfoDto> pageDto = new PageDto<>();
        final Pageable pageable = PageRequest.of(pageNumber, itemsPerPage);

        Criteria criteria = Criteria.where("_id.user._id").is(new ObjectId(userid));

        Aggregation aggregation = queryNormalizeService.getFollowsFields(criteria, pageable, null);
        FollowsResult allFollows = mongoTemplate.aggregate(aggregation, "follows", FollowsResult.class).getMappedResults().getFirst();
        if (allFollows == null)
            return null;
        List<SimpleUserinfoDto> users = allFollows.getFilteredResults().stream()
                .map(Follows::getId)
                .map(FollowsKey::getFollower)
                .filter(Objects::nonNull)
                .map(this::convertToSimpleDto).collect(Collectors.toList());


        long totalCount = allFollows.getTotalCount();

        pageDto.setItems(users);
        pageDto.setTotalNoOfItems(totalCount);

        return pageDto;
    }

    @Override
    public void changeCoverPhoto(String Photo) {
        if (userSessionData.getId() != null) {
            String id = userSessionData.getId();
            final Optional<User> byId = userRepository.findById(id);
            if (byId.isPresent()) {
                User user = byId.get();
                user.setCoverImage(Photo);
                userRepository.save(user);
                simpleUserInfo simpleUserInfo = new simpleUserInfo();
                simpleUserInfo.setFirstName(user.getFirstName());
                simpleUserInfo.setUsername(user.getUsername());
                simpleUserInfo.setLastName(user.getLastName());
                simpleUserInfo.setCoverImage(user.getCoverImage());
                simpleUserInfo.setProfileImage(user.getProfileImage());
                searchUserRepository.save(new SearchUser(user));
                updateUserUtil.updateSimpleUserInfoInSearchPosts(user.getId(),simpleUserInfo);
            }
        }
    }

    @Override
    public void changeProfileImage(String image) {
        if (userSessionData.getId() != null) {
            String id = userSessionData.getId();
            final Optional<User> byId = userRepository.findById(id);
            if (byId.isPresent()) {
                User user = byId.get();
                user.setProfileImage(image);
                userRepository.save(user);
                simpleUserInfo simpleUserInfo = new simpleUserInfo();
                simpleUserInfo.setFirstName(user.getFirstName());
                simpleUserInfo.setUsername(user.getUsername());
                simpleUserInfo.setLastName(user.getLastName());
                simpleUserInfo.setCoverImage(user.getCoverImage());
                simpleUserInfo.setProfileImage(user.getProfileImage());
                searchUserRepository.save(new SearchUser(user));
                updateUserUtil.updateSimpleUserInfoInSearchPosts(user.getId(),simpleUserInfo);
            } else {
                throw new CustomException(401, "User not found");
            }
        }
    }


    public void resendVerificationCode(String email, String mobile) {
        User user;
        if (email != null && !email.isEmpty()) {
            user = getUserByEmail(email);
            if (user == null) {
                throw new CustomException(404, "There is no user with this Email : " + email);
            }
            if (!user.isEmailActivated()) {
                String emailCode = GenerateRandomCode();
                user.setEmailCode(emailCode);
                emailService.sendVerificationEmail(email, emailCode);
            }

        } else if (mobile != null && !mobile.isEmpty()) {
            user = getUserByMobile(mobile);
            if (user == null) {
                throw new CustomException(404, "There is no user with this mobile : " + mobile);
            }
            if (!user.isPhoneActiviated()) {
                String mobileCode = GenerateRandomCode();
                user.setPhoneCode(mobileCode);
                twilioService.sendMessage(mobile, mobileCode);
            }
        } else {
            throw new CustomException(422, "invalid inputs");

        }
        userRepository.save(user);

    }


    @Override
    public String delete() throws FirebaseAuthException {
        String id = userSessionData.getId();
        final Aggregation ReplyAggregation = queryNormalizeService.getReplayReacts(Criteria.where("user._id").is(new ObjectId(id)));
        List<ReplyReaction> Reactions = mongoTemplate.aggregate(ReplyAggregation, "replyReaction", ReplyReaction.class).getMappedResults().stream().toList();
        final Aggregation CommentAggregation = queryNormalizeService.getCommentReacts(Criteria.where("user._id").is(new ObjectId(id)));
        List<CommentReaction> CommentReactionList = mongoTemplate.aggregate(CommentAggregation, "commentReaction", CommentReaction.class).getMappedResults().stream().toList();
        final Aggregation PackageAggregation = queryNormalizeService.getPackageReacts(Criteria.where("_id.user._id").is(new ObjectId(id)));
        List<PackageReaction> PackageReactions = mongoTemplate.aggregate(PackageAggregation, "packageReaction", PackageReaction.class).getMappedResults().stream().toList();
        final Aggregation PostAggregation = queryNormalizeService.getPostReactionFields(Criteria.where("_id.user._id").is(new ObjectId(id)));
        List<PostReaction> PostReactions = mongoTemplate.aggregate(PostAggregation, "postReaction", PostReaction.class).getMappedResults().stream().toList();
        final Aggregation CommentsAggregation = queryNormalizeService.getPostComments(Criteria.where("user._id").is(new ObjectId(id)), null);
        List<Comment> Comments = mongoTemplate.aggregate(CommentsAggregation, "comment", Comment.class).getMappedResults().stream().toList();
        final Aggregation replyAggregation = queryNormalizeService.getPostComments(Criteria.where("user._id").is(new ObjectId(id)), null);
        List<Reply> replies = mongoTemplate.aggregate(replyAggregation, "reply", Reply.class).getMappedResults().stream().toList();
        replyReactionRepository.deleteAllById(Reactions.stream().map(ReplyReaction::getId).collect(Collectors.toList()));
        commentReactionRepository.deleteAllById(CommentReactionList.stream().map(CommentReaction::getId).collect(Collectors.toList()));
        packageReactionRepository.deleteAllById(PackageReactions.stream().map(PackageReaction::getId).collect(Collectors.toList()));
        replyRepository.deleteAllById(replies.stream().map(Reply::getId).collect(Collectors.toList()));
        commentsRepository.deleteAllById(Comments.stream().map(Comment::getId).collect(Collectors.toList()));
        postReactionRepository.deleteAllById(PostReactions.stream().map(PostReaction::getId).collect(Collectors.toList()));

        userRepository.findById(id).ifPresent(userRepository::delete);

        // Delete from cache
        cashService.deleteData(id);

        // Delete from search
        searchUserRepository.deleteById(id);

        return id;
    }

    private UserInfoDto convertToDto(User user) {
        return modelMapper.map(user, UserInfoDto.class);
    }

    private SearchUser convertToSearchUser(User user) {
        return new SearchUser(user);
    }

    private SimpleUserinfoDto convertToSimpleDto(User user) {
        return modelMapper.map(user, SimpleUserinfoDto.class);
    }

    public User convertToEntity(UserRegistrationDto user) {
        return modelMapper.map(user, User.class);
    }


    public Place convertToEntity(PlaceDto place) {
        return modelMapper.map(place, Place.class);
    }

    @Override
    public User getUserByUserName(String username) {
        return this.userRepository.findByUsername(username).orElse(null);
    }

    @Override
    public UserDto getUserDataByUserName(String username) {
        User user = this.userRepository.findByUsername(username).orElseThrow(() -> new IllegalArgumentException("User with username does not exist"));
        UserDto dto = modelMapper.map(user, UserDto.class);
        
        LiveStream liveStream = liveStreamRepository.findByInfulancerIdAndStatus(user.getId(), LiveStreamStatus.LIVE);
        if (liveStream != null) {
            dto.setHasLiveStream(true);
            dto.setLiveStreamId(liveStream.getId());
            dto.setLiveStreamPlaybackUrl(liveStream.getPlaybackUrl());
        }
        
        return dto;
    }

    @Override
    public User getUserByEmail(String email) {
        Aggregation query = queryNormalizeService.getUserFields(Criteria.where("userInfo.email").is(email));
        User user = mongoTemplate.aggregate(query, "user", User.class).getMappedResults().stream().findAny().orElse(null);
        // in case the email value was firebaseID
        if (user == null) {
            user = this.userRepository.findByUsername(email).orElse(null);
        }
        return user;
    }


    @Override
    public User getUserByMobile(String mobile) {
        Aggregation query = queryNormalizeService.getUserFields(Criteria.where("userInfo.mobile").is(mobile));
        User user = mongoTemplate.aggregate(query, "user", User.class).getMappedResults().stream().findAny().orElse(null);
        // in case the email value was firebaseID

        if (user == null) {
            user = this.userRepository.findByUsername(mobile).orElse(null);
        }
        return user;
    }


    @Override
    public CustomUserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        List<SimpleGrantedAuthority> roles;
        User user = this.userRepository.findByUsername(username).orElse(null);
        if (user != null && user.getUsertype() != null) {
            String userType = user.getUsertype().name();
            roles = new ArrayList<>();
            roles.add(new SimpleGrantedAuthority("ROLE_" + userType));
            return new CustomUser(user.getUsername(), user.getPassword(), roles, user.getId());
        }
        throw new UsernameNotFoundException("User not found with the name " + username);
    }

    @Override
    public com.hb.crm.core.dtos.searchUserResultDto searchUsers(String query, int page, int size) {
        if (StringUtils.isEmpty(query)) {
            // Fetch only Traveler and Influencer users
            Page<SearchUser> users = searchUserRepository.findByUsertypeIn(
                    Arrays.asList(UserType.Traveler, UserType.Influencer),
                    PageRequest.of(page, size)
            );
            return new com.hb.crm.core.dtos.searchUserResultDto(
                    users.getTotalElements(),
                    new ArrayList<>(users.getContent())
            );
        }


        // Execute aggregation
        Aggregation aggregation = queryNormalizeService.searchByNameOrPackage(query, page, size);
        return mongoTemplate2.aggregate(
                aggregation, "searchUser", com.hb.crm.core.dtos.searchUserResultDto.class).getUniqueMappedResult();
    }

    @Override
    public com.hb.crm.core.dtos.PageDto<UserInfoDto> getAllUsersExceptGuest(String query, int page, int size) {
        com.hb.crm.core.dtos.PageDto<UserInfoDto> pageDto = new com.hb.crm.core.dtos.PageDto<>();
        final Pageable pageable = PageRequest.of(page, size);

        // Create base criteria to exclude guest users
        Criteria baseCriteria = Criteria.where("usertype").ne(UserType.Guest);

        Query searchQuery = new Query(baseCriteria);
        Query countQuery = new Query(baseCriteria);

        // Add fuzzy search if query is provided
        if (query != null && !query.trim().isEmpty()) {
            String searchTerm = query.trim();
            // Create fuzzy search criteria across multiple fields
            Criteria searchCriteria = new Criteria().orOperator(
                    Criteria.where("firstName").regex(searchTerm, "i"),
                    Criteria.where("lastName").regex(searchTerm, "i"),
                    Criteria.where("username").regex(searchTerm, "i"),
                    Criteria.where("userInfo.email").regex(searchTerm, "i"),
                    Criteria.where("city").regex(searchTerm, "i"),
                    Criteria.where("country").regex(searchTerm, "i")
            );

            searchQuery.addCriteria(searchCriteria);
            countQuery.addCriteria(searchCriteria);
        }

        // Add sorting and pagination
        searchQuery.with(Sort.by(Sort.Direction.DESC, "creationDate"));
        searchQuery.with(pageable);

        // Execute queries
        List<UserInfoDto> users = mongoTemplate.find(searchQuery, User.class).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
        long count = mongoTemplate.count(countQuery, User.class);

        // Set pagination info
        pageDto.setTotalNoOfItems(count);
        pageDto.setItems(users);
        pageDto.setItemsPerPage(size);
        pageDto.setPageNumber(page);

        return pageDto;
    }

}
