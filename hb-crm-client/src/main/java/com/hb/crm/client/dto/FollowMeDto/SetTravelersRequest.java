package com.hb.crm.client.dto.FollowMeDto;

import com.hb.crm.core.beans.Travelers.Traveler;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.ArrayList;

/**
 * Request DTO for setting travelers in FollowMe subscription
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SetTravelersRequest {
    
    @JsonProperty("packageId")
    private String packageId;
    
    @JsonProperty("travelers")
    private ArrayList<Traveler> travelers;
}
