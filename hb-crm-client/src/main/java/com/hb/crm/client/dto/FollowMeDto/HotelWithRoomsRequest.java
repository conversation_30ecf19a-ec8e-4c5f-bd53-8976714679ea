package com.hb.crm.client.dto.FollowMeDto;

import java.util.List;
import com.hb.crm.core.beans.Hotel.Hotel;

/**
 * Request DTO for adding hotel with selected rooms to FollowMe subscription
 * This combines hotel availability data with room selections for booking
 */
public class HotelWithRoomsRequest {
    
    private String packageId;
    private Hotel hotelData;
    private List<String> selectedRoomIds;
    private List<String> selectedOptionIds;

    public HotelWithRoomsRequest() {}

    public HotelWithRoomsRequest(String packageId, Hotel hotelData, List<String> selectedRoomIds, List<String> selectedOptionIds) {
        this.packageId = packageId;
        this.hotelData = hotelData;
        this.selectedRoomIds = selectedRoomIds;
        this.selectedOptionIds = selectedOptionIds;
    }

    public String getPackageId() {
        return packageId;
    }

    public void setPackageId(String packageId) {
        this.packageId = packageId;
    }

    public Hotel getHotelData() {
        return hotelData;
    }

    public void setHotelData(Hotel hotelData) {
        this.hotelData = hotelData;
    }

    public List<String> getSelectedRoomIds() {
        return selectedRoomIds;
    }

    public void setSelectedRoomIds(List<String> selectedRoomIds) {
        this.selectedRoomIds = selectedRoomIds;
    }

    public List<String> getSelectedOptionIds() {
        return selectedOptionIds;
    }

    public void setSelectedOptionIds(List<String> selectedOptionIds) {
        this.selectedOptionIds = selectedOptionIds;
    }
}
