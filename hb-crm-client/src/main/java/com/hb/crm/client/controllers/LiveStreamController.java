package com.hb.crm.client.controllers;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hb.crm.client.CommonService.Mapper;
import com.hb.crm.client.beans.UserSessionData;
import com.hb.crm.core.services.LiveStreamSessionManager;
import com.hb.crm.core.dtos.PageDto;
import com.hb.crm.core.dtos.LiveStream.LiveStreamCreateRequestDto;
import com.hb.crm.core.dtos.LiveStream.LiveStreamDto;
import com.hb.crm.core.dtos.LiveStream.LiveStreamDownloadDto;
import com.hb.crm.core.dtos.LiveStream.LiveStreamReactionDto;
import com.hb.crm.core.dtos.LiveStream.LiveStreamReactionRequestDto;
import com.hb.crm.core.dtos.LiveStream.LiveStreamResponseDto;
import com.hb.crm.core.dtos.LiveStream.LiveStreamStopRequestDto;
import com.hb.crm.core.dtos.LiveStream.LiveStreamForceStopRequestDto;
import com.hb.crm.core.dtos.LiveStream.ReactionCountUpdateDto;
import com.hb.crm.core.dtos.LiveStream.ViewerCountUpdateDto;
import com.hb.crm.core.dtos.LiveStream.LiveStreamCommentDto;
import com.hb.crm.core.dtos.LiveStream.LiveStreamCommentRequestDto;
import com.hb.crm.core.dtos.LiveStream.CommentCountUpdateDto;
import com.hb.crm.core.beans.LiveStream.LiveStreamReaction;
import com.hb.crm.core.beans.LiveStream.LiveStreamComment;
import com.hb.crm.core.repositories.PostRepository;
import com.hb.crm.core.repositories.UserRepository;
import com.hb.crm.core.repositories.LiveStream.LiveStreamRepository;
import com.hb.crm.core.repositories.EmployeeRepository;
import com.hb.crm.core.services.interfaces.IVSStreamService;
import com.hb.crm.core.services.interfaces.LiveStreamService;
import com.hb.crm.client.config.authentication.JwtUtil;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.security.SecurityException;
import io.jsonwebtoken.UnsupportedJwtException;

import io.jsonwebtoken.security.Keys;
import io.jsonwebtoken.Jwts;
import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import org.springframework.beans.factory.annotation.Value;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

import org.springframework.http.HttpStatus;

import com.hb.crm.core.Enums.LiveStreamStatus;
import com.hb.crm.core.Enums.UserType;
import com.hb.crm.core.beans.User;
import com.hb.crm.core.beans.Employee;
import com.hb.crm.core.beans.LiveStream.LiveStream;

@RestController
@RequestMapping(value="/v1/live-stream")
public class LiveStreamController 
{
    private final LiveStreamService liveStreamService;
    private final PostRepository _PostRepository;
    private final UserSessionData userSessionData;
    private final UserRepository userRepository;
    private final Mapper mapper;
    private final LiveStreamRepository liveStreamRepository;
    private final IVSStreamService ivsStreamService;
    private final LiveStreamSessionManager sessionManager;
    private final EmployeeRepository employeeRepository;
    private final JwtUtil jwtUtil;
    
    @Value("${adminjwt.secret}")
    private String adminJwtSecret;
    
    public LiveStreamController(PostRepository _PostRepository,LiveStreamService liveStreamService,UserSessionData userSessionData, UserRepository userRepository,Mapper mapper,LiveStreamRepository liveStreamRepository,IVSStreamService ivsStreamService, LiveStreamSessionManager sessionManager, EmployeeRepository employeeRepository, JwtUtil jwtUtil) 
    {
        this._PostRepository = _PostRepository;
        this.liveStreamService = liveStreamService;
        this.userSessionData = userSessionData;
        this.userRepository = userRepository;
        this.mapper = mapper;
        this.liveStreamRepository = liveStreamRepository;
        this.ivsStreamService=ivsStreamService;
        this.sessionManager = sessionManager;
        this.employeeRepository = employeeRepository;
        this.jwtUtil = jwtUtil;
    }
    
    @PostMapping("/start")
    @Operation(summary = "Start a live stream", 
            description = "Start a new live stream for the authenticated user. After starting, the streamer should connect to WebSocket using /app/live-stream/streamer-join to receive real-time updates.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Live stream started successfully. Streamer should now join WebSocket."),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "403", description = "Live Stream not allowed"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<LiveStreamResponseDto> startLiveStream(@RequestBody LiveStreamCreateRequestDto request) {
        try 
        {
            String userId = userSessionData.getId();
            
            // If findById returns Optional<User>
            Optional<User> optionalUser = userRepository.findById(userId);
            if (!optionalUser.isPresent()) {
                return ResponseEntity.notFound().build();
            }
            
            User user = optionalUser.get();
            if(user.getUsertype()!=UserType.Influencer)
            {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }
            LiveStream  liveStream = liveStreamService.startLiveStream(request, user);
            LiveStreamResponseDto  response = mapper.convertLiveStreamResponseDto(liveStream);
            
            return ResponseEntity.ok(response);
            
        } 
        catch (IllegalArgumentException e) 
        {
            return ResponseEntity.badRequest().build();
        } 
        catch (Exception e) 
        {
            return ResponseEntity.internalServerError().build();
        }
    }
    
    
    @PostMapping("/stopLiveStream")
    @Operation(summary = "Stop a live stream", 
            description = "Stop an active live stream for the authenticated influencer")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Live stream stopped successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data or stream cannot be stopped"),
        @ApiResponse(responseCode = "401", description = "User not authenticated"),
        @ApiResponse(responseCode = "403", description = "User not authorized to stop live streams or not owner of the stream"),
        @ApiResponse(responseCode = "404", description = "User or live stream not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<LiveStreamResponseDto> stopLiveStream(@RequestBody LiveStreamStopRequestDto request) 
    {
        try 
        {
            // Validate request
            if (request == null || request.getStreamId() == null || request.getStreamId().trim().isEmpty()) 
            {
                return ResponseEntity.badRequest().build();
            }

            String userId = userSessionData.getId();
            if (userId == null || userId.trim().isEmpty())
            {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
            }

            // Find and validate user
            Optional<User> optionalUser = userRepository.findById(userId);
            if (!optionalUser.isPresent()) 
            {
                return ResponseEntity.notFound().build();
            }

            User influencer = optionalUser.get();
            
            // Check if user is an influencer
            if (influencer.getUsertype() != UserType.Influencer) 
            {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            // Find and validate live stream ownership
            Optional<LiveStream> optionalStream = liveStreamRepository.findById(request.getStreamId());
            if (!optionalStream.isPresent()) 
            {
                return ResponseEntity.notFound().build();
            }

            LiveStream stream = optionalStream.get();
            
            // Check if the influencer owns this stream
            if (!stream.getInfulancer().getId().equals(influencer.getId())) 
            {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            // Stop the live stream
            LiveStream stoppedLiveStream = liveStreamService.stopLiveStream( stream);
            LiveStreamResponseDto response = mapper.convertLiveStreamResponseDto(stoppedLiveStream);
            
            // Close all WebSocket connections for this stream
            sessionManager.closeAllConnectionsForStream(request.getStreamId());
            
            return ResponseEntity.ok(response);
            
        } 
        catch (IllegalArgumentException e) 
        {
            // Handle business logic errors (e.g., stream already stopped, invalid state)
            return ResponseEntity.badRequest().build();
            
        } 
        catch (Exception e) 
        {
            // Log the error for debugging (you might want to add proper logging)
            // logger.error("Error stopping live stream: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    
    @PostMapping("/force-stop-by-admin")
    @Operation(summary = "Force stop a live stream (Employee/Admin only)", 
            description = "Administratively force stop any live stream. This endpoint requires employee authentication and bypasses ownership checks.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Live stream force stopped successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data or stream is not live"),
        @ApiResponse(responseCode = "401", description = "Employee authentication required"),
        @ApiResponse(responseCode = "403", description = "Employee authorization failed"),
        @ApiResponse(responseCode = "404", description = "Live stream not found or employee not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<LiveStreamResponseDto> forceStopLiveStream(@RequestBody LiveStreamForceStopRequestDto request) 
    {
        try 
        {
            // Validate request
            if (request == null || request.getStreamId() == null || request.getStreamId().trim().isEmpty()) 
            {
                return ResponseEntity.badRequest().build();
            }
            
            // Validate employee authorization
            if (request.getAdminId() == null || request.getAdminId().trim().isEmpty()) 
            {
                return ResponseEntity.badRequest().build();
            }
            
            if (request.getAdminToken() == null || request.getAdminToken().trim().isEmpty()) 
            {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
            }

            // Find and validate employee
            Optional<Employee> optionalEmployee = employeeRepository.findById(request.getAdminId());
            if (!optionalEmployee.isPresent()) 
            {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            Employee employee = optionalEmployee.get();
            
            // Additional employee validation (check if account is not locked)
            if (employee.isAccountLocked()) 
            {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }
            
            // Validate employee token
            if (!validateEmployeeToken(employee, request.getAdminToken())) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
            }
            
            // Find and validate live stream
            Optional<LiveStream> optionalStream = liveStreamRepository.findById(request.getStreamId());
            if (!optionalStream.isPresent()) 
            {
                return ResponseEntity.notFound().build();
            }
            
            LiveStream stream = optionalStream.get();
            if(stream.getStatus() != LiveStreamStatus.LIVE)
            {
                return ResponseEntity.badRequest().build();
            }
            // Force stop the live stream
            LiveStream stoppedLiveStream = liveStreamService.cancelLiveStream(stream);
            LiveStreamResponseDto response = mapper.convertLiveStreamResponseDto(stoppedLiveStream);
            
            // Close all WebSocket connections for this stream
            sessionManager.closeAllConnectionsForStream(request.getStreamId());
            return ResponseEntity.ok(response);
            
        } 
        catch (IllegalArgumentException e) 
        {
            return ResponseEntity.badRequest().build();
        } 
        catch (Exception e) 
        {
            return ResponseEntity.internalServerError().build();
        }
    }

  
    @GetMapping("/list")
    @Operation(summary = "Get live streams with filters", 
            description = "Retrieve live streams with optional filters for influencer and package")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved live streams"),
        @ApiResponse(responseCode = "400", description = "Invalid parameters"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<PageDto<LiveStreamResponseDto>> getLiveStreams(
            @Parameter(description = "Influencer ID filter", example = "507f1f77bcf86cd799439011")
            @RequestParam(required = false) String influencerId,
            
            @Parameter(description = "Package ID filter", example = "507f1f77bcf86cd799439012")
            @RequestParam(required = false) String packageId,
            
            // @Parameter(description = "Status of Live stream per page", example = "LIVE,COMPLETED or CANCELED")
            // @RequestParam(defaultValue = "LIVE") LiveStreamStatus status,

            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") int page,
            
            @Parameter(description = "Number of items per page", example = "10")
            @RequestParam(defaultValue = "10") int limit) 
    {
        try 
        {
            PageDto<LiveStream> liveStreams = liveStreamService.getLiveStreams(
                    influencerId, packageId, page, limit);
            
            // Convert PageDto<LiveStream> to PageDto<LiveStreamResponseDto>
            PageDto<LiveStreamResponseDto> response = new PageDto<>();
            response.setPageNumber(liveStreams.getPageNumber());
            response.setItemsPerPage(liveStreams.getItemsPerPage());
            response.setTotalNoOfItems(liveStreams.getTotalNoOfItems());
            response.setSortColumn(liveStreams.getSortColumn());
            response.setSortOrder(liveStreams.getSortOrder());
            
            // Convert each LiveStream to LiveStreamResponseDto
            List<LiveStreamResponseDto> convertedItems = liveStreams.getItems()
                    .stream()
                    .map(liveStream -> mapper.convertLiveStreamResponseDto(liveStream))
                    .collect(Collectors.toList());
            
            response.setItems(convertedItems);
            
            return ResponseEntity.ok(response);
            
        } 
        catch (IllegalArgumentException e)
        {
            return ResponseEntity.badRequest().build();
        } 
        catch (Exception e)
        {
            return ResponseEntity.internalServerError().build();
        }
    }

   
    @GetMapping("/convert-mp4/{streamId}")
    @Operation(summary = "Test MP4 conversion preparation", 
            description = "Test the MP4 conversion function - checks for existing MP4 or prepares conversion info")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Conversion info retrieved successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid stream ID or request parameters"),
        @ApiResponse(responseCode = "401", description = "User not authenticated"),
        @ApiResponse(responseCode = "403", description = "User not authorized to access this stream or not an influencer"),
        @ApiResponse(responseCode = "404", description = "User or live stream not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<Map<String, Object>> testMp4Conversion(
            @Parameter(description = "Live stream ID", example = "507f1f77bcf86cd799439011")
            @PathVariable String streamId)
    {
        try 
        {
            // Validate stream ID
            if (streamId == null || streamId.trim().isEmpty()) 
            {
                return ResponseEntity.badRequest().build();
            }

            String userId = userSessionData.getId();
            if (userId == null || userId.trim().isEmpty()) 
            {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
            }

            // Find and validate user
            Optional<User> optionalUser = userRepository.findById(userId);
            if (!optionalUser.isPresent()) 
            {
                return ResponseEntity.notFound().build();
            }

            User user = optionalUser.get();
            
            // Check if user is an influencer
            if (user.getUsertype() != UserType.Influencer) 
            {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            // Find and validate live stream
            Optional<LiveStream> optionalStream = liveStreamRepository.findById(streamId);
            if (!optionalStream.isPresent()) 
            {
                return ResponseEntity.notFound().build();
            }

            LiveStream stream = optionalStream.get();
            
            // Check if the influencer owns this stream
            if (!stream.getInfulancer().getId().equals(user.getId())) 
            {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            // Check if stream has a channel ARN (required for conversion)
            if (stream.getChannelArn() == null || stream.getChannelArn().trim().isEmpty()) 
            {
                return ResponseEntity.badRequest().build();
            }

            // Test the MP4 conversion function
            Map<String, Object> conversionResult = ivsStreamService.convertLiveStreamToMp4(stream.getChannelArn());
            
            return ResponseEntity.ok(conversionResult);
            
        } 
        catch (IllegalArgumentException e) 
        {
            return ResponseEntity.badRequest().build();
        } 
        catch (Exception e) 
        {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/check-mp4/{streamId}")
    @Operation(summary = "Check if MP4 exists for stream", 
            description = "Check if converted MP4 file exists for a live stream")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "MP4 status checked successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid stream ID"),
        @ApiResponse(responseCode = "401", description = "User not authenticated"),
        @ApiResponse(responseCode = "403", description = "User not authorized or not an influencer"),
        @ApiResponse(responseCode = "404", description = "User or live stream not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<Map<String, Object>> checkMp4Status(
            @Parameter(description = "Live stream ID", example = "507f1f77bcf86cd799439011")
            @PathVariable String streamId)
    {
        try 
        {
            // Validate stream ID
            if (streamId == null || streamId.trim().isEmpty()) 
            {
                return ResponseEntity.badRequest().build();
            }

            String userId = userSessionData.getId();
            if (userId == null || userId.trim().isEmpty()) 
            {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
            }

            // Find and validate user
            Optional<User> optionalUser = userRepository.findById(userId);
            if (!optionalUser.isPresent()) 
            {
                return ResponseEntity.notFound().build();
            }

            User user = optionalUser.get();
            
            // Check if user is an influencer
            if (user.getUsertype() != UserType.Influencer) 
            {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            // Find and validate live stream
            Optional<LiveStream> optionalStream = liveStreamRepository.findById(streamId);
            if (!optionalStream.isPresent()) 
            {
                return ResponseEntity.notFound().build();
            }

            LiveStream stream = optionalStream.get();
            
            // Check if the influencer owns this stream
            if (!stream.getInfulancer().getId().equals(user.getId())) 
            {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            // Extract channel ID from ARN for checking MP4 status
            String channelArn = stream.getChannelArn();
            if (channelArn == null || channelArn.trim().isEmpty()) 
            {
                return ResponseEntity.badRequest().build();
            }

            // Extract channel ID from ARN (same logic as in IVSStreamServiceImpl)
            String[] parts = channelArn.split("/");
            String channelId = parts[parts.length - 1];

            // Check MP4 status
            Map<String, Object> mp4Status = ivsStreamService.checkMp4Status(channelId);
            
            return ResponseEntity.ok(mp4Status);
            
        } 
        catch (IllegalArgumentException e) 
        {
            return ResponseEntity.badRequest().build();
        } 
        catch (Exception e) 
        {
            return ResponseEntity.internalServerError().build();
        }
    }
    
    @GetMapping("/viewers/{streamId}")
    @Operation(summary = "Get current viewer count for a live stream")
    public ResponseEntity<ViewerCountUpdateDto> getCurrentViewerCount(@PathVariable String streamId) {
        try {
            Optional<LiveStream> streamOpt = liveStreamRepository.findById(streamId);
            if (!streamOpt.isPresent()) {
                return ResponseEntity.notFound().build();
            }
            
            // Use session manager for accurate real-time count
            int currentViewerCount = sessionManager.getViewerCount(streamId);
            
            ViewerCountUpdateDto response = new ViewerCountUpdateDto(
                streamId, 
                currentViewerCount, 
                "CURRENT"
            );
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/conversion-status/{streamId}")
    @Operation(summary = "Check MP4 conversion status", 
            description = "Check the status of background MP4 conversion for a live stream")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Conversion status retrieved successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid stream ID"),
        @ApiResponse(responseCode = "401", description = "User not authenticated"),
        @ApiResponse(responseCode = "403", description = "User not authorized or not an influencer"),
        @ApiResponse(responseCode = "404", description = "User or stream not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<Map<String, Object>> getConversionStatus(
            @Parameter(description = "Live stream ID", example = "507f1f77bcf86cd799439011")
            @PathVariable String streamId)
    {
        try 
        {
            // Validate stream ID
            if (streamId == null || streamId.trim().isEmpty()) 
            {
                return ResponseEntity.badRequest().build();
            }

            String userId = userSessionData.getId();
            if (userId == null || userId.trim().isEmpty()) 
            {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
            }

            // Find and validate user
            Optional<User> optionalUser = userRepository.findById(userId);
            if (!optionalUser.isPresent()) 
            {
                return ResponseEntity.notFound().build();
            }

            User user = optionalUser.get();
            
            // Check if user is an influencer  
            if (user.getUsertype() != UserType.Influencer) 
            {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            // Find and validate live stream ownership
            Optional<LiveStream> optionalStream = liveStreamRepository.findById(streamId);
            if (!optionalStream.isPresent()) 
            {
                return ResponseEntity.notFound().build();
            }

            LiveStream stream = optionalStream.get();
            
            // Check if the influencer owns this stream
            if (!stream.getInfulancer().getId().equals(user.getId())) 
            {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            // Create response with conversion status
            Map<String, Object> response = new HashMap<>();
            response.put("streamId", stream.getId());
            response.put("mp4ConversionStatus", stream.getMp4ConversionStatus().toString());
            response.put("mp4Key", stream.getMp4Key());
            
            // Add appropriate message based on status
            switch (stream.getMp4ConversionStatus()) {
                case NOT_STARTED:
                    response.put("message", "MP4 conversion has not been started yet");
                    break;
                case PROCESSING:
                    response.put("message", "MP4 conversion is in progress");
                    break;
                case COMPLETED:
                    response.put("message", "MP4 conversion completed successfully");
                    if (stream.getMp4Key() != null) {
                        // Generate download URL using existing method
                        try {
                            java.net.URL downloadUrl = ivsStreamService.grantAccessUrl(stream.getMp4Key());
                            response.put("downloadUrl", downloadUrl.toString());
                        } catch (Exception e) {
                            response.put("downloadUrlError", "Failed to generate download URL");
                        }
                    }
                    break;
                case FAILED:
                    response.put("message", "MP4 conversion failed");
                    break;
            }
            
            return ResponseEntity.ok(response);
            
        } 
        catch (IllegalArgumentException e) 
        {
            return ResponseEntity.badRequest().build();
        } 
        catch (Exception e) 
        {
            return ResponseEntity.internalServerError().build();
        }
    }
    
    @PostMapping("/react/{streamId}")
    @Operation(summary = "React to a live stream", 
            description = "Add a reaction to a live stream")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Reaction added successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "401", description = "User not authenticated"),
        @ApiResponse(responseCode = "404", description = "Live stream not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<LiveStreamReactionDto> reactToLiveStream(
            @PathVariable String streamId,
            @RequestBody LiveStreamReactionRequestDto request) 
        {
        try {
            String userId = userSessionData.getId();
            if (userId == null || userId.trim().isEmpty()) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
            }

            Optional<User> optionalUser = userRepository.findById(userId);
            if (!optionalUser.isPresent()) {
                return ResponseEntity.notFound().build();
            }

            User user = optionalUser.get();
            LiveStreamReaction reaction = liveStreamService.reactToLiveStream(streamId, user, request.getReactionType());
            
            LiveStreamReactionDto response = new LiveStreamReactionDto(
                reaction.getId(),
                user.getId(),
                user.getUsername(),
                streamId,
                reaction.getReactionType(),
                reaction.getCreateDate(),
                reaction.getUpdateDate()
            );
            
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }
    
    @DeleteMapping("/react/{streamId}")
    @Operation(summary = "Remove reaction from a live stream", 
            description = "Remove user's reaction from a live stream")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Reaction removed successfully"),
        @ApiResponse(responseCode = "401", description = "User not authenticated"),
        @ApiResponse(responseCode = "404", description = "Live stream or reaction not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<Void> removeReactionFromLiveStream(@PathVariable String streamId) {
        try {
            String userId = userSessionData.getId();
            if (userId == null || userId.trim().isEmpty()) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
            }

            Optional<User> optionalUser = userRepository.findById(userId);
            if (!optionalUser.isPresent()) {
                return ResponseEntity.notFound().build();
            }

            User user = optionalUser.get();
            liveStreamService.removeReactionFromLiveStream(streamId, user);
            
            return ResponseEntity.ok().build();
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }
    
    @GetMapping("/reactions/{streamId}")
    @Operation(summary = "Get reactions for a live stream", 
            description = "Get paginated reactions for a live stream")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Reactions retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Live stream not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<PageDto<LiveStreamReactionDto>> getLiveStreamReactions(
            @PathVariable String streamId,
            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Number of items per page", example = "10")
            @RequestParam(defaultValue = "10") int limit) 
    {
        try 
        {
            Optional<LiveStream> streamOpt = liveStreamRepository.findById(streamId);
            if (!streamOpt.isPresent()) 
            {
                return ResponseEntity.notFound().build();
            }
            
            PageDto<LiveStreamReactionDto> reactions = liveStreamService.getLiveStreamReactions(streamId, page, limit);
            return ResponseEntity.ok(reactions);
        } 
        catch (Exception e) 
        {
            return ResponseEntity.internalServerError().build();
        }
    }
    
    @GetMapping("/reactions/count/{streamId}")
    @Operation(summary = "Get current reaction count for a live stream")
    @ApiResponses(value = 
    {
        @ApiResponse(responseCode = "200", description = "Reaction count retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Live stream not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ReactionCountUpdateDto> getCurrentReactionCount(@PathVariable String streamId) 
    {
        try 
        {
            Optional<LiveStream> streamOpt = liveStreamRepository.findById(streamId);
            if (!streamOpt.isPresent()) 
            {
                return ResponseEntity.notFound().build();
            }
            
            LiveStream stream = streamOpt.get();
            ReactionCountUpdateDto response = new ReactionCountUpdateDto(
                streamId,
                stream.getNumberOfReactions(),
                null, // No specific reaction type for current state
                "CURRENT",
                null, // No specific user for current state
                null  // No specific username for current state
            );
            
            return ResponseEntity.ok(response);
        } 
        catch (Exception e) 
        {
            return ResponseEntity.internalServerError().build();
        }
    }
    
    @PostMapping("/comment/{streamId}")
    @Operation(summary = "Add a comment to a live stream", 
            description = "Add a comment to a live stream")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Comment added successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "401", description = "User not authenticated"),
        @ApiResponse(responseCode = "404", description = "Live stream not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<LiveStreamCommentDto> addCommentToLiveStream(
            @PathVariable String streamId,
            @RequestBody LiveStreamCommentRequestDto request) {
        try {
            String userId = userSessionData.getId();
            if (userId == null || userId.trim().isEmpty()) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
            }

            Optional<User> optionalUser = userRepository.findById(userId);
            if (!optionalUser.isPresent()) {
                return ResponseEntity.notFound().build();
            }

            User user = optionalUser.get();
            LiveStreamComment comment = liveStreamService.addCommentToLiveStream(streamId, user, request.getComment());
            
            LiveStreamCommentDto response = new LiveStreamCommentDto(
                comment.getId(),
                comment.getComment(),
                user.getId(),
                user.getUsername(),
                streamId,
                comment.getCreatedDate(),
                comment.getUpdatedDate(),
                user.getProfileImage(),
                user.getFirstName(),
                user.getLastName()
            );
            
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }
    
    @DeleteMapping("/comment/{commentId}")
    @Operation(summary = "Remove a comment from a live stream", 
            description = "Remove user's comment from a live stream")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Comment removed successfully"),
        @ApiResponse(responseCode = "401", description = "User not authenticated"),
        @ApiResponse(responseCode = "403", description = "User can only delete their own comments"),
        @ApiResponse(responseCode = "404", description = "Comment not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<Void> removeCommentFromLiveStream(@PathVariable String commentId) {
        try {
            String userId = userSessionData.getId();
            if (userId == null || userId.trim().isEmpty()) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
            }

            Optional<User> optionalUser = userRepository.findById(userId);
            if (!optionalUser.isPresent()) {
                return ResponseEntity.notFound().build();
            }

            User user = optionalUser.get();
            liveStreamService.removeCommentFromLiveStream(commentId, user);
            
            return ResponseEntity.ok().build();
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }
    
    @GetMapping("/comments/{streamId}")
    @Operation(summary = "Get paginated comments for a live stream",
            description = "Get paginated comments for a live stream")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Comments retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Live stream not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<PageDto<LiveStreamCommentDto>> getLiveStreamComments(
            @PathVariable String streamId,
            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Number of items per page", example = "10")
            @RequestParam(defaultValue = "10") int limit) {
        try {
            Optional<LiveStream> streamOpt = liveStreamRepository.findById(streamId);
            if (!streamOpt.isPresent()) {
                return ResponseEntity.notFound().build();
            }
            
            PageDto<LiveStreamCommentDto> comments = liveStreamService.getLiveStreamComments(streamId, page, limit);
            return ResponseEntity.ok(comments);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }
    
    @GetMapping("/comments/count/{streamId}")
    @Operation(summary = "Get current comment count for a live stream",
            description = "Get the current comment count for a live stream")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Comment count retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Live stream not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<CommentCountUpdateDto> getCurrentCommentCount(@PathVariable String streamId) {
        try {
            Optional<LiveStream> streamOpt = liveStreamRepository.findById(streamId);
            if (!streamOpt.isPresent()) {
                return ResponseEntity.notFound().build();
            }
            
            LiveStream stream = streamOpt.get();
            CommentCountUpdateDto response = new CommentCountUpdateDto(
                streamId,
                stream.getNumberOfComments(),
                "CURRENT",
                null, // No specific user for current state
                null  // No specific username for current state
            );
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }
    
        @GetMapping("/current")
    @Operation(summary = "Get current live stream for influencer", 
            description = "Retrieve the current/last live stream that is currently LIVE for the authenticated influencer")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Current live stream retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "User not authenticated"),
        @ApiResponse(responseCode = "403", description = "User is not an influencer"),
        @ApiResponse(responseCode = "404", description = "No current live stream found or user not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity getCurrentLiveStream() 
    {
        try 
        {
            String userId = userSessionData.getId();
            if (userId == null || userId.trim().isEmpty()) 
            {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
            }

            // Find and validate user
            Optional<User> optionalUser = userRepository.findById(userId);
            if (!optionalUser.isPresent()) 
            {
                return ResponseEntity.notFound().build();
            }

            User user = optionalUser.get();
            
            // Check if user is an influencer
            if (user.getUsertype() != UserType.Influencer) 
            {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            // Find current live stream for this influencer
            LiveStream currentLiveStream = liveStreamRepository.findByInfulancerIdAndStatus(
                user.getId(), 
                com.hb.crm.core.Enums.LiveStreamStatus.LIVE
            );
            
            Map<String, Object> response = new HashMap<>();
            
            if (currentLiveStream!=null) 
            {
                response.put("hasActiveLiveStream", true);
                response.put("streamId", currentLiveStream.getId());
               
                response.put("message", "Influencer has an active live stream");
            } 
            else 
            {
                response.put("hasActiveLiveStream", false);
                response.put("streamId", null);
                response.put("message", "Influencer does not have any currently active live stream");
            }
            
            return ResponseEntity.ok(response);
        } 
        catch (Exception e) 
        {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/{streamId}")
    @Operation(summary = "Get live stream by ID", 
            description = "Retrieve detailed information about a specific live stream by its ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Live stream retrieved successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid stream ID"),
        @ApiResponse(responseCode = "404", description = "Live stream not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<LiveStreamDto> getLiveStreamById(
            @Parameter(description = "Live stream ID", example = "507f1f77bcf86cd799439011")
            @PathVariable String streamId) 
    {
        try 
        {
            // Validate stream ID
            if (streamId == null || streamId.trim().isEmpty()) 
            {
                return ResponseEntity.badRequest().build();
            }
            
            // Get live stream by ID
            LiveStreamDto liveStreamDto = liveStreamService.getLiveStreamById(streamId);
            liveStreamDto.setStreamKey("");
            liveStreamDto.setIngestEndpoint("");
            liveStreamDto.setChannelArn("");
            return ResponseEntity.ok(liveStreamDto);
            
        } 

        catch (Exception e) 
        {
            // Handle any other unexpected errors
            return ResponseEntity.internalServerError().build();
        }
    }
    
    @GetMapping("/influencer/{streamId}")
    @Operation(summary = "Get live stream by ID", 
            description = "Retrieve detailed information about a specific live stream by its ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Live stream retrieved successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid stream ID"),
        @ApiResponse(responseCode = "404", description = "Live stream not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<LiveStreamDto> getLiveStreamByIdForInfluancer(
            @Parameter(description = "Live stream ID", example = "507f1f77bcf86cd799439011")
            @PathVariable String streamId) 
    {
        try 
        {
            // Validate stream ID
            if (streamId == null || streamId.trim().isEmpty()) 
            {
                return ResponseEntity.badRequest().build();
            }
            
            // Get live stream by ID
            LiveStreamDto liveStreamDto = liveStreamService.getLiveStreamById(streamId);
            if(!liveStreamDto.getInfulancerId().equals(userSessionData.getId()))
            {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
            }
            
            return ResponseEntity.ok(liveStreamDto);
            
        } 
      
        catch (Exception e) 
        {
            // Handle any other unexpected errors
            return ResponseEntity.internalServerError().build();
        }
    }
    /**
     * Validate employee JWT token
     * Performs comprehensive JWT token validation for employee authentication
     */
    private boolean validateEmployeeToken(Employee employee, String token) 
    {
        if (token == null || token.trim().isEmpty()) 
        {
            return false;
        }

        try 
        {
            // Remove Bearer prefix if present
            String actualToken = token;
            if (token.startsWith("Bearer ")) 
            {
                actualToken = token.substring(7);
            }

            // Create secret key for JWT validation using admin JWT secret (same method as admin module)
            SecretKey key = Keys.hmacShaKeyFor(adminJwtSecret.getBytes(StandardCharsets.UTF_8));

            // Parse and validate the JWT token
            Claims claims = Jwts.parser()
                    .verifyWith(key)
                    .build()
                    .parseSignedClaims(actualToken)
                    .getPayload();

            // Extract employee information from token
            String tokenEmployeeId = claims.get("id", String.class);
            String tokenUsername = claims.getSubject();

            // Validate token was issued for this specific employee
            if (tokenEmployeeId == null || !tokenEmployeeId.equals(employee.getId())) 
            {
              
                return false;
            }

            // Validate username matches
            if (tokenUsername == null || !tokenUsername.equals(employee.getUsername())) 
            {
             
                return false;
            }

            // Check if token has expired (this is automatically checked by JWT parser, but we can add custom logic)
            long currentTime = System.currentTimeMillis() / 1000;
            long expirationTime = claims.getExpiration().getTime() / 1000;
            
            if (currentTime >= expirationTime) 
            {
                return false;
            }

            // Validate token has required role claim for admin operations
            String role = claims.get("role", String.class);
            if (role == null) 
            {
                return false;
            }

            // Additional security: Check if employee account is still active
            if (employee.isAccountLocked()) 
            {
                return false;
            }

            return true;

        } 
        
        catch (ExpiredJwtException e) 
        {
            System.err.println("❌ Token validation failed: Token has expired - " + e.getMessage());
            return false;
        } 
        catch (MalformedJwtException e) 
        {
            System.err.println("❌ Token validation failed: Malformed JWT token - " + e.getMessage());
            return false;
        } 
        catch (SecurityException e) 
        {
            System.err.println("❌ Token validation failed: Invalid JWT signature - " + e.getMessage());
            return false;
        } 
        catch (UnsupportedJwtException e) 
        {
            System.err.println("❌ Token validation failed: Unsupported JWT token - " + e.getMessage());
            return false;
        } 
        catch (IllegalArgumentException e) 
        {
            System.err.println("❌ Token validation failed: Invalid token format - " + e.getMessage());
            return false;
        } 
        catch (Exception e) 
        {
            System.err.println("❌ Token validation failed: Unexpected error - " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
   

}