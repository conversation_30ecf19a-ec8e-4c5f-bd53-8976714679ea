    package com.hb.crm.client.dto.FollowMeDto;

import com.hb.crm.core.beans.PackagePlaces.PackageCountry;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * Request DTO for setting package places in FollowMe subscription
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SetPackagePlacesRequest {
    
    @JsonProperty("packageId")
    private String packageId;
    
    @JsonProperty("packagePlaces")
    private List<PackageCountry> packagePlaces;

}
