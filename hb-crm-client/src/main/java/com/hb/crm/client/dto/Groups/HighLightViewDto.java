package com.hb.crm.client.dto.Groups;

import com.hb.crm.client.dto.posts.SimplePostMuteDto;
import com.hb.crm.client.dto.users.SimpleUserinfoDto;
import lombok.Data;
import org.springframework.data.annotation.Id;

import java.util.List;
@Data
public class HighLightViewDto {
    @Id
    private String id;
    private SimpleUserinfoDto influencer;
    private String name;
    private List<SimplePostMuteDto> stories; // List of posts (stories)
    private String image; // URL or path to the image
}
