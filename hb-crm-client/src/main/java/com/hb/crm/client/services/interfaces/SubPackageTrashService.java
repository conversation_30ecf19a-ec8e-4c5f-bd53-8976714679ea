package com.hb.crm.client.services.interfaces;

import com.hb.crm.client.dto.PageDto;
import com.hb.crm.core.Enums.PackageType;
import com.hb.crm.core.beans.Trash.TrashedSubPackage;

public interface SubPackageTrashService {
    
    /**
     * Soft delete a pending SubPackage (only draft/pending packages allowed)
     * Users can only delete their own packages that are in draft/pending status
     * 
     * @param subPackageId The ID of the SubPackage to delete
     * @param deletionReason The reason for deletion (optional)
     * @return The created TrashedSubPackage
     */
    TrashedSubPackage softDeletePendingSubPackage(String subPackageId, String deletionReason);
    
    /**
     * Get user's trashed packages with pagination
     * Only returns packages that belong to the current user
     * 
     * @param page Page number (zero-based)
     * @param size Number of items per page
     * @return PageDto containing the user's trashed packages
     */
    PageDto<TrashedSubPackage> getUserTrashedPackages(int page, int size);
    
    /**
     * Get user's trashed packages by package type with pagination
     * Only returns packages that belong to the current user
     * 
     * @param packageType The package type filter
     * @param page Page number (zero-based)
     * @param size Number of items per page
     * @return PageDto containing the filtered user's trashed packages
     */
    PageDto<TrashedSubPackage> getUserTrashedPackagesByType(PackageType packageType, int page, int size);
    
    /**
     * Search user's trashed packages with fuzzy search and pagination
     * Only searches within packages that belong to the current user
     * 
     * @param query The search query for fuzzy search (optional)
     * @param page Page number (zero-based)
     * @param size Number of items per page
     * @return PageDto containing the search results
     */
    PageDto<TrashedSubPackage> searchUserTrashedPackages(String query, int page, int size);
    
    /**
     * Search user's trashed packages with fuzzy search, package type filter, and pagination
     * Only searches within packages that belong to the current user
     * 
     * @param query The search query for fuzzy search (optional)
     * @param packageType The package type filter (optional)
     * @param page Page number (zero-based)
     * @param size Number of items per page
     * @return PageDto containing the search results
     */
    PageDto<TrashedSubPackage> searchUserTrashedPackages(String query, PackageType packageType, int page, int size);
    
    /**
     * Restore a user's SubPackage from trash back to active state
     * Users can only restore their own packages
     * 
     * @param trashedSubPackageId The ID of the TrashedSubPackage to restore
     * @return The restored SubPackage
     */
    com.hb.crm.core.beans.SubPackage restoreUserSubPackage(String trashedSubPackageId);
    
    /**
     * Get a specific user's trashed package by ID
     * Users can only access their own trashed packages
     * 
     * @param trashedSubPackageId The ID of the trashed package
     * @return The TrashedSubPackage if found and belongs to user
     */
    TrashedSubPackage getUserTrashedPackageById(String trashedSubPackageId);
    
    /**
     * Permanently delete a user's trashed package (cannot be undone)
     * Users can only permanently delete their own packages
     * 
     * @param trashedSubPackageId The ID of the trashed package to permanently delete
     */
    void permanentlyDeleteUserTrashedPackage(String trashedSubPackageId);
    
    /**
     * Get count of user's trashed packages by type
     * Only counts packages that belong to the current user
     * 
     * @param packageType The package type (optional)
     * @return The count of user's trashed packages
     */
    long getUserTrashedPackagesCount(PackageType packageType);
    
    /**
     * Get total count of all user's trashed packages
     * Only counts packages that belong to the current user
     * 
     * @return The total count of user's trashed packages
     */
    long getTotalUserTrashedPackagesCount();
    
    /**
     * Check if a user's SubPackage is in trash
     * Only checks packages that belong to the current user
     * 
     * @param subPackageId The original SubPackage ID
     * @return true if the package is in trash and belongs to user, false otherwise
     */
    boolean isUserSubPackageInTrash(String subPackageId);
}
