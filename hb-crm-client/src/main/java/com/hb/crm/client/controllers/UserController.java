package com.hb.crm.client.controllers;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.firebase.auth.FirebaseAuthException;
import com.hb.crm.client.beans.ObjectBoxEntity.TravelerInfoSyncEntity;
import com.hb.crm.client.beans.UserSessionData;
import com.hb.crm.client.dto.PageDto;
import com.hb.crm.client.dto.Results.SearchUserScoredResultDto;
import com.hb.crm.client.dto.Results.User.UserSimpleInfoResultDto;
import com.hb.crm.client.dto.Results.searchResultDto;
import com.hb.crm.client.dto.Results.searchUserResultDto;
import com.hb.crm.client.dto.SuccessMessage;
import com.hb.crm.client.dto.posts.PostDto;
import com.hb.crm.client.dto.splashScreen.splashUserStories;
import com.hb.crm.client.dto.users.*;
import com.hb.crm.client.services.interfaces.ReactService;
import com.hb.crm.client.services.interfaces.SearchService;
import com.hb.crm.client.services.interfaces.UserService;
import com.hb.crm.core.Enums.SearchEnum;
import com.hb.crm.core.dtos.MediaWithUserDto;
import com.hb.crm.core.searchBeans.ReactionSearch;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping(value = "/v1/users", produces = {MediaType.APPLICATION_JSON_VALUE})
class UserController {

    private final UserSessionData userSessionData;
    private final UserService userService;
    private final ReactService reactService;
    private final SearchService searchService;
    private final Logger logger = LoggerFactory.getLogger(UserController.class);


    @Autowired
    public UserController(UserSessionData userSessionData, UserService userService, ReactService reactService, SearchService searchService) {
        this.userSessionData = userSessionData;
        this.userService = userService;
        this.reactService = reactService;
        this.searchService = searchService;
    }

    @GetMapping("getAdminUsers")
    @ResponseBody
    public PageDto<UserInfoDto> getUsers() {
        return userService.getUsers();
    }

    @GetMapping("getAdminUserById/{city}/{id}")
    @ResponseBody
    public UserInfoDto getAdminUserById(@PathVariable("id") String id, @PathVariable("city") String city) {
        final Optional<UserInfoDto> byId = userService.findById(id);
        final UserInfoDto user = byId.get();
        return user;
    }

    @PostMapping("searchAdminUsers/{page}")
    @ResponseBody
    public PageDto<UserInfoDto> searchAdminUsers(@RequestBody Map<String, Object> obj, @PathVariable("page") int page) {
        //TODO Add specification
        return userService.search(obj, page, 99999);
    }

    @GetMapping("getUserinfo")
    @ResponseBody
    @Deprecated
    public UserCompleteRegiserationDto getUserinfo() {
        return userService.getUserinfo();
    }

    @GetMapping("getFullUserinfo")
    @ResponseBody
    public UserFullInfoDto getFullUserinfo() {
        return userService.getFullUserinfo();
    }
    @PostMapping("postGuestInfo")
    @ResponseBody
    public GuestInfo postGuestInfo(@RequestBody GuestInfo userDto) {
        return userService.completeGustRegistration(userDto);
    }

    @PreAuthorize("hasAnyRole('Influencer', 'Traveler')")

    @PostMapping("postUserinfo")
    @ResponseBody
    public UserCompleteRegiserationDto postUserinfo(@RequestBody UserCompleteRegiserationDto userdto) {
        String id = userSessionData.getId();
        return userService.completeUserRegistration(userdto, id);
    }
    @PreAuthorize("hasAnyRole('Influencer', 'Traveler')")
    @PostMapping("getFolloing")
    @Operation(summary = "get Following", description = "This api used to list the Following")
    public com.hb.crm.core.dtos.searchUserResultDto getFolloing(@RequestBody CustomSearchDto FollowDto) {
        return userService.getFollowing(FollowDto.getPropartyId(), FollowDto.getPagenumber(), FollowDto.getItemsPerPage(), FollowDto.getSearch());
    }
    @PreAuthorize("hasAnyRole('Influencer', 'Traveler')")
    @PostMapping("getFolloers")
    @Operation(summary = "get Followers", description = "This api used to list the Followers")
    public PageDto<SimpleUserinfoDto> getFolloers(@RequestBody CustomSearchDto FollowDto) {
        return userService.getFollowers(FollowDto.getPropartyId(), FollowDto.getPagenumber(), FollowDto.getItemsPerPage(), FollowDto.getSearch());
    }


    @GetMapping("getUserStories/{page}/{size}")
    @Operation(
            summary = "Get Users With stories ",
            description = "This API meant for home page to get stories grouped by users (deprecate)"
    )
    @ResponseBody
    public PageDto<UserStories> getUserStories(@PathVariable("page") int page, @PathVariable("size") int size)
    {
        //TODO resend reset password email
        return userService.getUserStories(page, size);
    }

    @GetMapping("saved/reels")
    @Operation(
            summary = "Get User's saved reels ",
            description = "This API meant get reels grouped by users saved section"
    )
    @ResponseBody
    public PageDto<MediaWithUserDto> getUserSavedReels(
            @RequestParam(required = false) String userId,
            @RequestParam("page") int page,
            @RequestParam("size") int size) {
        return userService.getUserSavedReels(userId, page, size);
    }


    @GetMapping("getUserStoriesv2")
    @Operation(
            summary = "Get Users With stories ",
            description = "This API meant for home page to get stories grouped by users"
    )
    @ResponseBody
    public PageDto<splashUserStories> getUserStoriesv2(@RequestParam(required = false) String usernamr, @RequestParam("page") int page,  @RequestParam("size") int size) {
        //TODO resend reset password email
        return userService.getUserStoriesSplash(usernamr,page, size);
    }

    @GetMapping("{userId}/medias")
    @Operation(
            summary = "Get User Medias",
            description = "This API meant for profile to get medias for specific user"
    )
    @ResponseBody
    public PageDto<MediaWithUserDto> getUserMedia(
            @PathVariable String userId,
            @RequestParam(defaultValue = "reel") com.hb.crm.core.Enums.MediaType mediaTypeFilter,
            @RequestParam int page,
            @RequestParam int size) {
        return userService.getUserMedia(userId, mediaTypeFilter, page, size);
    }

    @GetMapping("/stories/{userId}")
    @Operation(summary = "deprecate", description = "deprecate use searchUserItem")

    public ResponseEntity<List<PostDto>> getUserStoriesById(@PathVariable String userId)
    {
        List<PostDto> userStories = userService.getUserStories(userId);

        if (userStories == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(userStories);
    }

    @GetMapping("/stories/username/{username}")
    @Operation(summary = "deprecate", description = "deprecate use searchUserItem")
    public ResponseEntity<List<PostDto>> getUserStoriesByUsername(@PathVariable String username)
    {
        List<PostDto> userStories = userService.getUserStoriesByUsername(username);

        if (userStories == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(userStories);
    }




    @GetMapping("/searchUserItem")
    @Operation(summary = "influencer Items", description = "This api meant for influencer profile" +
            " used to get packages and posts and stories related to the influencer")
    public searchResultDto search(
            @Parameter(description = "InfulancerId", example = "65cde24816adba73ed021488")
            @RequestParam(required = false) String InfulancerId,
            @Parameter(description = "InfulancerUserNamr", example = "abdo")
            @RequestParam(required = false) String InfulancerUserName,
            @Parameter(description = "Search query text", example = "")
            @RequestParam(required = false) String query,
            @Parameter(description = "Filter results based on a specific class", example = "Package,POST,story,followPackage")
            @RequestParam(required = false) SearchEnum classFilter,
            @Parameter(description = "Page number for pagination", example = "0")
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Number of results per page", example = "10")
            @RequestParam(defaultValue = "10") int size
    ) {
        return userService.searchUserItems(query, InfulancerId,InfulancerUserName, classFilter, page, size);
    }


    @GetMapping("getTravelerInfo")
    public TravelerUserDto GetTravelerInfo(@RequestParam(required = false) String Travelerid) {
        return userService.getTravelerInfo(Travelerid);
    }
    @Operation(summary = "get traveler by username ", description = "This api used to fetch the traveler by the user name ")
    @GetMapping("/traveler/{username}")
    public TravelerUserDto getTravelerInfoByUsername(@PathVariable String username) {
        return userService.getTravelerInfoByUsername(username);
    }

    @GetMapping("getInfluencerInfo")
    public InfluencerUserDto getInfluencerInfo(@RequestParam(required = false) String Influencerid) {
        logger.error("the Influencer is {}  ", Influencerid);
        return userService.getInfluenceInfo(Influencerid);
    }
    @GetMapping("TriggerAction")
    public ResponseEntity<String> TriggerAction() {
         userService.triggerAction();
         return  ResponseEntity.ok("done");
    }

    @GetMapping("FindTriggerAction")
    public ResponseEntity<List<TravelerInfoSyncEntity>> findTriggerAction() {
        return  ResponseEntity.ok(userService.findActions());
    }

    @DeleteMapping("/objectBox/delete/all")
    public ResponseEntity<String> deleteAllTravelers() {
        userService.deleteAllTravelerInfo();
        return ResponseEntity.ok("All traveler records deleted successfully.");
    }

    @GetMapping("/influencer/{username}")
    @Operation(summary = "get influencer by username ", description = "This api used to fetch the influencer by the user name ")
    public InfluencerUserDto getInfluencerInfoByUsername(@PathVariable String username) {
        return userService.getInfluenceInfoByUsername(username);
    }

    @GetMapping("getReacts")
    @PreAuthorize("hasAnyRole('Influencer', 'Traveler')")
    public List<ReactionSearch> getReactsWithoutInfluencer(@RequestParam String fromdate, @RequestParam(required = false) String toDate) throws ParseException {
        return reactService.userReaction(fromdate, toDate);
    }
    @PreAuthorize("hasAnyRole('Influencer', 'Traveler')")
    @GetMapping("getFollowedInfluencer")
    @Operation(summary = "get Followed Influencer", description = "This api meant to list All followed influencer to the current user for reaction ")
    public List<ReactionSearch> getFollowedInfluencer() {
        return reactService.userfollows();
    }

    @PostMapping("addReacts")
    @PreAuthorize("hasAnyRole('Influencer', 'Traveler')")
    @Operation(
            summary = "Add a reaction to content",
            description = """
                Adds a user reaction to various types of content within the platform.
                Supports reactions to posts, stories, packages, users, comments, and media content.
                entityType : Type of Entity reaction (e.g., React, PostMark, Favourite, Subscribe, Follow, View,Share,SaveReel)
                entityName : name of entity (e.g., Post, Package,FollowPackage, Comment, Media, User)
                """,
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Reaction object with entityId, entityName, entityType, and reactionType",
                    content = @Content(
                            mediaType = "application/json",
                            examples = {
                                    @ExampleObject(
                                            name = "React to Post",
                                            summary = "React to a post (entity id is the post id)",
                                            value = """
                                                    {
                                                        "entityId": "64f0c6f7a1a4e2f3d6b9c7e8",
                                                        "entityName": "Post",
                                                        "entityType": "React",
                                                        "reactionType": "like",
                                                        "userId": "12345"
                                                    }"""
                                    ),
                                    @ExampleObject(
                                            name = "PostMark a Post",
                                            summary = "PostMark a post (entity id is the post id)",
                                            value = """
                                                    {
                                                        "entityId": "64f0c6f7a1a4e2f3d6b9c7e9",
                                                        "entityName": "Post",
                                                        "entityType": "PostMark",
                                                      }"""
                                    ),
                                    @ExampleObject(
                                            name = "Favourite a Package",
                                            summary = "Favourite a travel package (entity id is the package id)",
                                            value = """
                                                    {
                                                        "entityId": "67890abcdef123456789",
                                                        "entityName": "Package",
                                                        "entityType": "Favourite",
                                                      }"""
                                    ),
                                    @ExampleObject(
                                            name = "Favourite a Follow me Package",
                                            summary = "Favourite a travel package (entity id is the package id)",
                                            value = """
                                                    {
                                                        "entityId": "67890abcdef123456789",
                                                        "entityName": "FollowPackage",
                                                        "entityType": "Favourite",
                                                    }"""
                                    ),
                                    @ExampleObject(
                                            name = "Subscribe to Package",
                                            summary = "Subscribe to a package",
                                            value = """
                                                    {
                                                        "entityId": "67890abcdef123456780",
                                                        "entityName": "Package",
                                                        "entityType": "Subscribe",
                                                      }"""
                                    ),
                                    @ExampleObject(
                                            name = "Follow User",
                                            summary = "Follow a user (entity id is the followed user id)",
                                            value = """
                                                    {
                                                        "entityId": "65cde24816adba73ed021488",
                                                        "entityName": "User",
                                                        "entityType": "Follow",
                                                      }"""
                                    ),
                                    @ExampleObject(
                                            name = "React to Media",
                                            summary = "React to media content (entity id is the media id)",
                                            value = """
                                                    {
                                                        "entityId": "64f0c6f7a1a4e2f3d6b9c7ec",
                                                        "entityName": "Media",
                                                        "entityType": "React",
                                                        "reactionType": "like",
                                                     }"""
                                    ),
                                    @ExampleObject(
                                            name = "Follow Package",
                                            summary = "Follow a package (entity id is the package id)",
                                            value = """
                                                    {
                                                        "entityId": "67890abcdef123456781",
                                                        "entityName": "Package",
                                                        "entityType": "Follow",
                                                        "reactionType": "angry",
                                                     }"""
                                    )
                            }
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Successful Response",
                            content = @Content(
                                    mediaType = "application/json",
                                    examples = @ExampleObject(
                                            value = """
                                                    {
                                                        "id": "64f0c6f7a1a4e2f3d6b9c7e8",
                                                        "userId": "12345",
                                                        "entityId": "67890",
                                                        "entityName": "Post",
                                                        "entityType": "React",
                                                        "reactionType": "like",
                                                        "updated": "2024-08-28T10:15:30"
                                                    }"""
                                    )
                            )
                    ),
                    @ApiResponse(responseCode = "400", description = "Invalid input - Check entityType and reactionType values")
            }
    )
    public ReactionSearch addReacts(@RequestBody ReactionSearch reactionSearch) {
        return reactService.AddReact(reactionSearch);
    }

    @PostMapping("removeReacts")
    @PreAuthorize("hasAnyRole('Influencer', 'Traveler')")
    public ReactionSearch removeReacts(@RequestBody ReactionSearch reactionSearch) {
        reactService.RemoveReact(reactionSearch.getId(), reactionSearch.getEntityId(), reactionSearch.getEntityName(), reactionSearch.getEntityType());
        return reactionSearch;
    }

    @GetMapping("GetInfluencers")
    @ResponseBody
    public List<UserInfoDto> GetInfluencers() {
        return userService.GetAllInfluence();
    }

    @GetMapping("GetInfluencers/paginated")
    @ResponseBody
    public Page<UserInfoDto> GetInfluencers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        return userService.GetAllInfluence(page, size);
    }


    @PostMapping("addFcmToken")
    @ResponseBody
    public fcmToken AddFcmToken(@RequestBody fcmToken fcm) throws FirebaseAuthException {
        return userService.AddFcmToken(fcm);
    }

    @DeleteMapping("/fcm-token")
    @Operation(summary = "Delete all FCM tokens", description = "Removes all FCM tokens for the specified user")
    public ResponseEntity<Void> deleteAllFcmTokens(@RequestParam(required = false) String userId) throws FirebaseAuthException {
        userService.deleteAllFcmTokens(userId);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/fcm-token")
    @Operation(summary = "Get user FCM token", description = "Get user FCM token")
    public ResponseEntity<String> getUserFCMToken(@RequestParam(required = false) String userId) {
        return ResponseEntity.ok(userService.getUserFCMToken(userId));
    }

    @PreAuthorize("hasAnyRole('Influencer', 'Traveler')")
    @Operation(summary = "Deprecated", description = "please Use Add Reacts")
    @GetMapping("followInfluencer/{influencerId}")
    @ResponseBody
    public ResponseEntity<SuccessMessage> FollowInfluencer(@PathVariable("influencerId") String InfluencerId) throws JsonProcessingException {
        //TODO resend reset password email
        userService.FollowInfluence(InfluencerId);
        return ResponseEntity.ok(new SuccessMessage("Influencer Followed "));
    }


    @PreAuthorize("hasAnyRole('Influencer', 'Traveler')")
    @Operation(summary = "Deprecated", description = "please Use remove Reacts")
    @GetMapping("unFollowInfluencer/{influencerId}")
    @ResponseBody
    public ResponseEntity<SuccessMessage> UnFollowInfluencer(@PathVariable("influencerId") String InfluencerId) {
        //TODO resend reset password email
        userService.UnFollowInfluence(InfluencerId);
        return ResponseEntity.ok(new SuccessMessage("Influencer unFollowed"));

    }


    @PreAuthorize("hasAnyRole('Influencer', 'Traveler')")
    @GetMapping("getUsersForMentions")
    @Operation(
            summary = "Get Users for Mentions",
            description = """
                        Retrieves users that match the given search query.\s
                        The query can search for users by:
                        - First Name
                        - Last Name
                        - Email
                        - Country
                        Results are paginated based on the provided page number and items per page.
                   \s"""
     )
    @ResponseBody
    public UserSimpleInfoResultDto getUsersForMentions(
            @Parameter(description = "Page number for pagination (starting from 0)", example = "0")
            @RequestParam int PageNumber,

            @Parameter(description = "Number of items per page", example = "10")
            @RequestParam int ItemsPerPage,

            @Parameter(
                    description = """
                                Search term used to filter users. 
                                - Searches across First Name, Last Name, Email, and Country.
                                - Example: 'John' will match 'John Doe', 'Johnny', and '<EMAIL>'.
                            """,
                    example = "John"
            )
            @RequestParam String query
    ) {
        return searchService.getUsersForMentions(query, PageNumber, ItemsPerPage);
    }

    @PreAuthorize("hasAnyRole('Influencer', 'Traveler')")
    @Operation(summary = "change Profile Image", description = "change Current User Profile Image .")
    @PostMapping("changeProfileImage")
    @ResponseBody
    public void changeProfileImage(@RequestBody String Image) {
        userService.changeProfileImage(Image);
    }

    @PreAuthorize("hasAnyRole('Influencer', 'Traveler')")
    @PostMapping("changeCoverPhoto")
    @ResponseBody
    public void changeCoverPhoto(@RequestBody String Photo) {
        userService.changeCoverPhoto(Photo);
    }

    @Operation(summary = "Getting recomanded  Influencer", description = "Getting recomanded  Influencer for Current User (deprecated).")
    @PostMapping("recomandedv2")
    @ResponseBody
    public SearchUserScoredResultDto Recomandedv2(@RequestBody Map<String, Object> obj, @RequestParam int page,
                                                @RequestParam(defaultValue = "10") int limit) {
        //TODO Add specification
        return userService.recommendedv2(obj, page, limit);
    }


    @Operation(summary = "Getting recomanded  Influencer", description = "Getting recomanded  Influencer for Current User.")
    @PostMapping("recomanded")
    @ResponseBody
    public PageDto<UserInfoDto> Recomanded(@RequestBody Map<String, Object> obj, @RequestParam int page, @RequestParam int limit) {
        //TODO Add specification
        return userService.recommended(obj, page, limit);
    }

    @Operation(summary = "Getting Top  Influencer", description = "Getting Top  Influencer based on Follower Count.")
    @PostMapping("topFollowerCountv2")
    @ResponseBody
    public searchUserResultDto TopRatedv2(@RequestBody Map<String, Object> obj, @RequestParam int page,
                                        @RequestParam(defaultValue = "10") int limit) {
        //TODO Add specification
        return userService.topRatedv2(obj, page, limit);
    }

    @Operation(summary = "Getting Top  Influencer", description = "Getting Top  Influencer based on Follower Count. (Deprecated)")
    @PostMapping("topFollowerCount")
    @ResponseBody
    public PageDto<UserInfoDto> TopRated(@RequestBody Map<String, Object> obj, @RequestParam int page) {
        //TODO Add specification
        return userService.topRated(obj, page);
    }

    @Operation(summary = "Delete user account", description = "Deletes the current user's account from the system ")
    @DeleteMapping("delete")
    @ResponseBody
    public String delete() throws FirebaseAuthException {
        //TODO Add specification
        return userService.delete();
    }

    @GetMapping("search/explore")
    @Operation(summary = "Search Users",
            description = "Search users by name or package name. If no query is provided, returns all users.")
    @ResponseBody
    public com.hb.crm.core.dtos.searchUserResultDto searchUsers(
            @Parameter(description = "Search query text (optional)", example = "john")
            @RequestParam(required = false) String query,

            @Parameter(description = "Page number for pagination", example = "0")
            @RequestParam(defaultValue = "0") int page,

            @Parameter(description = "Number of results per page", example = "10")
            @RequestParam(defaultValue = "10") int size
    ) {
        return userService.searchUsers(query, page, size);
    }


    @GetMapping("/{username}")
    @ResponseBody
    public UserDto getUserInfoByUsername(@PathVariable("username") String username) {
        return userService.getUserDataByUserName(username);
    }

    @GetMapping("/all/except-guest")
    @Operation(
            summary = "Get all users except guest users",
            description = """
                    Retrieves all users except guest users with optional fuzzy search and pagination.
                    The search can match across multiple fields including:
                    - First Name
                    - Last Name
                    - Username
                    - Email
                    - City
                    - Country
                    Results are sorted by creation date in descending order.
                    """
    )
    @ResponseBody
    public com.hb.crm.core.dtos.PageDto<UserInfoDto> getAllUsersExceptGuest(
            @Parameter(description = "Search query for fuzzy search (optional)", example = "john")
            @RequestParam(required = false) String query,

            @Parameter(description = "Page number for pagination (zero-based)", example = "0")
            @RequestParam(defaultValue = "0") int page,

            @Parameter(description = "Number of items per page", example = "10")
            @RequestParam(defaultValue = "10") int size
    ) {
        return userService.getAllUsersExceptGuest(query, page, size);
    }
}
