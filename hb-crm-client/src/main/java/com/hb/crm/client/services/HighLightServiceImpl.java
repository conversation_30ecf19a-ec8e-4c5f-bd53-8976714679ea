package com.hb.crm.client.services;

import com.hb.crm.client.beans.UserSessionData;
import com.hb.crm.client.config.CustomHundlerar.CustomException;
import com.hb.crm.client.dto.Groups.HighLightDTO;
import com.hb.crm.client.dto.Groups.HighLightListDTO;
import com.hb.crm.client.dto.Groups.HighLightViewDto;
import com.hb.crm.client.dto.PageDto;
import com.hb.crm.client.dto.Results.searchResultDto;
import com.hb.crm.client.services.interfaces.HighLightService;
import com.hb.crm.client.services.interfaces.QueryNormalizeService;
import com.hb.crm.core.Enums.SearchEnum;
import com.hb.crm.core.beans.HighLight;
import com.hb.crm.core.beans.Post;
import com.hb.crm.core.beans.User;
import com.hb.crm.core.repositories.HighLightRepository;
import com.mongodb.BasicDBObject;
import org.bson.types.ObjectId;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
@Service
public class HighLightServiceImpl implements HighLightService {
    private final HighLightRepository highLightRepository;


    private final UserSessionData userSessionData;
    private final MongoTemplate mongoTemplate2;
    private final MongoTemplate mongoTemplate;
    private final QueryNormalizeService queryNormalazieService;
    private  final ModelMapper modelMapper;

    public HighLightServiceImpl(HighLightRepository highLightRepository, UserSessionData userSessionData, @Qualifier("mongoTemplate2") MongoTemplate mongoTemplate2, @Qualifier("mongoTemplate1") MongoTemplate mongoTemplate, QueryNormalizeService queryNormalazieService, ModelMapper modelMapper) {
        this.highLightRepository = highLightRepository;
        this.userSessionData = userSessionData;
        this.mongoTemplate2 = mongoTemplate2;
        this.mongoTemplate = mongoTemplate;
        this.queryNormalazieService = queryNormalazieService;
        this.modelMapper = modelMapper;
    }


    // Create or Update HighLight
    public HighLightViewDto createOrUpdateHighLight(HighLightDTO groupDTO) {
        String influencerId = userSessionData.getId(); // Get influencerId from session
        if (influencerId == null) {
            throw new IllegalArgumentException("Influencer ID is missing from session");
        }

        Optional<HighLight> existingGroup = highLightRepository.findByNameAndInfluencer_Id(groupDTO.getName(), influencerId);
        HighLight highLight;

        if (existingGroup.isPresent()) {
            highLight = existingGroup.get();
            highLight.setImage(groupDTO.getImage());
            highLight.setStories(getPostReferences(groupDTO.getStoryIds()));
        } else {
            highLight = new HighLight();
            highLight.setInfluencer(new User(influencerId));
            highLight.setName(groupDTO.getName());
            highLight.setImage(groupDTO.getImage());
            highLight.setStories(getPostReferences(groupDTO.getStoryIds()));
        }
        highLight= highLightRepository.save(highLight);
        return modelMapper.map(highLight, HighLightViewDto.class);
    }

    // Add multiple stories to the group (Replace existing ones)
    public HighLightViewDto addStoryToHighLight(String groupId, List<String> storyIds) {
        HighLight highLight = highLightRepository.findById(groupId)
                .orElseThrow(() -> new CustomException(404,"HighLight not found"));

        highLight.setStories(getPostReferences(storyIds)); // Replace the existing list with new stories
        highLight= highLightRepository.save(highLight);
        return modelMapper.map(highLight, HighLightViewDto.class);

    }
    @Override
    public PageDto<HighLightListDTO> getHighLightsForInfluencer(String username, String influencerId, String keyword, int page, int size) {
        List<AggregationOperation> operations = new ArrayList<>();

        // Match by influencer ID if provided
        if (influencerId != null && !influencerId.isEmpty()) {
            operations.add(Aggregation.match(Criteria.where("influencer.$id").is(new ObjectId(influencerId))));
        }

        // Match by group name (keyword)
        if (keyword != null && !keyword.trim().isEmpty()) {
            operations.add(Aggregation.match(Criteria.where("name").regex(keyword, "i")));
        }

        // If username is provided, perform lookup and filter by username
        boolean filterByUsername = username != null && !username.trim().isEmpty();
        if (filterByUsername) {
            operations.add(Aggregation.lookup("user", "influencer.$id", "_id", "influencerDoc"));
            operations.add(Aggregation.unwind("influencerDoc"));
            operations.add(Aggregation.match(Criteria.where("influencerDoc.username").is(username)));
        }

        // Pagination
        operations.add(Aggregation.sort(Sort.Direction.DESC, "created"));
        operations.add(Aggregation.skip((long) page * size));
        operations.add(Aggregation.limit(size));

        Aggregation aggregation = Aggregation.newAggregation(operations);
        List<HighLightListDTO> items = mongoTemplate.aggregate(aggregation, "group", HighLightListDTO.class).getMappedResults();

        // Count aggregation (without pagination stages)
        List<AggregationOperation> countOps = new ArrayList<>(operations);
        countOps.removeIf(op -> op instanceof SkipOperation || op instanceof LimitOperation || op instanceof SortOperation);
        countOps.add(Aggregation.count().as("total"));
        AggregationResults<BasicDBObject> countResults = mongoTemplate.aggregate(Aggregation.newAggregation(countOps), "group", BasicDBObject.class);
        long total = countResults.getUniqueMappedResult() != null ? countResults.getUniqueMappedResult().getLong("total") : 0;

        // Wrap into PageDto
        PageDto<HighLightListDTO> pageDto = new PageDto<>(size, page, "created");
        pageDto.setItems(items);
        pageDto.setTotalNoOfItems(total);
        return pageDto;
    }


     public searchResultDto getPostsByHighLightId(String id, String query, int page, int limit) {
         HighLight highLight = highLightRepository.findById(id)
                 .orElseThrow(() -> new CustomException(404,"HighLight not found"));
         List<ObjectId> storyIds = highLight.getStories().stream()
                 .map(post -> new ObjectId(post.getId()))  // Convert String ID to ObjectId
                 .collect(Collectors.toList());

        Criteria criteria = new Criteria();
         criteria.and("_id").in(storyIds);  // Match the post IDs in the list of story IDs
        // Create pageable with descending sort on "updated" field.
        final Pageable pageable = PageRequest.of(page, limit, Sort.Direction.DESC, "updated");
        // Retrieve the search aggregation pipeline based on query and classFilter.
        Aggregation aggregation = queryNormalazieService.getSearch(query, SearchEnum.story, pageable, criteria);
        // Execute the aggregation query against the "search" collection and map the results to the searchResultDto class.
        var result = mongoTemplate2.aggregate(aggregation, "search", searchResultDto.class);
        // Return a unique mapped result from the aggregation query.
        return result.getUniqueMappedResult();
    }
     public searchResultDto getStoriesByInfulancer(  String query,int page, int limit) {


        Criteria criteria =  Criteria.where("user._id").is(new ObjectId(userSessionData.getId()));

         // Create pageable with descending sort on "updated" field.
        final Pageable pageable = PageRequest.of(page, limit, Sort.Direction.DESC, "updated");
        // Retrieve the search aggregation pipeline based on query and classFilter.
        Aggregation aggregation = queryNormalazieService.getSearch(query, SearchEnum.story, pageable, criteria);
        // Execute the aggregation query against the "search" collection and map the results to the searchResultDto class.
        var result = mongoTemplate2.aggregate(aggregation, "search", searchResultDto.class);
        // Return a unique mapped result from the aggregation query.
        return result.getUniqueMappedResult();
    }

    // Helper method to convert a list of storyIds into post references
    private List<Post> getPostReferences(List<String> storyIds) {
        return storyIds.stream()
                .map(id -> new Post(id)) // Wrap each storyId as a Reference
                .collect(Collectors.toList());
    }
}
