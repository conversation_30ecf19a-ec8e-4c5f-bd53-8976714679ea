package com.hb.crm.client.controllers;

import com.google.api.gax.rpc.NotFoundException;
import com.hb.crm.client.dto.Groups.HighLightDTO;
import com.hb.crm.client.dto.Groups.HighLightListDTO;
import com.hb.crm.client.dto.Groups.HighLightViewDto;
import com.hb.crm.client.dto.PageDto;
import com.hb.crm.client.dto.Results.searchResultDto;
import com.hb.crm.client.services.interfaces.HighLightService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Controller responsible for managing groups and their associated stories for influencers.
 */
@RestController
@RequestMapping("/v1/highLight")
public class HighLightController {

    @Autowired
    private HighLightService highLightService;

    /**
     * Create a new group or update an existing one.
     *
     * @param groupDTO Data transfer object containing group information.
     * @return The created or updated group entity.
     */
    @Operation(summary = "Create or update a group", description = "Creates a new group or updates an existing one for the logged-in influencer.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "HighLight created or updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input data"),
            @ApiResponse(responseCode = "404", description = "Influencer not found")
    })
    @PostMapping
    public ResponseEntity<HighLightViewDto> createOrUpdateGroup(@RequestBody HighLightDTO groupDTO) {
        try {
            HighLightViewDto highLight = highLightService.createOrUpdateHighLight(groupDTO);
            return ResponseEntity.status(HttpStatus.CREATED).body(highLight);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null);
        } catch (NotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }
    }

    /**
     * Add stories to a specific group, replacing existing stories.
     *
     * @param groupId  The ID of the group.
     * @param storyIds List of story IDs to associate with the group.
     * @return The updated group entity.
     */
    @Operation(summary = "Add stories to a group", description = "Adds a list of story IDs to the specified group, replacing existing ones.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Stories added successfully"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/{groupId}/stories")
    public ResponseEntity<HighLightViewDto> addStoriesToGroup(@PathVariable String groupId, @RequestBody List<String> storyIds) {
        try {
            HighLightViewDto updatedHighLight = highLightService.addStoryToHighLight(groupId, storyIds);
            return ResponseEntity.ok(updatedHighLight);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    /**
     * Get paginated stories for a specific group by its ID, with optional keyword search.
     *
     * @param groupId        The ID of the group.
     * @param search         Optional search keyword.
     * @param pageNumber     Page number (starting from 0).
     * @param itemsPerPage   Number of items per page.
     * @return Paginated list of stories.
     */
    @Operation(summary = "Get stories by group ID", description = "Retrieves a paginated list of stories associated with a specific group.")
    @PostMapping("/getStoriesByGroupId/{pageNumber}/{itemsPerPage}")
    @ResponseBody
    public searchResultDto getPostsByGroupId(@RequestParam String groupId, @RequestParam(required = false) String search,
                                             @PathVariable("pageNumber") int pageNumber,
                                             @PathVariable("itemsPerPage") int itemsPerPage) {
        return highLightService.getPostsByHighLightId(groupId, search, pageNumber, itemsPerPage);
    }

    /**
     * Get paginated list of groups created by the current influencer, with optional keyword search.
     *
     * @param keyword Optional search keyword.
     * @param page    Page number (default is 0).
     * @param size    Number of groups per page (default is 10).
     * @return Paginated list of groups.
     */
    @Operation(summary = "Get all groups for the influencer", description = "Retrieves a paginated list of groups belonging to the logged-in influencer, with optional search keyword.")
    @GetMapping
    public PageDto<HighLightListDTO> getGroups(
                                           @RequestParam(required = false) String username,
                                           @RequestParam(required = false) String influencerId,
                                           @RequestParam(required = false) String keyword,
                                           @RequestParam(defaultValue = "0") int page,
                                           @RequestParam(defaultValue = "10") int size) {
        return highLightService.getHighLightsForInfluencer(username,influencerId,keyword, page, size);
    }

    /**
     * Get paginated list of stories across all groups for the current influencer, with optional keyword search.
     *
     * @param search         Optional search keyword.
     * @param pageNumber     Page number.
     * @param itemsPerPage   Number of items per page.
     * @return Paginated search results.
     */
    @Operation(summary = "Get all stories for influencer", description = "Retrieves a paginated list of all stories belonging to all groups of the logged-in influencer.")
    @PostMapping("/getStoriesByInfluencerId/{pageNumber}/{itemsPerPage}")
    @ResponseBody
    public searchResultDto getStoriesByInfluencerId(@RequestParam(required = false) String search,
                                                    @PathVariable("pageNumber") int pageNumber,
                                                    @PathVariable("itemsPerPage") int itemsPerPage) {
        return highLightService.getStoriesByInfulancer(search, pageNumber, itemsPerPage);
    }
}
