package com.hb.crm.client.dto.PackageDtos;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hb.crm.client.dto.MediaWrapperDto;
import com.hb.crm.client.dto.keyandvalue;
import com.hb.crm.client.dto.mood.MoodDto;
import com.hb.crm.client.dto.posts.PostDto;
import com.hb.crm.client.dto.users.UserInfoDto;
import com.hb.crm.core.Enums.PackageType;
import com.hb.crm.core.beans.Flight.Flight;
import com.hb.crm.core.beans.FlightPackage.Airport;
import com.hb.crm.core.beans.Hotel.Hotel;
import com.hb.crm.core.beans.PackagePlaces.PackageCountry;
import com.hb.crm.core.beans.Rate;
import com.hb.crm.core.beans.Tag;
import lombok.Data;
import org.springframework.data.annotation.Id;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Data
public class FullPackageDto {
    @Id
    private String id;
    private String link;
    private PackageType packageType = PackageType.TravelWithMe;
    private String name;
    private String description;
    private Boolean loveIt;
    private String theme;
    private Object details;
    private Object price;
    private List<MediaWrapperDto> medias;
    private LocalDateTime start;
    private LocalDateTime end;
    private UserInfoDto infulancer;
    private MediaWrapperDto brochure;
    private MediaWrapperDto mainImage;
    private boolean privateDate = false;
    private int capacity;
    private int subscribeCount;
    private List<Tag> tags;
    private boolean favouritebyme;
    private List<ActivityGroupDto> activities;
    private List<PostDto> posts;
    private List<MoodDto> moods;
    private List<Rate> rates;
    private double AvgRate;
    private Rate myRate;
    private float BusinessFLight;
    private float EconomicFLight;
    private List<Flight> flights;
    private List<Hotel> hotels;
    private boolean alreadyActive = false;
    private boolean subscribed = false;
    private LocalDateTime expierDate;
    private BigDecimal totalPrice;
    private BigDecimal discoundPirce;
    private Airport fromAirport;
    private Airport toAirport;
    private int followMeDiscount;
    private LocalDateTime creationDate;
    private boolean NotficationEnabledByMe;
    private List<PackageCountry> packagePlaces;
    @JsonIgnore
    private AlphaPackage _Package;
    private String followMeSlug;

    private int numberOfRoom;
    private boolean availableForFollowMe;
    private LocalDateTime availableFrom;
    private long duration;
    private List<HotelWithLocationDTO> hotelsWithLocations;
    private List<TransportationWithLocationDTO> transportationWithLocations;



    public long getDuration() {
        return ChronoUnit.DAYS.between(this.start, this.end) + 1;
    }

    public boolean isAvailableForFollowMe() {
        return _Package.isAvailableForFollowMe();
    }





    public List<MoodDto> getMoods() {
        return _Package.getMoods();
    }


    public String getName() {
        return _Package.getName();
    }

    public void setName(String name) {
        _Package.setName(name);
    }

    public String getDescription() {
        return _Package.getDescription();
    }

    public void setDescription(String description) {
        _Package.setDescription(description);
    }

    public List<MediaWrapperDto> getMedias() {
        return _Package.getMedias();
    }

    public void setMedias(List<MediaWrapperDto> medias) {
        _Package.setMedias(medias);
    }

    public UserInfoDto getInfulancer() {
        return _Package.getInfulancer();
    }

    public void setInfulancer(UserInfoDto infulancer) {
        _Package.setInfulancer(infulancer);
    }

    public List<Tag> getTags() {
        return _Package.getTags();
    }

    public void setTags(List<Tag> tags) {
        _Package.setTags(tags);
    }


    public Object getPrice() {
        HashMap<String, String> map = (HashMap<String, String>) this.price;
        if (map == null) {
            return this.price;
        }
        return getingvalues(map);
    }

    public Airport getToAirport() {
        return this._Package.getToAirport();
    }

    public Airport getFromAirport() {
        return this._Package.getFromAirport();
    }

 public  String getFollowMeSlug(){
        return  this._Package.getFollowMeSlug();
 }

 public  void setFollowMeSlug(String followMeSlug) {
          this._Package.setFollowMeSlug(followMeSlug);
 }
    public void setToAirport(Airport toAirport) {
        this._Package.setToAirport(toAirport);
    }

    public void setFromAirport(Airport fromAirport) {
        this._Package.setFromAirport(fromAirport);
    }

    public BigDecimal getDiscoundPirce() {
        var discount = totalPrice.multiply(BigDecimal.valueOf(followMeDiscount)).divide(BigDecimal.valueOf(100));
        return totalPrice.subtract(discount);
    }

    public List<Rate> getRates() {
        return this._Package.getRates();
    }

    public double getAvgRate() {
        return this._Package.getAvgRate();
    }

    public MediaWrapperDto getBrochure() {
        return _Package.getBrochure();
    }

    public void setBrochure(MediaWrapperDto brochure) {
        this._Package.setBrochure(brochure);
    }

    public MediaWrapperDto getMainImage() {
        return _Package.getMainImage();
    }

    public void setMainImage(MediaWrapperDto mainImage) {
        this._Package.setMainImage(mainImage);
    }

    public List<PackageCountry> getPackagePlaces() {
        return packagePlaces;
    }

    public void setPackagePlaces(List<PackageCountry> packagePlaces) {
        this.packagePlaces = packagePlaces;
    }

    private List<keyandvalue> getingvalues(HashMap<String, String> map) {
        List<keyandvalue> values = new ArrayList<keyandvalue>();
        for (HashMap.Entry<String, String> entry : map.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            values.add(new keyandvalue(key, value));
        }
        return values;
    }
}
