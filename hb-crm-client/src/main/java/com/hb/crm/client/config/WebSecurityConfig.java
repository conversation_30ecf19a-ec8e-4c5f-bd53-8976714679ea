package com.hb.crm.client.config;


import com.hb.crm.client.config.authentication.CustomJwtAuthenticationFilter;
import com.hb.crm.client.config.authentication.JwtAuthenticationEntryPoint;
import com.hb.crm.client.services.interfaces.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.RequestContextFilter;

import java.util.List;

@Configuration
@EnableWebSecurity
@EnableAsync
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class WebSecurityConfig { // (1)


    @Autowired
    private UserService userService;

    @Autowired
    private CustomJwtAuthenticationFilter customJwtAuthenticationFilter;

    @Autowired
    private JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;

    @Autowired
    public PasswordEncoder passwordEncoder;


//    @Override
//    public void configure(AuthenticationManagerBuilder auth) throws Exception {
//        auth.userDetailsService(userService).passwordEncoder(passwordEncoder);
//    }

    @Bean
    public DaoAuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(userService);
        authProvider.setPasswordEncoder(passwordEncoder);
        return authProvider;
    }

    @Bean
    public AuthenticationManager authenticationManager(
            AuthenticationConfiguration authConfig) throws Exception {
        return authConfig.getAuthenticationManager();
    }

    private static final String[] AUTH_WHITELIST = {
            // -- Swagger UI v2
            "/v2/api-docs",
            "/swagger-resources",
            "/swagger-resources/**",
            "/configuration/ui",
            "/configuration/security",
            "/swagger-ui.html",
            "/webjars/**",
            // -- Swagger UI v3 (OpenAPI)
            "/v3/api-docs/**",
            "/gfx/*/**",
            "/spring-security-rest/api/v2/api-docs",
            "/v1/country/**",
            "/v1/tag/*",
            "/v1/tag/**",
            "/privacypolicy.html",
            "/apple-app-site-association",
            "/img/HighLight.svg",
            "/img/HighLight (2).svg",
            "/img/Frame.svg",
            "/v1/package/success",
            "/v1/package/TravelWithmeSuccess",
            "/v1/package/paymentdone",
            "/v1/package/cancel",
            "/v1/users/getUserStoriesv2",
            "/v1/authent/*",
            "/health",
            "/.well-known/assetlinks.json",
            "/.well-known/apple-app-site-association",
            "/apple-app-site-association",
            "/",
            "/ws/live-stream/**",
            "/ws/live-stream",
            // Add your anonymous endpoints here
            "/v1/live-stream/force-stop-by-admin" 

            // other public endpoints of your API may be appended to this array
    };

//    @Override
//    public void configure(HttpSecurity http) throws Exception {
//        http.csrf().disable()
//                .authorizeRequests()
//                .antMatchers( AUTH_WHITELIST).permitAll()
//                .antMatchers( ).permitAll()
//                .anyRequest().authenticated()
//                .and().exceptionHandling().authenticationEntryPoint(jwtAuthenticationEntryPoint).
//                and().sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS).
//                and().addFilterBefore(customJwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
//    }


    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http, RequestContextFilter requestContextFilter) throws Exception {
        http.csrf(AbstractHttpConfigurer::disable);
        http.headers(headers -> headers
                .frameOptions(HeadersConfigurer.FrameOptionsConfig::sameOrigin) // Allow iframes from the same origin
        );
        http.cors(Customizer.withDefaults())
                .authenticationProvider(authenticationProvider());
        http.authorizeHttpRequests(request -> {
            request.requestMatchers( AUTH_WHITELIST).permitAll()
                    .requestMatchers("/v3/api-docs/**", "/swagger-ui/**", "/swagger-ui.html").permitAll()
                    .anyRequest().authenticated();
        });
        http.exceptionHandling(exception -> exception.authenticationEntryPoint(jwtAuthenticationEntryPoint));
        http.sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS));
        http.addFilterBefore(customJwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
        return http.build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        
        // For development: Allow all origins
        configuration.addAllowedOriginPattern("*");
        
        // For production: Use specific origins
        
        configuration.setAllowedOrigins(List.of(
            "https://api.havebreak.cc",
            "https://www.havebreak.cc",
            "https://havebreak.cc",
            "http://localhost:3000"
            //"http://localhost:8089",
            //"file://"
        ));
        
        
        configuration.setAllowedMethods(List.of("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(List.of("*"));
        configuration.setAllowCredentials(true);
        configuration.setMaxAge(3600L);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/v1/**", configuration);
        //source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
