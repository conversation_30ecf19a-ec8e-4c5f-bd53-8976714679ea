package com.hb.crm.client.dto.splashScreen;

import com.hb.crm.core.Enums.Gender;
import com.hb.crm.core.Enums.UserType;
import lombok.Data;
import org.springframework.data.annotation.Id;
@Data
public class splashUserInfoDto {
    @Id
    private String id;
    private String firstName;
    private String lastName;
    private String username;
    private UserType usertype;
    private int follwerscount;
    private String ProfileImage;
    private Gender gender;
    private boolean hasLiveStream;
    private String liveStreamId;
    private String liveStreamPlaybackUrl;
}
