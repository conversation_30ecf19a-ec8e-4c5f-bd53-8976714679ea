package com.hb.crm.client.services;

import com.hb.crm.client.dto.PageDto;
import com.hb.crm.client.services.interfaces.SubPackageTrashService;
import com.hb.crm.core.Enums.PackageStatus;
import com.hb.crm.core.Enums.PackageType;
import com.hb.crm.core.beans.SubPackage;
import com.hb.crm.core.beans.Trash.TrashedSubPackage;
import com.hb.crm.core.beans.User;
import com.hb.crm.core.repositories.SubPackageRepository;
import com.hb.crm.core.repositories.TrashedSubPackageRepository;
import com.hb.crm.core.searchBeans.searchPackage;
import com.hb.crm.core.searchRepositories.SearchPackageRepository;
import com.hb.crm.core.util.CustomException;
import com.hb.crm.core.util.UserSessionData;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class SubPackageTrashServiceImpl implements SubPackageTrashService {

    @Autowired
    private TrashedSubPackageRepository trashedSubPackageRepository;

    @Autowired
    private SubPackageRepository subPackageRepository;

    @Autowired
    private SearchPackageRepository searchPackageRepository;

    @Autowired
    private UserSessionData userSessionData;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    @Transactional
    public TrashedSubPackage softDeletePendingSubPackage(String subPackageId, String deletionReason) {
        String currentUserId = userSessionData.getId();
        
        // Find the SubPackage to delete
        SubPackage subPackage = subPackageRepository.findById(subPackageId)
                .orElseThrow(() -> new CustomException(404, "SubPackage not found with ID: " + subPackageId));

        // Verify ownership - user can only delete their own packages
        if (subPackage.get_package() == null || 
            subPackage.get_package().getInfulancer() == null || 
            !currentUserId.equals(subPackage.get_package().getInfulancer().getId())) {
            throw new CustomException(403, "You can only delete your own packages");
        }

        // Verify package status - only pending/draft packages can be deleted by users
        if (subPackage.getPackageStatus() != PackageStatus.draft && 
            subPackage.getPackageStatus() != PackageStatus.pending) {
            throw new CustomException(400, "Only pending or draft packages can be deleted. Posted packages cannot be deleted by users.");
        }

        // Check if already in trash
        if (trashedSubPackageRepository.existsByOriginalSubPackageId(subPackageId)) {
            throw new CustomException(400, "SubPackage is already in trash");
        }

        // Create TrashedSubPackage and copy all data
        TrashedSubPackage trashedSubPackage = new TrashedSubPackage();
        copySubPackageToTrashedSubPackage(subPackage, trashedSubPackage);
        
        // Set trash-specific metadata
        trashedSubPackage.setOriginalSubPackageId(subPackageId);
        trashedSubPackage.setDeletionReason(deletionReason != null ? deletionReason : "Deleted by user");
        trashedSubPackage.setDeletedAt(LocalDateTime.now());

        // Save to trash
        trashedSubPackage = trashedSubPackageRepository.save(trashedSubPackage);

        // Remove from search index
        searchPackageRepository.deleteById(subPackageId);

        // Delete original SubPackage
        subPackageRepository.deleteById(subPackageId);

        return trashedSubPackage;
    }

    @Override
    public PageDto<TrashedSubPackage> getUserTrashedPackages(int page, int size) {
        String currentUserId = userSessionData.getId();
        
        // Create query to filter by user
        Query query = new Query();
        query.addCriteria(Criteria.where("_package.infulancer._id").is(currentUserId));
        query.with(PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "deletedAt")));
        
        List<TrashedSubPackage> trashedPackages = mongoTemplate.find(query, TrashedSubPackage.class);
        long totalCount = mongoTemplate.count(Query.query(Criteria.where("_package.infulancer._id").is(currentUserId)), TrashedSubPackage.class);
        
        return new PageDto<>(size, totalCount, page, trashedPackages);
    }

    @Override
    public PageDto<TrashedSubPackage> getUserTrashedPackagesByType(PackageType packageType, int page, int size) {
        String currentUserId = userSessionData.getId();
        
        Query query = new Query();
        query.addCriteria(Criteria.where("_package.infulancer._id").is(currentUserId)
                .and("packageType").is(packageType));
        query.with(PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "deletedAt")));
        
        List<TrashedSubPackage> trashedPackages = mongoTemplate.find(query, TrashedSubPackage.class);
        long totalCount = mongoTemplate.count(Query.query(Criteria.where("_package.infulancer._id").is(currentUserId)
                .and("packageType").is(packageType)), TrashedSubPackage.class);
        
        return new PageDto<>(size, totalCount, page, trashedPackages);
    }

    @Override
    public PageDto<TrashedSubPackage> searchUserTrashedPackages(String query, int page, int size) {
        String currentUserId = userSessionData.getId();
        
        if (query == null || query.trim().isEmpty()) {
            return getUserTrashedPackages(page, size);
        }

        // For simplicity, using basic text search. You can enhance this with Atlas Search if needed
        Query mongoQuery = new Query();
        mongoQuery.addCriteria(Criteria.where("_package.infulancer._id").is(currentUserId)
                .and("name").regex(query, "i")); // Case-insensitive regex search
        mongoQuery.with(PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "deletedAt")));
        
        List<TrashedSubPackage> trashedPackages = mongoTemplate.find(mongoQuery, TrashedSubPackage.class);
        long totalCount = mongoTemplate.count(Query.query(Criteria.where("_package.infulancer._id").is(currentUserId)
                .and("name").regex(query, "i")), TrashedSubPackage.class);
        
        return new PageDto<>(size, totalCount, page, trashedPackages);
    }

    @Override
    public PageDto<TrashedSubPackage> searchUserTrashedPackages(String query, PackageType packageType, int page, int size) {
        String currentUserId = userSessionData.getId();
        
        if (query == null || query.trim().isEmpty()) {
            return getUserTrashedPackagesByType(packageType, page, size);
        }

        Query mongoQuery = new Query();
        mongoQuery.addCriteria(Criteria.where("_package.infulancer._id").is(currentUserId)
                .and("packageType").is(packageType)
                .and("name").regex(query, "i"));
        mongoQuery.with(PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "deletedAt")));
        
        List<TrashedSubPackage> trashedPackages = mongoTemplate.find(mongoQuery, TrashedSubPackage.class);
        long totalCount = mongoTemplate.count(Query.query(Criteria.where("_package.infulancer._id").is(currentUserId)
                .and("packageType").is(packageType)
                .and("name").regex(query, "i")), TrashedSubPackage.class);
        
        return new PageDto<>(size, totalCount, page, trashedPackages);
    }

    @Override
    @Transactional
    public SubPackage restoreUserSubPackage(String trashedSubPackageId) {
        String currentUserId = userSessionData.getId();
        
        // Find the TrashedSubPackage
        TrashedSubPackage trashedSubPackage = trashedSubPackageRepository.findById(trashedSubPackageId)
                .orElseThrow(() -> new CustomException(404, "TrashedSubPackage not found with ID: " + trashedSubPackageId));

        // Verify ownership
        if (trashedSubPackage.get_package() == null || 
            trashedSubPackage.get_package().getInfulancer() == null || 
            !currentUserId.equals(trashedSubPackage.get_package().getInfulancer().getId())) {
            throw new CustomException(403, "You can only restore your own packages");
        }

        // Check if original ID already exists (conflict resolution)
        if (subPackageRepository.existsById(trashedSubPackage.getOriginalSubPackageId())) {
            throw new CustomException(400, "Cannot restore: A SubPackage with the original ID already exists");
        }

        // Create new SubPackage and copy data back
        SubPackage restoredSubPackage = new SubPackage();
        copyTrashedSubPackageToSubPackage(trashedSubPackage, restoredSubPackage);
        
        // Use original ID for restoration
        restoredSubPackage.setId(trashedSubPackage.getOriginalSubPackageId());

        // Save restored SubPackage
        restoredSubPackage = subPackageRepository.save(restoredSubPackage);

        // Rebuild search package index
        rebuildSearchPackage(restoredSubPackage);

        // Remove from trash
        trashedSubPackageRepository.deleteById(trashedSubPackageId);

        return restoredSubPackage;
    }

    @Override
    public TrashedSubPackage getUserTrashedPackageById(String trashedSubPackageId) {
        String currentUserId = userSessionData.getId();
        
        TrashedSubPackage trashedSubPackage = trashedSubPackageRepository.findById(trashedSubPackageId)
                .orElseThrow(() -> new CustomException(404, "TrashedSubPackage not found with ID: " + trashedSubPackageId));

        // Verify ownership
        if (trashedSubPackage.get_package() == null || 
            trashedSubPackage.get_package().getInfulancer() == null || 
            !currentUserId.equals(trashedSubPackage.get_package().getInfulancer().getId())) {
            throw new CustomException(403, "You can only access your own trashed packages");
        }

        return trashedSubPackage;
    }

    @Override
    @Transactional
    public void permanentlyDeleteUserTrashedPackage(String trashedSubPackageId) {
        String currentUserId = userSessionData.getId();
        
        TrashedSubPackage trashedSubPackage = trashedSubPackageRepository.findById(trashedSubPackageId)
                .orElseThrow(() -> new CustomException(404, "TrashedSubPackage not found with ID: " + trashedSubPackageId));

        // Verify ownership
        if (trashedSubPackage.get_package() == null || 
            trashedSubPackage.get_package().getInfulancer() == null || 
            !currentUserId.equals(trashedSubPackage.get_package().getInfulancer().getId())) {
            throw new CustomException(403, "You can only delete your own trashed packages");
        }

        trashedSubPackageRepository.deleteById(trashedSubPackageId);
    }

    @Override
    public long getUserTrashedPackagesCount(PackageType packageType) {
        String currentUserId = userSessionData.getId();
        
        Query query = new Query();
        query.addCriteria(Criteria.where("_package.infulancer._id").is(currentUserId));
        
        if (packageType != null) {
            query.addCriteria(Criteria.where("packageType").is(packageType));
        }
        
        return mongoTemplate.count(query, TrashedSubPackage.class);
    }

    @Override
    public long getTotalUserTrashedPackagesCount() {
        return getUserTrashedPackagesCount(null);
    }

    @Override
    public boolean isUserSubPackageInTrash(String subPackageId) {
        String currentUserId = userSessionData.getId();
        
        Query query = new Query();
        query.addCriteria(Criteria.where("originalSubPackageId").is(subPackageId)
                .and("_package.infulancer._id").is(currentUserId));
        
        return mongoTemplate.exists(query, TrashedSubPackage.class);
    }

    /**
     * Copy all fields from SubPackage to TrashedSubPackage
     */
    private void copySubPackageToTrashedSubPackage(SubPackage source, TrashedSubPackage target) {
        // Use BeanUtils for basic field copying, then handle special cases
        BeanUtils.copyProperties(source, target, "id"); // Exclude ID to generate new one
        
        // Copy all fields manually to ensure completeness
        target.setName(source.getName());
        target.setTotalPrice(source.getTotalPrice());
        target.setPackageStatus(source.getPackageStatus());
        target.setConfirmFlightprices(source.getConfirmFlightprices());
        target.setPrivateDate(source.isPrivateDate());
        target.setCapacity(source.getCapacity());
        target.setStart(source.getStart());
        target.setEnd(source.getEnd());
        target.setDetails(source.getDetails());
        target.setPrice(source.getPrice());
        target.setRejectionNote(source.getRejectionNote());
        target.setPackageType(source.getPackageType());
        target.setCreationDate(source.getCreationDate());
        target.setUpdateDate(source.getUpdateDate());
        target.setFollowMeDiscount(source.getFollowMeDiscount());
        target.setNumberOfRoom(source.getNumberOfRoom());
        target.setFlights(source.getFlights());
        target.setSubscribeCount(source.getSubscribeCount());
        target.set_package(source.get_package());
        target.setPackagePlaces(source.getPackagePlaces());
        target.setHotels(source.getHotels());
        target.setActivities(source.getActivities());
        target.setSlug(source.getSlug());
        target.setFollowMeSlug(source.getFollowMeSlug());
        target.setRates(source.getRates());
        target.setState(source.getState());
        target.setModifyRequest(source.getModifyRequest());
    }

    /**
     * Copy all fields from TrashedSubPackage back to SubPackage
     */
    private void copyTrashedSubPackageToSubPackage(TrashedSubPackage source, SubPackage target) {
        // Copy all fields back, excluding trash-specific metadata
        target.setName(source.getName());
        target.setTotalPrice(source.getTotalPrice());
        target.setPackageStatus(source.getPackageStatus());
        target.setConfirmFlightprices(source.getConfirmFlightprices());
        target.setPrivateDate(source.isPrivateDate());
        target.setCapacity(source.getCapacity());
        target.setStart(source.getStart());
        target.setEnd(source.getEnd());
        target.setDetails(source.getDetails());
        target.setPrice(source.getPrice());
        target.setRejectionNote(source.getRejectionNote());
        target.setPackageType(source.getPackageType());
        target.setCreationDate(source.getCreationDate());
        target.setUpdateDate(LocalDateTime.now()); // Update the restoration time
        target.setFollowMeDiscount(source.getFollowMeDiscount());
        target.setNumberOfRoom(source.getNumberOfRoom());
        target.setFlights(source.getFlights());
        target.setSubscribeCount(source.getSubscribeCount());
        target.set_package(source.get_package());
        target.setPackagePlaces(source.getPackagePlaces());
        target.setHotels(source.getHotels());
        target.setActivities(source.getActivities());
        target.setSlug(source.getSlug());
        target.setFollowMeSlug(source.getFollowMeSlug());
        target.setRates(source.getRates());
        target.setState(source.getState());
        target.setModifyRequest(source.getModifyRequest());
    }

    /**
     * Rebuild search package index for restored SubPackage
     */
    private void rebuildSearchPackage(SubPackage subPackage) {
        try {
            // Create search package from restored SubPackage
            searchPackage searchPkg = new searchPackage();
            
            // Copy basic fields
            searchPkg.setId(subPackage.getId());
            searchPkg.setName(subPackage.getName());
            searchPkg.setPackageStatus(subPackage.getPackageStatus());
            searchPkg.setPackageType(subPackage.getPackageType());
            searchPkg.setState(subPackage.getState());
            searchPkg.setTotalPrice(subPackage.getTotalPrice());
            searchPkg.setCapacity(subPackage.getCapacity());
            searchPkg.setStart(subPackage.getStart());
            searchPkg.setEnd(subPackage.getEnd());
            searchPkg.setCreationDate(subPackage.getCreationDate());
            searchPkg.setUpdateDate(subPackage.getUpdateDate());
            searchPkg.setSubscribeCount(subPackage.getSubscribeCount());
            searchPkg.setSlug(subPackage.getSlug());
            searchPkg.setFollowMeSlug(subPackage.getFollowMeSlug());
            searchPkg.setPackagePlaces(subPackage.getPackagePlaces());
            
            // Set package reference
            if (subPackage.get_package() != null) {
                searchPkg.setPackageId(subPackage.get_package().getId());
                searchPkg.setInfulancer(subPackage.get_package().getInfulancer());
                searchPkg.setTags(subPackage.get_package().getTags());
                searchPkg.setMoods(subPackage.get_package().getMoods());
                searchPkg.setFromAirport(subPackage.get_package().getFromAirport());
                searchPkg.setToAirport(subPackage.get_package().getToAirport());
                searchPkg.setFromAirportInside(subPackage.get_package().isFromAirportInside());
                searchPkg.setToAirportInside(subPackage.get_package().isToAirportInside());
            }
            
            // Save to search repository
            searchPackageRepository.save(searchPkg);
            
        } catch (Exception e) {
            // Log error but don't fail the restore operation
            System.err.println("Warning: Failed to rebuild search index for restored package " + subPackage.getId() + ": " + e.getMessage());
        }
    }
}
