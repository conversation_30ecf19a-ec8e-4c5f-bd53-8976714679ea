package com.hb.crm.admin.services;

import com.hb.crm.core.Enums.PackageStatus;
import com.hb.crm.core.Enums.PackageType;
import com.hb.crm.core.Enums.State;
import com.hb.crm.core.beans.Package;
import com.hb.crm.core.beans.SubPackage;
import com.hb.crm.core.beans.Trash.TrashedSubPackage;
import com.hb.crm.core.exceptions.CustomException;
import com.hb.crm.core.repositories.SubPackageRepository;
import com.hb.crm.core.repositories.TrashedSubPackageRepository;
import com.hb.crm.core.searchRepositories.SearchPackageRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TrashedSubPackageServiceTest {

    @Mock
    private TrashedSubPackageRepository trashedSubPackageRepository;

    @Mock
    private SubPackageRepository subPackageRepository;

    @Mock
    private SearchPackageRepository searchPackageRepository;

    @InjectMocks
    private TrashedSubPackageServiceImpl trashedSubPackageService;

    private SubPackage testSubPackage;
    private TrashedSubPackage testTrashedSubPackage;

    @BeforeEach
    void setUp() {
        // Create test SubPackage
        testSubPackage = new SubPackage();
        testSubPackage.setId("test-subpackage-id");
        testSubPackage.setName("Test Package");
        testSubPackage.setTotalPrice(new BigDecimal("1000.00"));
        testSubPackage.setPackageStatus(PackageStatus.posted);
        testSubPackage.setPackageType(PackageType.TravelWithMe);
        testSubPackage.setCapacity(10);
        testSubPackage.setStart(LocalDateTime.now().plusDays(1));
        testSubPackage.setEnd(LocalDateTime.now().plusDays(7));
        testSubPackage.setState(State.NotStarted);
        testSubPackage.setCreationDate(LocalDateTime.now().minusDays(1));
        testSubPackage.setUpdateDate(LocalDateTime.now());

        // Create test Package
        Package testPackage = new Package();
        testPackage.setId("test-package-id");
        testPackage.setName("Test Main Package");
        testSubPackage.set_package(testPackage);

        // Create test TrashedSubPackage
        testTrashedSubPackage = new TrashedSubPackage();
        testTrashedSubPackage.setId("test-trashed-id");
        testTrashedSubPackage.setOriginalSubPackageId("test-subpackage-id");
        testTrashedSubPackage.setName("Test Package");
        testTrashedSubPackage.setDeletionReason("Test deletion");
        testTrashedSubPackage.setDeletedAt(LocalDateTime.now());
    }

    @Test
    void testSoftDeleteSubPackage_Success() {
        // Arrange
        when(subPackageRepository.findById("test-subpackage-id")).thenReturn(Optional.of(testSubPackage));
        when(trashedSubPackageRepository.existsByOriginalSubPackageId("test-subpackage-id")).thenReturn(false);
        when(trashedSubPackageRepository.save(any(TrashedSubPackage.class))).thenReturn(testTrashedSubPackage);

        // Act
        TrashedSubPackage result = trashedSubPackageService.softDeleteSubPackage("test-subpackage-id", "Test deletion");

        // Assert
        assertNotNull(result);
        assertEquals("test-trashed-id", result.getId());
        assertEquals("test-subpackage-id", result.getOriginalSubPackageId());
        assertEquals("Test deletion", result.getDeletionReason());

        // Verify interactions
        verify(subPackageRepository).findById("test-subpackage-id");
        verify(trashedSubPackageRepository).existsByOriginalSubPackageId("test-subpackage-id");
        verify(trashedSubPackageRepository).save(any(TrashedSubPackage.class));
        verify(searchPackageRepository).deleteById("test-subpackage-id");
        verify(subPackageRepository).deleteById("test-subpackage-id");
    }

    @Test
    void testSoftDeleteSubPackage_SubPackageNotFound() {
        // Arrange
        when(subPackageRepository.findById("non-existent-id")).thenReturn(Optional.empty());

        // Act & Assert
        CustomException exception = assertThrows(CustomException.class, () -> {
            trashedSubPackageService.softDeleteSubPackage("non-existent-id", "Test deletion");
        });

        assertEquals(404, exception.getStatusCode());
        assertTrue(exception.getMessage().contains("SubPackage not found"));
    }

    @Test
    void testSoftDeleteSubPackage_AlreadyInTrash() {
        // Arrange
        when(subPackageRepository.findById("test-subpackage-id")).thenReturn(Optional.of(testSubPackage));
        when(trashedSubPackageRepository.existsByOriginalSubPackageId("test-subpackage-id")).thenReturn(true);

        // Act & Assert
        CustomException exception = assertThrows(CustomException.class, () -> {
            trashedSubPackageService.softDeleteSubPackage("test-subpackage-id", "Test deletion");
        });

        assertEquals(400, exception.getStatusCode());
        assertTrue(exception.getMessage().contains("already in trash"));
    }

    @Test
    void testRestoreSubPackage_Success() {
        // Arrange
        when(trashedSubPackageRepository.findById("test-trashed-id")).thenReturn(Optional.of(testTrashedSubPackage));
        when(subPackageRepository.existsById("test-subpackage-id")).thenReturn(false);
        when(subPackageRepository.save(any(SubPackage.class))).thenReturn(testSubPackage);

        // Act
        SubPackage result = trashedSubPackageService.restoreSubPackage("test-trashed-id");

        // Assert
        assertNotNull(result);
        assertEquals("test-subpackage-id", result.getId());

        // Verify interactions
        verify(trashedSubPackageRepository).findById("test-trashed-id");
        verify(subPackageRepository).existsById("test-subpackage-id");
        verify(subPackageRepository).save(any(SubPackage.class));
        verify(trashedSubPackageRepository).deleteById("test-trashed-id");
    }

    @Test
    void testRestoreSubPackage_TrashedPackageNotFound() {
        // Arrange
        when(trashedSubPackageRepository.findById("non-existent-id")).thenReturn(Optional.empty());

        // Act & Assert
        CustomException exception = assertThrows(CustomException.class, () -> {
            trashedSubPackageService.restoreSubPackage("non-existent-id");
        });

        assertEquals(404, exception.getStatusCode());
        assertTrue(exception.getMessage().contains("TrashedSubPackage not found"));
    }

    @Test
    void testRestoreSubPackage_ConflictWithExistingId() {
        // Arrange
        when(trashedSubPackageRepository.findById("test-trashed-id")).thenReturn(Optional.of(testTrashedSubPackage));
        when(subPackageRepository.existsById("test-subpackage-id")).thenReturn(true);

        // Act & Assert
        CustomException exception = assertThrows(CustomException.class, () -> {
            trashedSubPackageService.restoreSubPackage("test-trashed-id");
        });

        assertEquals(400, exception.getStatusCode());
        assertTrue(exception.getMessage().contains("Cannot restore"));
    }

    @Test
    void testGetTrashedPackageById_Success() {
        // Arrange
        when(trashedSubPackageRepository.findById("test-trashed-id")).thenReturn(Optional.of(testTrashedSubPackage));

        // Act
        TrashedSubPackage result = trashedSubPackageService.getTrashedPackageById("test-trashed-id");

        // Assert
        assertNotNull(result);
        assertEquals("test-trashed-id", result.getId());
        assertEquals("test-subpackage-id", result.getOriginalSubPackageId());
    }

    @Test
    void testGetTrashedPackageById_NotFound() {
        // Arrange
        when(trashedSubPackageRepository.findById("non-existent-id")).thenReturn(Optional.empty());

        // Act & Assert
        CustomException exception = assertThrows(CustomException.class, () -> {
            trashedSubPackageService.getTrashedPackageById("non-existent-id");
        });

        assertEquals(404, exception.getStatusCode());
        assertTrue(exception.getMessage().contains("TrashedSubPackage not found"));
    }

    @Test
    void testPermanentlyDeleteTrashedPackage_Success() {
        // Arrange
        when(trashedSubPackageRepository.existsById("test-trashed-id")).thenReturn(true);

        // Act
        trashedSubPackageService.permanentlyDeleteTrashedPackage("test-trashed-id");

        // Assert
        verify(trashedSubPackageRepository).existsById("test-trashed-id");
        verify(trashedSubPackageRepository).deleteById("test-trashed-id");
    }

    @Test
    void testPermanentlyDeleteTrashedPackage_NotFound() {
        // Arrange
        when(trashedSubPackageRepository.existsById("non-existent-id")).thenReturn(false);

        // Act & Assert
        CustomException exception = assertThrows(CustomException.class, () -> {
            trashedSubPackageService.permanentlyDeleteTrashedPackage("non-existent-id");
        });

        assertEquals(404, exception.getStatusCode());
        assertTrue(exception.getMessage().contains("TrashedSubPackage not found"));
    }

    @Test
    void testIsSubPackageInTrash() {
        // Arrange
        when(trashedSubPackageRepository.existsByOriginalSubPackageId("test-subpackage-id")).thenReturn(true);
        when(trashedSubPackageRepository.existsByOriginalSubPackageId("non-existent-id")).thenReturn(false);

        // Act & Assert
        assertTrue(trashedSubPackageService.isSubPackageInTrash("test-subpackage-id"));
        assertFalse(trashedSubPackageService.isSubPackageInTrash("non-existent-id"));
    }

    @Test
    void testEmptyTrash() {
        // Act
        trashedSubPackageService.emptyTrash();

        // Assert
        verify(trashedSubPackageRepository).deleteAll();
    }

    @Test
    void testGetTotalTrashedPackagesCount() {
        // Arrange
        when(trashedSubPackageRepository.count()).thenReturn(5L);

        // Act
        long result = trashedSubPackageService.getTotalTrashedPackagesCount();

        // Assert
        assertEquals(5L, result);
        verify(trashedSubPackageRepository).count();
    }

    @Test
    void testGetTrashedPackagesCount_WithPackageType() {
        // Arrange
        when(trashedSubPackageRepository.countByPackageType(PackageType.TravelWithMe)).thenReturn(3L);

        // Act
        long result = trashedSubPackageService.getTrashedPackagesCount(PackageType.TravelWithMe);

        // Assert
        assertEquals(3L, result);
        verify(trashedSubPackageRepository).countByPackageType(PackageType.TravelWithMe);
    }

    @Test
    void testGetTrashedPackagesCount_WithNullPackageType() {
        // Arrange
        when(trashedSubPackageRepository.count()).thenReturn(5L);

        // Act
        long result = trashedSubPackageService.getTrashedPackagesCount(null);

        // Assert
        assertEquals(5L, result);
        verify(trashedSubPackageRepository).count();
    }
}
