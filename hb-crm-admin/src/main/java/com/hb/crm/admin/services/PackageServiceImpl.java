package com.hb.crm.admin.services;

import com.hb.crm.admin.config.CustomHundlerar.CustomException;
import com.hb.crm.admin.config.DateFormater;
import com.hb.crm.admin.config.DateFormatter;
import com.hb.crm.admin.dto.CountDto;
import com.hb.crm.admin.dto.PageDto;
import com.hb.crm.admin.dto.ReferenceModelDto;
import com.hb.crm.admin.dto.keyAndValue;
import com.hb.crm.admin.dto.media.CreateMediaWrapperDto;
import com.hb.crm.admin.dto.packageDto.AlphaPackage;
import com.hb.crm.admin.dto.packageDto.ChangePrice;
import com.hb.crm.admin.dto.packageDto.CreateUpdatePackageDto;
import com.hb.crm.admin.dto.packageDto.PackageDto;
import com.hb.crm.admin.services.interfaces.PackageService;
import com.hb.crm.admin.services.interfaces.QueryNormalizeService;
import com.hb.crm.admin.services.interfaces.TagService;
import com.hb.crm.core.CombinedKeys.SubscribeKey;
import com.hb.crm.core.Enums.*;
import com.hb.crm.core.beans.Flight.Flight;
import com.hb.crm.core.beans.Package;
import com.hb.crm.core.beans.*;
import com.hb.crm.core.beans.PackagePlaces.*;
import com.hb.crm.core.dtos.CreateMediaDto;
import com.hb.crm.core.dtos.CreateMediaWrapperWithMediaDto;
import com.hb.crm.core.dtos.PackageEditRequest;
import com.hb.crm.core.dtos.chat.response.SimpleGroupConversationResponseDto;
import com.hb.crm.core.repositories.*;
import com.hb.crm.core.repositories.chat.GroupChatMessageRepository;
import com.hb.crm.core.repositories.chat.GroupConversationRepository;
import com.hb.crm.core.searchBeans.*;
import com.hb.crm.core.searchRepositories.ReactionSearchRepository;
import com.hb.crm.core.searchRepositories.SearchPackageRepository;
import com.hb.crm.core.searchRepositories.SearchUserRepository;
import com.hb.crm.core.services.chat.ChatMessageService;
import com.hb.crm.core.services.interfaces.NotificationService;
import com.hb.crm.core.util.ApplicationUtil;
import org.bson.types.ObjectId;
import org.jetbrains.annotations.NotNull;
import org.joda.time.DateTime;
import org.modelmapper.ModelMapper;
import org.modelmapper.TypeMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
public class PackageServiceImpl implements PackageService {

    private static final int ALL_LIMIT = 99999;
    private final PackageRepository packageRepository;
    private final SubPackageRepository subPackageRepository;
    private final ActivityCloneRepository activityCloneRepository;
    private final ActivityRepository activityRepository;
    private final MongoTemplate mongoTemplate;
    private final MongoTemplate mongoTemplate2;
    private final ModelMapper modelMapper;
    private final PlaceServiceImpl placeService;
    private final MediaRepository mediaRepository;
    private final TagService tagService;
    private final QueryNormalizeService queryNormalizeService;
    private final SearchPackageRepository searchPackageRepository;
    private final GroupChatMessageRepository groupChatMessageRepository;
    private final GroupConversationRepository groupConversationRepository;
    private final ChatMessageService chatMessageService;
    private final UserRepository userRepository;
    private final NotificationService notificationService;
    private final ReactionSearchRepository reactionSearchRepository;
    private final SearchUserRepository searchUserRepository;
    private final SubscribeRepository subscribeRepository;
    private Logger logger = LoggerFactory.getLogger(PackageServiceImpl.class);

    public PackageServiceImpl(PackageRepository packageRepository,
                              SubPackageRepository subPackageRepository,
                              ActivityCloneRepository activityCloneRepository,
                              ActivityRepository activityRepository,
                              @Qualifier("mongoTemplate1") MongoTemplate mongoTemplate,
                              @Qualifier("mongoTemplate2") MongoTemplate mongoTemplate2,
                              ModelMapper modelMapper, PlaceServiceImpl placeService,
                              TagRepository tagRepository, MediaRepository mediaRepository,
                              TagService tagService, QueryNormalizeService queryNormalizeService,
                              SearchPackageRepository searchPackageRepository,
                              GroupChatMessageRepository groupChatMessageRepository,
                              GroupConversationRepository groupConversationRepository,
                              ChatMessageService chatMessageService,
                              UserRepository userRepository, NotificationService notificationService, ReactionSearchRepository reactionSearchRepository, SearchUserRepository searchUserRepository, SubscribeRepository subscribeRepository) {
        this.packageRepository = packageRepository;
        this.subPackageRepository = subPackageRepository;
        this.activityCloneRepository = activityCloneRepository;
        this.activityRepository = activityRepository;
        this.mongoTemplate = mongoTemplate;
        this.mongoTemplate2 = mongoTemplate2;
        this.modelMapper = modelMapper;
        this.placeService = placeService;
        this.mediaRepository = mediaRepository;
        this.tagService = tagService;
        this.queryNormalizeService = queryNormalizeService;
        this.searchPackageRepository = searchPackageRepository;
        this.groupChatMessageRepository = groupChatMessageRepository;
        this.groupConversationRepository = groupConversationRepository;
        this.chatMessageService = chatMessageService;
        this.userRepository = userRepository;
        this.notificationService = notificationService;
        this.reactionSearchRepository = reactionSearchRepository;
        this.searchUserRepository = searchUserRepository;
        this.subscribeRepository = subscribeRepository;
     }

    @Override
    public List<AlphaPackage> search() {

        final Criteria searchQuery = new Criteria();
        searchQuery.and("packageStatus").is(PackageStatus.posted)
                .and("packageType").is(PackageType.TravelWithMe);
        Aggregation aggregation = queryNormalizeService.getAlphaPackageList(searchQuery);
        List<AlphaPackage> Packages = mongoTemplate.aggregate(aggregation, "subPackage", SubPackage.class).getMappedResults()
                .stream().map(this::convertToDto)
                .map(PackageDto::get_package).distinct()
                .collect(Collectors.toList());

        for (AlphaPackage aPackage : Packages) {
            var conversation = groupConversationRepository.findByPackageId(aPackage.getId());
            if (conversation.isEmpty())
                continue;
            aPackage.setConversation(modelMapper.map(conversation.getFirst(),
                    SimpleGroupConversationResponseDto.class));
        }

        return Packages;
    }

    @Override
    public PageDto<PackageDto> search(Map<String, Object> obj, int page, int limit, String direction, String propertyName) {
        if (page < 0) {
            limit = ALL_LIMIT;
            page = 0;
        }
        PageDto<PackageDto> pageDto = new PageDto<>();
        final Pageable pageable = ApplicationUtil.createPageRequest(page, limit, propertyName, direction.toUpperCase());
        final Criteria searchQuery = createSearchSpecification(obj);
        searchQuery.and("packageType").is(PackageType.TravelWithMe);
        Aggregation aggregation = queryNormalizeService.getPackage(searchQuery, pageable, false);
        List<PackageDto> aggregationResults = mongoTemplate.aggregate(aggregation, "subPackage", SubPackage.class).getMappedResults().stream().map(this::convertToDto).collect(Collectors.toList());

        // fetch the group conversation for each package
        for (PackageDto aggregationResult : aggregationResults) {
            var conversation = groupConversationRepository.findByPackageId(aggregationResult.getId());
            if (conversation.isEmpty())
                continue;
            aggregationResult.setConversation(modelMapper.map(conversation.getFirst(),
                    SimpleGroupConversationResponseDto.class));
        }

        pageDto.setItems(aggregationResults);
        Aggregation countAggregation = queryNormalizeService.getPackage(searchQuery, pageable, true);

        AggregationResults<CountDto> countResults = mongoTemplate.aggregate(countAggregation, "subPackage", CountDto.class);
        long totalCount = countResults.getUniqueMappedResult() != null ? countResults.getUniqueMappedResult().getCount() : 0;
        pageDto.setTotalNoOfItems(totalCount);
        return pageDto;
    }

    @Override
    @Transactional
    public void acceptModification(String packageId) {
        SubPackage subPackage = subPackageRepository.findById(packageId)
                .orElseThrow(() -> new CustomException(404, "SubPackage not found"));

        Package pkg = packageRepository.findById(subPackage.get_package().getId())
                .orElseThrow(() -> new CustomException(404, "Package not found"));

        if (pkg.getInfulancer() != null) {
            User influencer = userRepository.findById(pkg.getInfulancer().getId())
                    .orElseThrow(() -> new CustomException(404, "User not found"));
            pkg.setInfulancer(influencer);
        }

        subPackage.set_package(pkg);

        List<PackageEditRequest> modifyRequests = subPackage.getModifyRequest();
        if(modifyRequests == null || modifyRequests.isEmpty())
            throw new com.hb.crm.core.exceptions.CustomException(400, "This package has no draft to accept!");

        // Find the non-approved request to accept
        PackageEditRequest nonApprovedRequest = modifyRequests.stream()
                .filter(request -> !request.isApproved())
                .findFirst()
                .orElseThrow(() -> new com.hb.crm.core.exceptions.CustomException(400, "No pending modification to accept!"));

        // Set the modify request as approved
        nonApprovedRequest.setApproved(true);

        this.updatePackageFromRequest(nonApprovedRequest.getModifications(), subPackage);
        subPackageRepository.save(subPackage);
    }

    @Override
    @Transactional
    public void modifyModification(String packageId, com.hb.crm.core.dtos.CreateUpdatePackageDto obj) {
        SubPackage subPackage = subPackageRepository.findById(packageId)
                .orElseThrow(() -> new CustomException(404, "Package not found!"));

        List<PackageEditRequest> modifyRequests = subPackage.getModifyRequest();
        if(modifyRequests == null || modifyRequests.isEmpty())
            throw new com.hb.crm.core.exceptions.CustomException(400, "This package has no draft to modify!");

        // Find the non-approved request to modify
        PackageEditRequest nonApprovedRequest = modifyRequests.stream()
                .filter(request -> !request.isApproved())
                .findFirst()
                .orElseThrow(() -> new com.hb.crm.core.exceptions.CustomException(400, "No pending modification to modify!"));

        nonApprovedRequest.setModifications(obj);
        subPackageRepository.save(subPackage);
    }

    @Override
    public PageDto<PackageDto> searchFollowMe(Map<String, Object> obj, int page, int limit, String direction, String propertyName) {
        if (page < 0) {
            limit = ALL_LIMIT;
            page = 0;
        }
        PageDto<PackageDto> pageDto = new PageDto<>();
        final Pageable pageable = ApplicationUtil.createPageRequest(page, limit, propertyName, direction.toUpperCase());
        final Criteria searchQuery = createSearchSpecification(obj);
        searchQuery.and("packageType").is(PackageType.FollowMe);
        Aggregation aggregation = queryNormalizeService.getPackage(searchQuery, pageable, false);
        List<PackageDto> aggregationResults = mongoTemplate.aggregate(aggregation, "subPackage", SubPackage.class).getMappedResults().stream().map(this::convertToDto).collect(Collectors.toList());

        // fetch the group conversation for each package
        for (PackageDto aggregationResult : aggregationResults) {
            var conversation = groupConversationRepository.findByPackageId(aggregationResult.getId());
            if (conversation.isEmpty())
                continue;
            aggregationResult.setConversation(modelMapper.map(conversation.getFirst(),
                    SimpleGroupConversationResponseDto.class));
        }

        pageDto.setItems(aggregationResults);
        Aggregation countAggregation = queryNormalizeService.getPackage(searchQuery, pageable, true);
        AggregationResults<CountDto> countResults = mongoTemplate.aggregate(countAggregation, "subPackage", CountDto.class);
        long totalCount = countResults.getUniqueMappedResult() != null ? countResults.getUniqueMappedResult().getCount() : 0;
        pageDto.setTotalNoOfItems(totalCount);
        return pageDto;
    }

    @Override
    public PageDto<PackageDto> searchTodoList(Map<String, Object> obj, int page, int limit) {
        if (page < 0) {
            limit = ALL_LIMIT;
            page = 0;
        }
        PageDto<PackageDto> pageDto = new PageDto<>();
        final Pageable pageable = PageRequest.of(page, limit);
        final Query searchQuery = Query.query(createSearchSpecification(obj));
        searchQuery.with(Sort.by(Sort.Direction.DESC, "creationDate"));
        final Query filterQuery = Query.query(createSearchSpecification(obj));
        Criteria criteria = new Criteria().orOperator(Criteria.where("packageStatus").is(PackageStatus.returned),
                Criteria.where("packageStatus").is(PackageStatus.draft));

        searchQuery.addCriteria(criteria);
        filterQuery.addCriteria(criteria);
        searchQuery.with(pageable);
        List<PackageDto> users = mongoTemplate.find(searchQuery, SubPackage.class).stream().map(z -> convertToDto(z)).collect(Collectors.toList());

        // fetch the group conversation for each package
        for (PackageDto aggregationResult : users) {
            var conversation = groupConversationRepository.findByPackageId(aggregationResult.getId());
            if (conversation.isEmpty())
                continue;
            aggregationResult.setConversation(modelMapper.map(conversation.getFirst(),
                    SimpleGroupConversationResponseDto.class));
        }

        long count = mongoTemplate.count(filterQuery, Package.class);
        pageDto.setTotalNoOfItems(count);
        pageDto.setItems(users);
        return pageDto;

    }


    @Override
    @Transactional(readOnly = true)
    public PackageDto getPackageById(String id) {

        if (id == null) {
            return null;
        }
        final Optional<SubPackage> byId = subPackageRepository.findById(id);
        if (byId.isEmpty()) {
            return null;
        }

        var conversation = groupConversationRepository.findByPackageId(id);
        if (conversation.isEmpty())
            return convertToDto(byId.get());

        var result = convertToDto(byId.get());
        result.setConversation(modelMapper.map(conversation.getFirst(), SimpleGroupConversationResponseDto.class));

        return result;
    }

    @Transactional
    @Override
    public void acceptPackage(String id, String notes, PackageStatus accept) {
        final Optional<SubPackage> byId = subPackageRepository.findById(id);
        if (byId.isPresent()) {
            SubPackage _package = byId.get();
            _package.setPackageStatus(accept);
            _package.setRejectionNote(notes);
            subPackageRepository.save(_package);
            if (_package.get_package() != null) {
                _package.set_package(packageRepository.findById(_package.get_package().getId()).orElse(null));
            }

            searchPackage searchPackage = convertPackageToSearchPackage(_package);
            searchPackage.setPackagePlaces(_package.getPackagePlaces());
            searchPackageRepository.save(searchPackage);

            try {
                NotificationType type = accept == PackageStatus.accepted ? NotificationType.AcceptPackage : NotificationType.PostPackage;

                if (notes != null && accept == PackageStatus.draft)
                    type = NotificationType.RejectPackage;

                notificationService.sendAndStoreNotification(
                        _package.getId(),
                        type,
                        _package.get_package().getInfulancer(),
                        List.of(_package),
                        _package.get_package().getMedias()
                                .stream()
                                .filter(MediaWrapper::isMainImage)
                                .findFirst().orElse(new MediaWrapper())
                                .getUrl(),
                        null,
                        NotificationEntityType.PACKAGE,
                        _package.get_package().getInfulancer(),
                        _package.getSlug(),
                        _package.get_package().getInfulancer().getUsername(), null);
            } catch (Exception e) {
                System.out.println(e.getMessage());
                logger.error(e.getMessage());
            }
        }
    }

    @Override
    public void setAvailableFollowMe(String id, int discount, LocalDate AvailableFrom) {
        final Optional<SubPackage> byId = subPackageRepository.findById(id);
        if (byId.isPresent()) {
            var _Package = packageRepository.findById(byId.get().get_package().getId()).orElse(null);
            SubPackage subPackage = byId.get();
            subPackage.set_package(_Package);
            subPackage.get_package().setAvailableForFollowMe(true);
            subPackage.get_package().setFollowMeDiscount(discount);
            subPackage.get_package().setAvailableFrom(AvailableFrom);
            packageRepository.save(subPackage.get_package());
            subPackageRepository.save(subPackage);
            searchPackage searchPackage = convertPackageToSearchPackage(subPackage);
            searchPackageRepository.save(searchPackage);
        }
    }

    /**
     * Updates or creates a package and its associated sub-package based on the provided DTO.
     * This method handles media processing, tag management, date formatting, and various package properties.
     *
     * @param obj The DTO containing package update/creation information
     * @return The updated or created SubPackage entity
     * @throws CustomException if multiple main images are selected
     */
    @Override
    public SubPackage update(CreateUpdatePackageDto obj) {

        boolean newPackage = false;
        // Validate and process media attachments
        if (obj.getMedias() != null) {
            // Ensure only one main image is selected
            List<CreateMediaWrapperDto> wrappers = obj.getMedias().stream().filter(CreateMediaWrapperDto::isMainImage).toList();
            if (wrappers.size() > 1) {
                throw new CustomException(500, "Only one Main Image Should be Selected");
            }
            // Generate media for each attachment
            for (CreateMediaWrapperDto media : obj.getMedias())
                generateMedia(media);
        }
        // Process and validate tags if present
        if (obj.getTags() != null)
            obj.setTags(tagService.checkNewTags(obj.getTags()));
        // Format dates to consistent format
        obj.setStart(DateFormater.formatLocalDateTime(obj.getStart()));
        obj.setEnd(DateFormater.formatLocalDateTime(obj.getEnd()));
        if (obj.getSlug() != null) {
            obj.setSlug(generateSlug(obj.getSlug()));

        } else {
            obj.setSlug(generateSlug(obj.getName()));
        }
        Package _package = convertToEntity(obj);
        SubPackage _sub = convertToSubEntity(obj);

        subPackageRepository.findById(obj.getId()).ifPresent(subPackage -> {
            _sub.setModifyRequest(subPackage.getModifyRequest());
        });

        User user = userRepository.findById(_package.getInfulancer().getId()).orElse(null);
        _package.setInfulancer(user);
        // Set initial status for new sub-packages
        if (_sub.getId() == null)
            _sub.setPackageStatus(PackageStatus.draft);
        if (obj.getId() == null) {

            // Handle new package creation
            _sub.setCreationDate((DateFormater.formatLocalDateTime(LocalDateTime.now())));
            _sub.setUpdateDate((DateFormater.formatLocalDateTime(LocalDateTime.now())));
            newPackage = true;
        } else {
            // Handle package update
            SubPackage sPackage = subPackageRepository.findById(obj.getId()).get();
            if (!obj.getSlug().equals(sPackage.getSlug())) {
                sPackage.setSlug(generateSlug(obj.getSlug()));
                _sub.setSlug(generateSlug(obj.getSlug()));
            } else {
                sPackage.setSlug(slugify(obj.getSlug()));
                _sub.setSlug(slugify(obj.getSlug()));
            }

            // Preserve original creation date or set new one
            if (sPackage.getCreationDate() != null)
                _sub.setCreationDate(sPackage.getCreationDate());
            else
                _sub.setCreationDate((DateFormater.formatLocalDateTime(LocalDateTime.now())));
            // Maintain existing package properties
            _sub.setPackageStatus(sPackage.getPackageStatus());
            _sub.set_package(_package);
            // omar for god sake test your code
            // Send notification to the influencer
            // sendNotificationToInfluencerWithUpdatePackage(_sub.get_package().getInfulancer(), _sub,
            // List.of(_sub));
            _sub.setRejectionNote(sPackage.getRejectionNote());
            _sub.setState(State.NotStarted);
            _sub.setSubscribeCount(sPackage.getSubscribeCount());
            _package.setRates(sPackage.get_package().getRates());
            _package.setId(sPackage.get_package().getId());
            _package.setAvailableForFollowMe(sPackage.get_package().isAvailableForFollowMe());
            _package.setAvailableFrom(sPackage.get_package().getAvailableFrom());
            _package.setFollowMeDiscount(sPackage.get_package().getFollowMeDiscount());
        }

        // Process package places if provided
        if (obj.getPackagePlaces() != null)
            addPlacesToPackage(_package, obj.getPackagePlaces());

        // Update package relationships and timestamps
        _sub.setUpdateDate((DateFormater.formatLocalDateTime(LocalDateTime.now())));
        _sub.set_package(_package);

        // Handle brochure removal if null
        if (obj.getBrochure() == null)
            _package.setBrochure(null);

        // Extract media IDs for bulk fetching
        List<String> ids = _package.getMedias()
                .stream()
                .map(MediaWrapper::getMedia)
                .map(Media::getId)
                .toList();

        // Update media user associations
        List<Media> medias = mediaRepository.findAllById(ids);
        medias.forEach(media -> {
                    media.setUser(user);
                    media.set_package(_package);
                }
        );
        mediaRepository.saveAll(medias);

        // Create efficient media lookup map
        Map<String, Media> mediaMap = medias.stream()
                .collect(Collectors.toMap(Media::getId, Function.identity()));

        // Update media references with full media objects
        for (MediaWrapper mediaWrapper : _package.getMedias()) {
            String mediaId = mediaWrapper.getMedia().getId();
            mediaWrapper.setMedia(mediaMap.get(mediaId));
        }
        boolean checkMain = _package.getMedias().stream().anyMatch(MediaWrapper::isMainImage);
        if (!checkMain && !_package.getMedias().isEmpty()) {
            List<MediaWrapper> mediaWrappers = _package.getMedias();
            mediaWrappers.getFirst().setMainImage(true);
            _package.setMedias(mediaWrappers);
        }

        // Save all entities and update search index
        packageRepository.save(_package);
        var savedPackage = subPackageRepository.save(_sub);
        searchPackage searchPackage = convertPackageToSearchPackage(_sub);
        searchPackage.setMedias(mapToMediaSearch(_package.getMedias()));
        searchPackage.setTags(_package.getTags());
        searchPackage.setPackagePlaces(_sub.getPackagePlaces());
        searchPackageRepository.save(searchPackage);
        PackageType packageType = searchPackage.getPackageType().equals(PackageType.FollowMe) ? PackageType.TravelWithMe : PackageType.FollowMe;
        searchPackage OtherPackage = searchPackageRepository.findByPackageIdAndPackageType(_package.getId(), packageType).orElse(null);
        if (OtherPackage != null) {
            OtherPackage.setMedias(searchPackage.getMedias());
            OtherPackage.setTags(searchPackage.getTags());
            OtherPackage.setMoods(searchPackage.getMoods());
            searchPackageRepository.save(OtherPackage);
        }
        if (newPackage) {
            SubscribePackage(savedPackage.getId(), user.getId());

            // Create group conversation for the package
            chatMessageService.createGroupConversation(savedPackage.getId());

            // Send notification to users that have the same moods as the package
            var packageMoods = savedPackage.get_package().getMoods();
            List<User> matchedMoodsUsers = userRepository.findByMoodsIn(packageMoods);

            var packageImageMedia = savedPackage.get_package().getMedias() != null
                    && !savedPackage.get_package().getMedias().isEmpty() ?
                    savedPackage.get_package().getMedias()
                            .stream()
                            .filter(MediaWrapper::isMainImage)
                            .toList()
                            .getFirst()
                    : null;

            String packageImageMediaUrl = null;
            if (packageImageMedia != null)
                packageImageMediaUrl = packageImageMedia.getUrl();


            for (User mathcedUser : matchedMoodsUsers) {
                if (mathcedUser.getFcmTokens() != null && !mathcedUser.getFcmTokens().isEmpty()) {
                    notificationService.sendAndStoreNotification(
                            savedPackage.getId(),
                            NotificationType.MatchedMoodsNewPackage,
                            mathcedUser,
                            List.of(savedPackage, savedPackage.get_package().getInfulancer()),
                            savedPackage.get_package().getInfulancer().getProfileImage(),
                            packageImageMediaUrl,
                            NotificationEntityType.PACKAGE,
                            savedPackage.get_package().getInfulancer(),
                            savedPackage.getSlug(),
                            savedPackage.get_package().getInfulancer().getUsername(), null);
                }
            }

            // Find the user that follows the influencer of the package and send them notification.
            var packageInfluencerFollowers = findFollowers(savedPackage.get_package().getInfulancer().getId());

            for (User follower : packageInfluencerFollowers) {
                notificationService.sendAndStoreNotification(
                        savedPackage.getId(),
                        NotificationType.FollowedInfluencerNewPackage,
                        follower,
                        List.of(savedPackage, savedPackage.get_package().getInfulancer()),
                        savedPackage.get_package().getInfulancer().getProfileImage(),
                        packageImageMediaUrl,
                        NotificationEntityType.PACKAGE,
                        savedPackage.get_package().getInfulancer(),
                        savedPackage.getSlug(),
                        savedPackage.get_package().getInfulancer().getUsername(), null);
            }

//            // Send notification to the influencer
            sendNotificationToInfluencerWithCreatePackage(savedPackage.get_package().getInfulancer(), savedPackage,
                    List.of(savedPackage), packageImageMediaUrl);
        }

        return _sub;
    }

    public void SubscribePackage(String PackageId, String userId) {
        SubscribeKey SubscribeKey = new SubscribeKey(new User(userId), new SubPackage(PackageId));
        Subscribe subscribe = mongoTemplate.findById(SubscribeKey, Subscribe.class);

        if (subscribe == null) {
            subscribe = new Subscribe();
            subscribe.setId(SubscribeKey);
            subscribe.setStatus(SubscribeStatus.Influencer);
            subscribeRepository.save(subscribe);
        }
    }

    @Override
    public void Clone(CreateUpdatePackageDto obj) {
        Optional<SubPackage> opPackage = subPackageRepository.findById(obj.getId());
        if (opPackage.isEmpty() || opPackage.get().get_package() == null)
            return;

        Optional<Package> packageOptional  =packageRepository.findById( opPackage.get().get_package().getId());
        SubPackage _sub = opPackage.get();
        if(packageOptional.isEmpty()) {
            return;
        }
        var _package = packageOptional.get();
        // Check if there is an existing FollowMe subpackage for this package
        List<SubPackage> followMePackages = subPackageRepository.existsByPackageAndPackageType(_package.getId(), PackageType.FollowMe);
        if (!followMePackages.isEmpty() ) {
            SubPackage subPackage = followMePackages.getFirst();
            if(obj.getId()==null){
                throw new IllegalStateException("This package already has a FollowMe subpackage.");

            }else if(!obj.getId().equals(subPackage.getId())){
                throw new IllegalStateException("This package already has a FollowMe subpackage.");

            }
        }

        SubPackage _subConv = convertToSubEntity(obj);

        _sub.setPackageType(PackageType.FollowMe);
        _sub.setCapacity(_subConv.getCapacity());
        _sub.setPrice(_subConv.getPrice());
        _sub.setTotalPrice(_subConv.getTotalPrice());
        _sub.set_package(_package);
        _sub.setState(State.NotStarted);
        _sub.setPackageStatus(PackageStatus.posted);

        // Send notification to the influencer
        //sendNotificationToInfluencerWithUpdatePackage(_sub.get_package().getInfulancer(), _sub);

        if(obj.getId()!=null){
            String slug = _package.getSlug() + "-follow-me";
            _sub.setFollowMeSlug(generateSlug(slug));
            _sub.setSlug(_package.getSlug());
            _package.setFollowMeSlug(slug);
            subPackageRepository.save(_sub);
            packageRepository.save(_package);
            searchPackage searchPackage = convertPackageToSearchPackage(_sub);
            searchPackage.setFollowMeSlug(slug);
            searchPackage.setMedias(mapToMediaSearch(_package.getMedias()));
            searchPackage.setTags(_package.getTags());
            syncPackage(_package);
            searchPackageRepository.save(searchPackage);
        }else {
            _sub.setId(null);
            String slug = _package.getSlug() + "-follow-me";
            _sub.setFollowMeSlug(generateSlug(slug));
            _sub.setSlug(_package.getSlug());
            _package.setFollowMeSlug(slug);
            packageRepository.save(_package);
            subPackageRepository.save(_sub);
            searchPackage searchPackage = convertPackageToSearchPackage(_sub);
            searchPackage.setFollowMeSlug(slug);
            searchPackage.setMedias(mapToMediaSearch(_package.getMedias()));
            searchPackage.setTags(_package.getTags());
            syncPackage(_package);
            searchPackageRepository.save(searchPackage);
        }


    }


    public void syncPackage(Package _package) {
        Query query = new Query(Criteria.where("media.media._package._id").is(new ObjectId(_package.getId())));
        Query query2 = new Query(Criteria.where("media.media._package._id").is( new ObjectId(_package.getId())));
        Query query3 = new Query(Criteria.where("Package._id").is(new ObjectId(_package.getId())));

        Update update = new Update()
                .set("media.$[].media._package.name", _package.getName())
                .set("media.$[].media._package.slug", _package.getSlug())
                .set("media.$[].media._package.followMeSlug", _package.getFollowMeSlug())
                .set("media.$[].media._package.description", _package.getDescription());


        Update updatePackage = new Update()
                .set("medias.$[].media._package.name", _package.getName())
                .set("medias.$[].media._package.slug", _package.getSlug())
                .set("medias.$[].media._package.followMeSlug", _package.getFollowMeSlug())
                .set("medias.$[].media._package.description", _package.getDescription());

        Update updatePostAndStory = new Update()
                .set("Package.name", _package.getName())
                .set("Package.slug", _package.getSlug())
                .set("Package.followMeSlug", _package.getFollowMeSlug())
                .set("Package.description", _package.getDescription());

        mongoTemplate2.updateMulti(query, update, searchPost.class);
        mongoTemplate2.updateMulti(query2, updatePackage, searchPackage.class);
        mongoTemplate2.updateMulti(query3, updatePostAndStory, searchPost.class);
        mongoTemplate2.updateMulti(query3, updatePostAndStory, searchStory.class);
    }



    private void generateMedia(CreateMediaWrapperDto media) {
        if (media.getMedia() == null || media.getMedia().getId() == null || media.getMedia().getId().isEmpty()) {
            Media newMedia = new Media();
            newMedia.setTitle(media.getCaption());
            newMedia.setCreationDate(DateTime.now().toDate());
            String category = media.getUrl().split("/")[1];
            ImageCategory imageCategory = ImageCategory.valueOf(category);
            newMedia.setImageCategory(imageCategory);
            if (media.getType() == null) {
                if (isImageFile(media.getUrl())) {
                    media.setType(MediaType.image);
                    newMedia.setSource(media.getUrl());
                } else if (isVideoFile(media.getUrl())) {
                    media.setType(MediaType.video);
                    newMedia.setVideoUrl(media.getUrl());
                } else {
                    throw new RuntimeException("invalid media type");
                }
            } else {
                if (isImageFile(media.getUrl())) {
                    newMedia.setSource(media.getUrl());
                } else if (isVideoFile(media.getUrl())) {
                    newMedia.setVideoUrl(media.getUrl());
                } else {
                    throw new RuntimeException("invalid media type");
                }
            }
            newMedia.setMediaType(media.getType());

            if (media.getType() == MediaType.video && media.getVideoSize() != null) {
                newMedia.setVideoSize(media.getVideoSize());
            }


            mediaRepository.save(newMedia);
            ReferenceModelDto referenceModel = new ReferenceModelDto();
            referenceModel.setId(newMedia.getId());
            media.setMedia(referenceModel);
        }
    }

    private PackageDto convertToDto(SubPackage _package) {
        PackageDto packageDto = modelMapper.map(_package, PackageDto.class);
        return packageDto;
    }

    @Override
    public void ClearPackages() {
        packageRepository.deleteAll();
    }

    @Override
    public ChangePrice ChangeTripPrices(ChangePrice changePriceDto) {
        Optional<SubPackage> sub = subPackageRepository.findById(changePriceDto.getPackageId());
        if (sub.isEmpty())
            return null;
        SubPackage subPackage = sub.get();
        List<Flight> flights = subPackage.getFlights();
        Optional<Flight> flight = flights.stream().filter(z -> z.getSearchIdentifier()
                .equals(changePriceDto.getSearchIdentifier())).findAny();
        if (flight.isEmpty())
            return null;
        int index = flights.indexOf(flight.get());
        Flight updatedFlight = flight.get();
        updatedFlight.setEconomyPrice(changePriceDto.getEconomyPrice());
        updatedFlight.setBusinessPrice(changePriceDto.getBusinessPrice());
        updatedFlight.setChildEconomyPrice(changePriceDto.getChildEconomyPrice());
        updatedFlight.setChildBusinessPrice(changePriceDto.getChildEconomyPrice());
        updatedFlight.setPath(changePriceDto.getPath());
        flights.remove(index);
        flights.add(index, updatedFlight);
        subPackage.setFlights(flights);
        subPackageRepository.save(subPackage);
        searchPackage searchPackage = convertPackageToSearchPackage(subPackage);
        searchPackageRepository.save(searchPackage);
        return changePriceDto;
    }

    @Override
    public boolean isSlugUnique(String slug) {
        return !subPackageRepository.existsBySlugIgnoreCase(slug);
    }

    @Override
    public PageDto<PackageDto> getPendingPackages(String query, int page, int size) {
        PageDto<PackageDto> pageDto = new PageDto<>();
        final Pageable pageable = PageRequest.of(page, size);

        // Create base criteria for pending (draft) packages
        Criteria baseCriteria = Criteria.where("packageStatus").is(PackageStatus.draft);

        Query searchQuery = new Query(baseCriteria);
        Query countQuery = new Query(baseCriteria);

        // Add fuzzy search if query is provided
        if (query != null && !query.trim().isEmpty()) {
            String searchTerm = query.trim();
            // Create fuzzy search criteria across multiple fields
            Criteria searchCriteria = new Criteria().orOperator(
                    Criteria.where("name").regex(searchTerm, "i"),
                    Criteria.where("description").regex(searchTerm, "i"),
                    Criteria.where("slug").regex(searchTerm, "i")
            );

            searchQuery.addCriteria(searchCriteria);
            countQuery.addCriteria(searchCriteria);
        }

        // Add sorting and pagination
        searchQuery.with(Sort.by(Sort.Direction.DESC, "creationDate"));
        searchQuery.with(pageable);

        // Execute queries
        List<PackageDto> packages = mongoTemplate.find(searchQuery, SubPackage.class).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
        long count = mongoTemplate.count(countQuery, SubPackage.class);

        // Set pagination info
        pageDto.setTotalNoOfItems(count);
        pageDto.setItems(packages);
        pageDto.setItemsPerPage(size);
        pageDto.setPageNumber(page);

        return pageDto;
    }

    public String generateSlug(String name) {
        String baseSlug = slugify(name); // Convert name to URL-friendly slug
        String slug = baseSlug;
        int counter = 1;

        while (!isSlugUnique(slug)) {
            slug = baseSlug + "-" + counter;
            counter++;
        }

        return slug;
    }

    public String slugify(@NotNull String input) {
        return input.toLowerCase()
                .replaceAll("[^a-z0-9\\s-]", "")   // remove special characters
                .replaceAll("\\s+", "-")           // replace spaces with hyphens
                .replaceAll("-+", "-")             // replace multiple hyphens
                .replaceAll("^-|-$", "");           // trim hyphens from start/end
    }

    @Override
    public void delete(String id) {
        SubPackage sPackage = subPackageRepository.findById(id).orElseThrow(() -> new CustomException(404, "Package now found"));
        if (sPackage.get_package() != null) {
            List<SubPackage> OthersPackage = subPackageRepository.findBy_package_Id(sPackage.getId());
            if (OthersPackage.isEmpty()) {
                packageRepository.deleteById(sPackage.getId());
            }
        }
        subPackageRepository.delete(sPackage);
        searchPackageRepository.deleteById(sPackage.getId());

    }

    public void deleteFollowMe(String id) {
        // Find the specific FollowMe package by ID
        SubPackage followMeSub = subPackageRepository.findById(id)
                .orElseThrow(() -> new CustomException(404, "FollowMe package not found"));
        if (followMeSub.get_package() != null) {
            List<SubPackage> OthersPackage = subPackageRepository.findBy_package_Id(followMeSub.getId());
            if (OthersPackage.isEmpty()) {
                packageRepository.deleteById(followMeSub.getId());
            }
        }
        // Verify the package type is FollowMe
        if (followMeSub.getPackageType() != PackageType.FollowMe) {
            throw new CustomException(400, "Not a FollowMe package");
        }

        // Retrieve the associated TravelWithMe package
        Package parentPackage = followMeSub.get_package();
        if (parentPackage != null) {
            parentPackage.setAvailableForFollowMe(false);
            packageRepository.save(parentPackage);
        }

        // Delete the FollowMe subpackage and search entry
        subPackageRepository.delete(followMeSub);
        searchPackageRepository.deleteById(id);
    }

    @Override
    public void deleteSomeFollowMe() {
        List<SubPackage> followMePackages = subPackageRepository.findAll()
                .stream()
                .filter(z -> z.getPackageType().equals(PackageType.FollowMe))
                .collect(Collectors.toList());

        // Keep track of unique Packages
        Set<String> uniquePackages = new HashSet<>();

        // Iterate over followMePackages and keep one SubPackage per Package
        Iterator<SubPackage> iterator = followMePackages.iterator();
        while (iterator.hasNext()) {
            SubPackage subPackage = iterator.next();
            Package currentPackage = subPackage.get_package();

            // If the Package is not in the set, add it and continue to the next SubPackage
            if (currentPackage != null && uniquePackages.add(currentPackage.getId())) {
                continue;
            }

            // If the Package is already in the set, delete the SubPackage
            iterator.remove();
            subPackageRepository.delete(subPackage);
            searchPackageRepository.deleteById(subPackage.getId());

        }


    }

    @Transactional
    public void updatePackageFromRequest(com.hb.crm.core.dtos.CreateUpdatePackageDto obj, SubPackage oldSubPackage) {
        var generatedMedia = generateMediaWithWrapper(obj.getMedias());
        obj.setMedias(new ArrayList<>());

        // Map DTOs to entity objects
        Package _package = convertToEntity(obj);
        SubPackage _sub = convertToSubEntity(obj);

        _sub.setModifyRequest(oldSubPackage.getModifyRequest());

        _package.setMedias(generatedMedia);
        if (obj.getTags() != null && !obj.getTags().isEmpty())
            obj.setTags(tagService.checkNewTags(obj.getTags()));

        // Calculate package end date based on start date and duration
        _sub.setStart(DateFormatter.formatLocalDateTime(obj.getStart()));
        _sub.setEnd(obj.getEnd());


        // ** EXISTING PACKAGE UPDATE FLOW **

        // Retrieve existing package data to preserve certain fields
        SubPackage sPackage = subPackageRepository.findById(obj.getId())
                .orElseThrow(() -> new CustomException(404, "Package not found!"));

        // Preserve critical fields that shouldn't be modified during updates
        _sub.setCreationDate(sPackage.getCreationDate());
        _sub.setPackageStatus(sPackage.getPackageStatus());
        _sub.setRejectionNote(sPackage.getRejectionNote());
        _sub.setStart(DateFormatter.formatLocalDateTime(sPackage.getStart()));
        _sub.setEnd(DateFormatter.formatLocalDateTime(sPackage.getEnd()));
        _sub.setState(sPackage.getState());

        // Preserve main package fields and relationships
        _package.setId(sPackage.get_package().getId());
        _package.setStart(DateFormatter.formatLocalDateTime(sPackage.get_package().getStart()));
        _package.setEnd(DateFormatter.formatLocalDateTime(sPackage.get_package().getEnd()));
        _package.setAvailableForFollowMe(sPackage.get_package().isAvailableForFollowMe());
        _package.setAvailableFrom(sPackage.get_package().getAvailableFrom());
        _package.setFollowMeDiscount(sPackage.get_package().getFollowMeDiscount());
        _package.setInfulancer(oldSubPackage.get_package().getInfulancer());


        // Update the modification timestamp
        _sub.setUpdateDate(DateFormatter.formatLocalDateTime(LocalDateTime.now()));

        // Re-establish package relationship
        _sub.set_package(_package);

        // Save main package only if it's a "TravelWithMe" type
        if (_sub.getPackageType().equals(PackageType.TravelWithMe))
            packageRepository.save(_package);

        // Save updated sub-package
        subPackageRepository.save(_sub);

        // Update search index with modified package data
        searchPackage searchPackage = convertPackageToSearchPackage(_sub);
        searchPackageRepository.save(searchPackage);


        // ** MEDIA OWNERSHIP UPDATE **
        // Update media ownership information if package has associated media
        if (_package.getMedias() != null) {
            List<Media> mediasToSave = new ArrayList<>();

            // Extract media objects from wrappers and update user ownership
            _package.getMedias().stream()
                    .map(MediaWrapper::getMedia)
                    .filter(Objects::nonNull)
                    .forEach(media -> {
                        media.setUser(_package.getInfulancer());
                        media.set_package(_package);
                        mediasToSave.add(media);
                    });

            // Batch saves all media ownership updates
            mediaRepository.saveAll(mediasToSave);
        }
    }

    public List<MediaWrapper> generateMediaWithWrapper(List<CreateMediaDto> mediasRequest) {
        if (mediasRequest == null)
            return new ArrayList<>();
        List<MediaWrapper> createdMedias = new ArrayList<>();
        for (CreateMediaDto createMediaDto : mediasRequest) {
            Media newMedia = new Media();
            newMedia.setMediaType(createMediaDto.getMediaType());
            newMedia.setSource(createMediaDto.getSource());
            newMedia.setVideoUrl(createMediaDto.getVideoUrl());
            newMedia.setVideoSize(createMediaDto.getVideoSize());
            newMedia.setCreationDate(DateTime.now().toDate());
            newMedia.setLastUpdate(DateTime.now().toDate());

            newMedia.setTitle(createMediaDto.getTitle());

            if (createMediaDto.getMediaType() == MediaType.image) {
                try {
                    String category = createMediaDto.getSource().split("/")[1];
                    ImageCategory imageCategory = ImageCategory.valueOf(category);
                    newMedia.setImageCategory(imageCategory);
                } catch (Exception ignored) {
                }
            }


            var savedMedia = mediaRepository.save(newMedia);

            MediaWrapper mediaWrapper = new MediaWrapper();
            mediaWrapper.setMedia(savedMedia);
            mediaWrapper.setUrl(newMedia.getMediaType() == MediaType.video ? newMedia.getVideoUrl() : newMedia.getSource());
            mediaWrapper.setType(createMediaDto.getMediaType());
            mediaWrapper.setMainImage(createMediaDto.isMainImage());
            createdMedias.add(mediaWrapper);
        }

        return createdMedias;
    }



    private Criteria createSearchSpecification(Map<String, Object> obj) {
        Criteria query = new Criteria();
        List<Criteria> list = new ArrayList<>();
        for (Map.Entry<String, Object> entry : obj.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (value != null && !value.toString().isEmpty() && value.getClass().equals(Boolean.class)) {
                list.add(Criteria.where(key).is(value));
            }
            if (value != null && !value.toString().isEmpty() && key == "state") {
                list.add(Criteria.where(key).is(Integer.valueOf(value.toString())));
            } else if (value != null && !value.toString().isEmpty()) {
                list.add(Criteria.where(key).regex(value.toString(), "i"));
            }
        }
        if (list.size() > 0)
            query.andOperator(list);
        return query;
    }

    private Package convertToEntity(CreateUpdatePackageDto _package) {
        Package packageEntity = modelMapper.map(_package, Package.class);
        return packageEntity;
    }

    @Transactional
    public searchPackage convertPackageToSearchPackage(SubPackage _package) {
        searchPackage packageEntity = modelMapper.map(_package, searchPackage.class);
        // Extract IDs from moods
        List<String> moodIds = _package.get_package().getMoods().stream()
                .map(Mood::getId)
                .collect(Collectors.toList());
        List<String> tagsIds = _package.get_package().getTags().stream()
                .map(Tag::getId)
                .toList();
        // Fetch all moods at once
        List<Mood> fullMoods = moodIds.isEmpty() ? Collections.emptyList() :
                mongoTemplate.find(
                        Query.query(Criteria.where("_id").in(moodIds)),
                        Mood.class
                );
        List<Tag> fullTags = tagsIds.isEmpty() ? Collections.emptyList() :
                mongoTemplate.find(Query.query(Criteria.where("_id").in(tagsIds)), Tag.class);
        List<SearchMood> list = new ArrayList<>();
        for (Mood z : fullMoods) {
            SearchMood map = modelMapper.map(z, SearchMood.class);
            list.add(map);
        }
        packageEntity.setMoods(list);
        packageEntity.setTags(fullTags);
        packageEntity.setMedias(mapToMediaSearch(_package.get_package().getMedias()));
        packageEntity.setDescription(_package.get_package().getDescription());
        packageEntity.setInfulancer(modelMapper.map(_package.get_package().getInfulancer(), simpleUserInfo.class));
        packageEntity.setState(_package.getState());
        packageEntity.setPackageId(_package.get_package().getId());
        return packageEntity;
    }

    private SubPackage convertToSubEntity(CreateUpdatePackageDto _package) {
        SubPackage packageEntity = modelMapper.map(_package, SubPackage.class);
        packageEntity.setPrice(SettingValues(_package.getPrice()));
        packageEntity.setDetails(SettingValues(_package.getDetails()));
        return packageEntity;
    }

    private HashMap<String, String> SettingValues(List<keyAndValue> values) {
        if (values == null) {
            return null;
        }
        HashMap<String, String> myHash = new HashMap<String, String>();
        for (keyAndValue value : values) {
            myHash.put(value.getKey(), value.getValue());
        }
        return myHash;
    }

    private boolean isImageFile(String fileName) {
        String extension = getFileExtension(fileName);
        return extension != null && (extension.equalsIgnoreCase("jpg") ||
                extension.equalsIgnoreCase("jpeg") ||
                extension.equalsIgnoreCase("png") ||
                extension.equalsIgnoreCase("gif") ||
                extension.equalsIgnoreCase("bmp") ||
                extension.equalsIgnoreCase("webp") ||
                extension.equalsIgnoreCase("tiff") ||
                extension.equalsIgnoreCase("svg"));
    }

    private boolean isVideoFile(String fileName) {
        String extension = getFileExtension(fileName);
        return extension != null && (extension.equalsIgnoreCase("mp4") ||
                extension.equalsIgnoreCase("avi") ||
                extension.equalsIgnoreCase("mov") ||
                extension.equalsIgnoreCase("wmv") ||
                extension.equalsIgnoreCase("flv") ||
                extension.equalsIgnoreCase("mkv") ||
                extension.equalsIgnoreCase("webm") ||
                extension.equalsIgnoreCase("3gp"));
    }

    private String getFileExtension(String fileName) {
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex > 0 && dotIndex < fileName.length() - 1) {
            return fileName.substring(dotIndex + 1).toLowerCase();
        } else {
            return null;
        }
    }


    public void addPlacesToPackage(Package _package, List<PackageCountry> countries) {
        List<PackageCountry> countryList = new ArrayList<>();
        for (PackageCountry country : countries) {
            PackageCountry mappedCountry = new PackageCountry();
            mappedCountry.setPropertyId(country.getPropertyId());
            mappedCountry.setOrder(country.getOrder()); // Set order directly from the country
            // Map Cities
            List<PackageCity> mappedCities = new ArrayList<>();
            for (PackageCity city : country.getCities()) {
                PackageCity mappedCity = new PackageCity();
                mappedCity.setPropertyId(city.getPropertyId());
                mappedCity.setOrder(city.getOrder());
                // Map Areas
                List<PackageArea> mappedAreas = new ArrayList<>();
                for (PackageArea area : city.getAreas()) {
                    PackageArea mappedArea = new PackageArea();
                    mappedArea.setPropertyId(area.getPropertyId());
                    mappedArea.setOrder(area.getOrder());
                    // Map Hotels and Transportation in Area
                    mappedArea.setHotels(mapHotels(area.getHotels()));
                    mappedArea.setTransportation(mapTransportation(area.getTransportation()));
                    mappedArea.setItinerary(mapItineraries(area.getItinerary()));
                    mappedAreas.add(mappedArea);
                }
                mappedCity.setAreas(mappedAreas);
                // Map Hotels and Transportation in City
                mappedCity.setHotels(mapHotels(city.getHotels()));
                mappedCity.setTransportation(mapTransportation(city.getTransportation()));
                mappedCity.setItinerary(mapItineraries(city.getItinerary()));
                mappedCities.add(mappedCity);
            }
            mappedCountry.setCities(mappedCities);
            // Map Hotels and Transportation in Country
            mappedCountry.setHotels(mapHotels(country.getHotels()));
            mappedCountry.setTransportation(mapTransportation(country.getTransportation()));
            mappedCountry.setItinerary(mapItineraries(country.getItinerary()));
            countryList.add(mappedCountry);
        }
        _package.setPackagePlaces(countryList);
        // Assign the list of countries to the package
        // Assuming `_package` has a method to set countries directly
    }
    // Assign the list of countries to the package


    private List<PackageHotel> mapHotels(List<PackageHotel> hotels) {
        return hotels.stream().map(hotel -> {
            PackageHotel mappedHotel = new PackageHotel();
            mappedHotel.setPropertyId(hotel.getPropertyId());
            mappedHotel.setName(hotel.getName());
            return mappedHotel;
        }).collect(Collectors.toList());
    }

    private List<PackageTransportation> mapTransportation(List<PackageTransportation> transportationList) {
        return transportationList.stream().map(transportation -> {
            PackageTransportation mappedTransportation = new PackageTransportation();
            mappedTransportation.setPropertyId(transportation.getPropertyId());
            mappedTransportation.setType(transportation.getType());
            mappedTransportation.setFrom(transportation.getFrom());
            mappedTransportation.setTo(transportation.getTo());
            mappedTransportation.setDescription(transportation.getDescription());
            mappedTransportation.setImageUrl(transportation.getImageUrl());
            mappedTransportation.setPrice(transportation.getPrice());
            mappedTransportation.setStartDate(transportation.getStartDate());
            mappedTransportation.setEndDate(transportation.getEndDate());
            mappedTransportation.setLatitudeFrom(transportation.getLatitudeFrom());
            mappedTransportation.setLongitudeFrom(transportation.getLongitudeFrom());
            mappedTransportation.setLatitudeTo(transportation.getLatitudeTo());
            mappedTransportation.setLongitudeTo(transportation.getLongitudeTo());
            return mappedTransportation;
        }).collect(Collectors.toList());
    }

    private List<PackageItinerary> mapItineraries(List<PackageItinerary> itineraries) {
        if (itineraries == null) return new ArrayList<>();
        return itineraries.stream().map(itinerary -> {
            PackageItinerary mappedItinerary = new PackageItinerary();
            // mappedItinerary.setPropertyId(itinerary.getPropertyId());
            mappedItinerary.setName(itinerary.getName());
            mappedItinerary.setDetails(itinerary.getDetails());
            mappedItinerary.setStart(itinerary.getStart());
            mappedItinerary.setEnd(itinerary.getEnd());
            // mappedItinerary.setPlace(itinerary.getPlace());
            mappedItinerary.setCategory(itinerary.getCategory());
            mappedItinerary.setOtherDetail(itinerary.getOtherDetail());
            return mappedItinerary;
        }).collect(Collectors.toList());
    }


    private List<MediaWrapperSearch> mapToMediaSearch(List<MediaWrapper> medias) {
        return medias
                .stream()
                .map(item -> modelMapper.map(item, MediaWrapperSearch.class))
                .toList();
    }

    public List<User> findFollowers(String influencerId) {
        // Get followed influencers
        var followedInfluencersReactions = reactionSearchRepository.findByEntityNameAndEntityTypeAndEntityId(
                EntityName.User,
                EntityType.Follow,
                influencerId
        );

        var followedInfluencersIds = followedInfluencersReactions
                .stream()
                .map(ReactionSearch::getUserId)
                .filter(Objects::nonNull)
                .toList();
        return userRepository.findByIdIn(followedInfluencersIds);
    }

    private void sendNotificationToInfluencerWithCreatePackage(User influencer, SubPackage _package,
                                                               List<Object> entities, String packageImage) {
        notificationService.sendAndStoreNotification(_package.getId(), NotificationType.InfluencerNewPackage,
                influencer, entities, packageImage,
                null,
                NotificationEntityType.PACKAGE,
                influencer,
                _package.getSlug(),
                influencer.getUsername(), null);
    }

    private void sendNotificationToInfluencerWithUpdatePackage(User influencer, SubPackage _package, List<Object> entities) {
        notificationService.sendAndStoreNotification(_package.getId(), NotificationType.InfluencerUpdatePackage,
                influencer, entities, _package.get_package()
                        .getMedias()
                        .stream()
                        .filter(MediaWrapper::isMainImage)
                        .findFirst()
                        .get().getUrl(),
                null,
                NotificationEntityType.PACKAGE,
                influencer,
                _package.getSlug(),
                influencer.getUsername(), null);
    }


    public Package convertToEntity(com.hb.crm.core.dtos.CreateUpdatePackageDto _package) {
        // Create a custom TypeMap to prepare ModelMapper for mapping media objects
        // This ensures proper conversion between CreateMediaWrapperWithMediaDto and MediaWrapper
        var typemap = modelMapper.getTypeMap(CreateMediaWrapperWithMediaDto.class, MediaWrapper.class);
        if(typemap == null) {
            TypeMap<CreateMediaWrapperWithMediaDto, MediaWrapper> mediaTypeMap =
                    modelMapper.createTypeMap(CreateMediaWrapperWithMediaDto.class, MediaWrapper.class);
        }
        Package packageEntity = modelMapper.map(_package, Package.class);
        return packageEntity;
    }

    public SubPackage convertToSubEntity(com.hb.crm.core.dtos.CreateUpdatePackageDto _package) {
        SubPackage packageEntity = modelMapper.map(_package, SubPackage.class);
        return packageEntity;
    }

}
