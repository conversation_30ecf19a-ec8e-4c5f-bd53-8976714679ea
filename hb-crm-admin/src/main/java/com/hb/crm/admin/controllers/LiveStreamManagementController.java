package com.hb.crm.admin.controllers;

import com.hb.crm.admin.dto.PageDto;
import com.hb.crm.admin.dto.LiveStream.LiveStreamCommentMonitoringDto;
import com.hb.crm.admin.dto.LiveStream.LiveStreamReactionMonitoringDto;
import com.hb.crm.admin.dto.LiveStream.LiveStreamRealtimeMonitoringStatsDto;
import com.hb.crm.admin.services.interfaces.LiveStreamManagementService;
import com.hb.crm.admin.beans.UserSessionData;
import com.hb.crm.admin.beans.CustomUser;
import com.hb.crm.admin.config.authentication.JwtUtil;
import com.hb.crm.core.dtos.LiveStream.LiveStreamResponseDto;
import com.hb.crm.core.dtos.LiveStream.LiveStreamForceStopRequestDto;
import com.hb.crm.core.Enums.LiveStreamStatus;
import com.hb.crm.core.beans.Employee;
import com.hb.crm.core.beans.Role;
import com.hb.crm.core.beans.LiveStream.LiveStream;
import com.hb.crm.core.repositories.EmployeeRepository;
import com.hb.crm.core.repositories.LiveStream.LiveStreamRepository;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * REST Controller for Live Stream Management in Admin Panel
 * Provides endpoints for comprehensive administrative live stream operations
 */
@RestController
@RequestMapping(value = "/v1/live-stream-management", produces = {MediaType.APPLICATION_JSON_VALUE})
@Tag(name = "Live Stream Management", description = "Administrative operations for live stream management")
public class LiveStreamManagementController 
{

    private final LiveStreamManagementService liveStreamManagementService;
    private final UserSessionData userSessionData;
    private final RestTemplate restTemplate;
    private final LiveStreamRepository liveStreamRepository;
    private final EmployeeRepository employeeRepository;
    private final JwtUtil jwtUtil;
    
    @Value("${client.api.base-url:http://localhost:8089}")
    private String clientApiBaseUrl;

    @Autowired
    public LiveStreamManagementController(LiveStreamManagementService liveStreamManagementService, 
                                         UserSessionData userSessionData, 
                                         RestTemplate restTemplate,
                                         EmployeeRepository employeeRepository,
                                         JwtUtil jwtUtil,
                                         LiveStreamRepository liveStreamRepository) {
        this.liveStreamManagementService = liveStreamManagementService;
        this.userSessionData = userSessionData;
        this.restTemplate = restTemplate;
        this.employeeRepository = employeeRepository;
        this.jwtUtil = jwtUtil;
        this.liveStreamRepository = liveStreamRepository;
    }

    @PostMapping("/search")
    @Operation(
        summary = "Search and filter live streams",
        description = "Advanced search for live streams with comprehensive filtering options including influencer, package, status, dates, and engagement metrics. Supports pagination and sorting."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Live streams retrieved successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid search parameters"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @ResponseBody
    public ResponseEntity<PageDto<LiveStreamResponseDto>> searchLiveStreams(
            @Parameter(description = "Search filters object containing criteria like influencerId, packageId, status, title, dateRange, etc.")
            @RequestBody Map<String, Object> filters,
            
            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") int page,
            
            @Parameter(description = "Number of items per page", example = "10")
            @RequestParam(defaultValue = "10") int size,
            
            @Parameter(description = "Sort direction (ASC or DESC)", example = "DESC")
            @RequestParam(defaultValue = "DESC") String direction,
            
            @Parameter(description = "Property to sort by", example = "createdAt")
            @RequestParam(defaultValue = "createdAt") String propertyName) 
    {
        
        try 
        {

            PageDto<LiveStreamResponseDto> result = liveStreamManagementService.searchLiveStreams(
                filters, page, size, direction, propertyName);
            return ResponseEntity.ok(result);
            
        } 
        catch (IllegalArgumentException e) 
        {
            PageDto<LiveStreamResponseDto> errorResponse = new PageDto<>();
            errorResponse.setItems(java.util.Collections.emptyList());
            errorResponse.setTotalNoOfItems(0);
            errorResponse.setPageNumber(0);
            errorResponse.setItemsPerPage(0);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(errorResponse);
        } 
        catch (Exception e) 
        {
            PageDto<LiveStreamResponseDto> errorResponse = new PageDto<>();
            errorResponse.setItems(java.util.Collections.emptyList());
            errorResponse.setTotalNoOfItems(0);
            errorResponse.setPageNumber(0);
            errorResponse.setItemsPerPage(0);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(errorResponse);
        }
         }

     @PostMapping("/force-stop/{streamId}")
     @Operation(
         summary = "Force stop a live stream",
         description = "Administratively force stop a live stream via client API, end all websocket connections, and delete the AWS IVS channel"
     )
     @ApiResponses(value = {
         @ApiResponse(responseCode = "200", description = "Live stream force stopped successfully"),
         @ApiResponse(responseCode = "400", description = "Invalid stream ID or stream already stopped"),
         @ApiResponse(responseCode = "401", description = "Authentication failed"),
         @ApiResponse(responseCode = "404", description = "Live stream not found"),
         @ApiResponse(responseCode = "500", description = "Internal server error")
     })
     @ResponseBody
     public ResponseEntity<LiveStreamResponseDto> forceStopLiveStream(
             @Parameter(description = "Live stream ID to force stop", required = true)
             @PathVariable String streamId,
             
             @Parameter(description = "Reason for force stopping the stream", required = true)
             @RequestParam String reason
             ) 
     {
         try 
         {
             // Get current admin employee ID
             String adminId = userSessionData.getId();
             if (adminId == null || adminId.trim().isEmpty()) {
                 return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
             }
             
             // Get current employee from database
             Optional<Employee> optionalEmployee = employeeRepository.findById(adminId);
             if (!optionalEmployee.isPresent()) {
                 return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
             }
             
             Employee employee = optionalEmployee.get();
             
             // Generate JWT token for the current admin employee
             String adminToken = generateTokenForEmployee(employee);
             
             // Create request payload for client API
             LiveStreamForceStopRequestDto clientRequest = new LiveStreamForceStopRequestDto();
             clientRequest.setStreamId(streamId);
             clientRequest.setAdminId(adminId);
             clientRequest.setAdminToken(adminToken);
             clientRequest.setReason(reason);
             
             // Prepare HTTP headers
             HttpHeaders headers = new HttpHeaders();
             headers.setContentType(MediaType.APPLICATION_JSON);
             
             // Create HTTP entity
             HttpEntity<LiveStreamForceStopRequestDto> entity = new HttpEntity<>(clientRequest, headers);
             
             Optional<LiveStream> liveStream = liveStreamRepository.findById(streamId);
             if(!liveStream.isPresent())
             {
                return ResponseEntity.notFound().build();
             }
             if(liveStream.get().getStatus() != LiveStreamStatus.LIVE)
             {
                return ResponseEntity.badRequest().build();
             }

             // Make HTTP call to client API
             String clientUrl = clientApiBaseUrl + "/v1/live-stream/force-stop-by-admin";
             ResponseEntity<LiveStreamResponseDto> clientResponse = restTemplate.postForEntity(
                 clientUrl, entity, LiveStreamResponseDto.class);
             
             // Return client response
             return ResponseEntity.status(clientResponse.getStatusCode()).body(clientResponse.getBody());
             
         } 
         catch (Exception e) 
         {
             // Log error for debugging
             System.err.println("❌ Error calling client API for force stop: " + e.getMessage());
             e.printStackTrace();
             
             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
         }
     }
    
    @GetMapping("/filter-options")
    @Operation(
        summary = "Get available filter options",
        description = "Retrieve available filter options for live stream search including statuses and MP4 conversion statuses"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Filter options retrieved successfully"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getFilterOptions() 
    {
        try 
        {
            Map<String, Object> filterOptions = new HashMap<>();
            
            // Live stream statuses
            filterOptions.put("liveStreamStatuses", new String[]{"LIVE", "COMPLETED", "CANCELED"});
            
            // MP4 conversion statuses  
            filterOptions.put("mp4ConversionStatuses", new String[]{"NOT_STARTED", "IN_PROGRESS", "COMPLETED", "FAILED"});
            
            // Sort properties
            filterOptions.put("sortProperties", new String[]
            {
                "createdAt", "endedAt", "title", "status", "viewersCount", 
                "numberOfReactions", "numberOfComments", "mp4ConversionStatus"
            });
            
            // Sort directions
            filterOptions.put("sortDirections", new String[]{"ASC", "DESC"});
            
            // Example date format
            filterOptions.put("dateFormat", "2024-01-15T10:30:00");
            filterOptions.put("dateFormatDescription", "ISO Local DateTime format (YYYY-MM-DDTHH:MM:SS)");
            
            return ResponseEntity.ok(filterOptions);
            
        } 
        catch (Exception e) 
        {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Error retrieving filter options: " + e.getMessage()));
        }
    }

    
    @GetMapping("/{streamId}/monitor")
    @Operation(
        summary = "Get basic stream monitoring info",
        description = "Get basic information needed for admin monitoring including playback URL and current stats"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Stream monitoring info retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Stream not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getStreamMonitorInfo(
            @Parameter(description = "Live stream ID to monitor") @PathVariable String streamId) 
    {
        try 
        {
            Map<String, Object> monitorInfo = liveStreamManagementService.getBasicMonitoringInfo(streamId);
            return ResponseEntity.ok(monitorInfo);
        } 
        catch (IllegalArgumentException e) 
        {
            return ResponseEntity.notFound().build();
        } 
        catch (Exception e) 
        {
            e.printStackTrace(); 
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Error retrieving monitoring info: " + e.getMessage()));
        }
    }
    
    @GetMapping("/{streamId}/realtime-stats")
    @Operation(
        summary = "Get comprehensive real-time monitoring statistics",
        description = "Get detailed stream information including current viewer count, activity metrics, user details, technical info, MP4 conversion status, and detailed list of new comments with user information. Works for both LIVE and ENDED streams."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Real-time stats retrieved successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid parameters"),
        @ApiResponse(responseCode = "404", description = "Stream not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @ResponseBody
    public ResponseEntity<LiveStreamRealtimeMonitoringStatsDto> getRealtimeMonitoringStats(
            @Parameter(description = "Live stream ID to monitor") 
            @PathVariable String streamId,
            
            @Parameter(description = "Time window in seconds to count new activity (default: 5 seconds)", example = "5")
            @RequestParam(defaultValue = "5") int secondsWindow) 
    {
        try 
        {
            // Validate parameters
            if (secondsWindow > 300) {
                return ResponseEntity.badRequest()
                    .body(null);
            }
            
            LiveStreamRealtimeMonitoringStatsDto realtimeStats = liveStreamManagementService.getRealtimeMonitoringStats(streamId, secondsWindow);
            return ResponseEntity.ok(realtimeStats);
        } 
        catch (IllegalArgumentException e) 
        {
            return ResponseEntity.notFound().build();
        } 
        catch (Exception e) 
        {
            e.printStackTrace(); 
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(null);
        }
    }
    
    @GetMapping("/{streamId}/comments")
    @Operation(
        summary = "Get paginated comments for a live stream",
        description = "Retrieve all comments for a specific live stream with pagination and sorting support"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Comments retrieved successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid parameters"),
        @ApiResponse(responseCode = "404", description = "Stream not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @ResponseBody
    public ResponseEntity<PageDto<LiveStreamCommentMonitoringDto>> getStreamComments(
            @Parameter(description = "Live stream ID to get comments for", required = true)
            @PathVariable String streamId,
            
            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") int page,
            
            @Parameter(description = "Number of items per page", example = "20")
            @RequestParam(defaultValue = "20") int size,
            
            @Parameter(description = "Sort direction (ASC or DESC)", example = "DESC")
            @RequestParam(defaultValue = "DESC") String direction,
            
            @Parameter(description = "Property to sort by", example = "createdDate")
            @RequestParam(defaultValue = "createdDate") String propertyName) 
    {
        try 
        {
            // Validate parameters
            if (page < 0) {
                return ResponseEntity.badRequest().build();
            }
            if (size < 1 || size > 100) {
                return ResponseEntity.badRequest().build();
            }
            
            PageDto<LiveStreamCommentMonitoringDto> comments = liveStreamManagementService.getStreamComments(
                streamId, page, size, direction, propertyName);
            return ResponseEntity.ok(comments);
        } 
        catch (IllegalArgumentException e) 
        {
            return ResponseEntity.notFound().build();
        } 
        catch (Exception e) 
        {
            e.printStackTrace(); 
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @GetMapping("/{streamId}/reactions")
    @Operation(
        summary = "Get paginated reactions for a live stream",
        description = "Retrieve all reactions for a specific live stream with pagination and sorting support"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Reactions retrieved successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid parameters"),
        @ApiResponse(responseCode = "404", description = "Stream not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @ResponseBody
    public ResponseEntity<PageDto<LiveStreamReactionMonitoringDto>> getStreamReactions(
            @Parameter(description = "Live stream ID to get reactions for", required = true)
            @PathVariable String streamId,
            
            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") int page,
            
            @Parameter(description = "Number of items per page", example = "20")
            @RequestParam(defaultValue = "20") int size,
            
            @Parameter(description = "Sort direction (ASC or DESC)", example = "DESC")
            @RequestParam(defaultValue = "DESC") String direction,
            
            @Parameter(description = "Property to sort by", example = "createDate")
            @RequestParam(defaultValue = "createDate") String propertyName) 
    {
        try 
        {
            // Validate parameters
            if (page < 0) {
                return ResponseEntity.badRequest().build();
            }
            if (size < 1 || size > 100) {
                return ResponseEntity.badRequest().build();
            }
            
            PageDto<LiveStreamReactionMonitoringDto> reactions = liveStreamManagementService.getStreamReactions(
                streamId, page, size, direction, propertyName);
            return ResponseEntity.ok(reactions);
        } 
        catch (IllegalArgumentException e) 
        {
            return ResponseEntity.notFound().build();
        } 
        catch (Exception e) 
        {
            e.printStackTrace(); 
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * Generate JWT token for an employee
     * @param employee The employee to generate token for
     * @return JWT token string
     */
    private String generateTokenForEmployee(Employee employee) {
        // Convert employee roles to authorities
        List<SimpleGrantedAuthority> authorities = new ArrayList<>();
        if (employee.getRoles() != null && !employee.getRoles().isEmpty()) {
            for (Role role : employee.getRoles()) {
                authorities.add(new SimpleGrantedAuthority("ROLE_" + role.getType()));
            }
        }
        
        // Create CustomUser (which implements CustomUserDetails)
        CustomUser customUser = new CustomUser(
            employee.getUsername(), 
            employee.getPassword(), 
            authorities, 
            employee.getId()
        );
        
        // Generate and return JWT token
        return jwtUtil.generateToken(customUser);
    }
   
}