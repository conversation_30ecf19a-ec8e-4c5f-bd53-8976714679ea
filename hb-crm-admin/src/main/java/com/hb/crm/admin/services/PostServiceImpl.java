package com.hb.crm.admin.services;

import com.hb.crm.admin.beans.UserSessionData;
import com.hb.crm.admin.config.CustomHundlerar.CustomException;
import com.hb.crm.admin.config.DateFormater;
import com.hb.crm.admin.config.DateFormatter;
import com.hb.crm.admin.dto.CountDto;
import com.hb.crm.admin.dto.PageDto;
import com.hb.crm.admin.dto.Results.CommentResultDto;
import com.hb.crm.admin.dto.Results.UserStoriesResultDto;
import com.hb.crm.admin.dto.UserStory.UserStories;
import com.hb.crm.admin.dto.packageDto.MediaWrapperDto;
import com.hb.crm.admin.dto.posts.CommentDto;
import com.hb.crm.admin.dto.posts.PostDto;
import com.hb.crm.admin.dto.posts.ReactionDto;
import com.hb.crm.admin.dto.users.SimpleUserinfoDto;
import com.hb.crm.admin.dto.users.UserInfoDto;
import com.hb.crm.admin.services.interfaces.PostService;
import com.hb.crm.admin.services.interfaces.SettingService;
import com.hb.crm.admin.services.interfaces.TagService;
import com.hb.crm.core.Enums.PackageStatus;
import com.hb.crm.core.Enums.PostType;
import com.hb.crm.core.beans.*;
import com.hb.crm.core.beans.Package;
import com.hb.crm.core.repositories.*;
import com.hb.crm.core.searchBeans.*;
import com.hb.crm.core.searchRepositories.SearchPostRepository;
import com.hb.crm.core.searchRepositories.SearchStoryRepository;
import com.hb.crm.core.util.ApplicationUtil;
import org.bson.types.ObjectId;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PostServiceImpl implements PostService {

    private static final int ALL_LIMIT = 99999;
    private final PostRepository postRepository;
    private final SearchStoryRepository searchStoryRepository;
    private final SearchPostRepository searchPostRepository;
    private final MongoTemplate mongoTemplate;
    private final UserRepository userRepository;
    private final QueryNormalizeServiceImpl queryNormalizeService;
    private final ModelMapper modelMapper;
    private final UserSessionData userSessionData;
    private final CommentRepository commentRepository;
    private final SettingService settingService;

    private final MediaRepository mediaRepository;
    private final PackageRepository packageRepository;
    private final TagService tagService;
    private final EmployeeRepository employeeRepository;
    private Logger logger = LoggerFactory.getLogger(PostServiceImpl.class);

    public PostServiceImpl(PostRepository postRepository
            , SearchStoryRepository searchStoryRepository
            , SearchPostRepository searchPostRepository, @Qualifier("mongoTemplate1") MongoTemplate mongoTemplate, UserRepository userRepository
            , QueryNormalizeServiceImpl queryNormalizeService
            , ModelMapper modelMapper, UserSessionData userSessionData, CommentRepository commentRepository, SettingService settingService, MediaRepository mediaRepository, PackageRepository packageRepository, TagService tagService, EmployeeRepository employeeRepository) {
        this.postRepository = postRepository;
        this.searchStoryRepository = searchStoryRepository;
        this.searchPostRepository = searchPostRepository;
        this.mongoTemplate = mongoTemplate;
        this.userRepository = userRepository;
        this.queryNormalizeService = queryNormalizeService;
        this.modelMapper = modelMapper;
        this.userSessionData = userSessionData;
        this.commentRepository = commentRepository;
        this.settingService = settingService;
        this.mediaRepository = mediaRepository;
        this.packageRepository = packageRepository;
        this.tagService = tagService;
        this.employeeRepository = employeeRepository;
    }

    @Override
    public Page<PostDto> search() {

        final Pageable pageable = ApplicationUtil.createPageRequest(0, ALL_LIMIT, "creationDate", "DESC");
        List<PostDto> Posts = postRepository.findAll().stream().map(Post -> convertToDto(Post)).collect(Collectors.toList());
        final Page<PostDto> page = new PageImpl<>(Posts.subList(0, Posts.size()), pageable, Posts.size());
        return page;
    }

    @Override
    public PageDto<PostDto> search(Map<String, Object> obj, int page, int limit) {
        if (page < 0) {
            limit = ALL_LIMIT;
            page = 0;
        }
        PageDto<PostDto> pageDto = new PageDto<>();
        final Pageable pageable = PageRequest.of(page, limit);
        Criteria criteria = createSearchSpecification(obj);
        Sort sort = Sort.by(Sort.Direction.DESC, "update");
        final Aggregation aggregation = queryNormalizeService.getPostFields(criteria, pageable, sort, false);
        final Aggregation countAggregation = queryNormalizeService.getPostFields(criteria, pageable, sort, true);
        List<PostDto> Posts = mongoTemplate.aggregate(aggregation, "post", Post.class).getMappedResults().stream().map(post -> convertToDto(post)).collect(Collectors.toList());

        AggregationResults<CountDto> countResults = mongoTemplate.aggregate(countAggregation, "post", CountDto.class);
        long totalCount = countResults.getUniqueMappedResult() != null ? countResults.getUniqueMappedResult().getCount() : 0;
        pageDto.setTotalNoOfItems(totalCount);
        pageDto.setItems(Posts);
        return pageDto;
    }

    @Override
    public void acceptPost(String id, String notes, PackageStatus accept) {
        final Optional<Post> byId = postRepository.findById(id);
        if (!byId.isEmpty()) {
            Post post = byId.get();
            post.setPostStatus(accept);
            post.setRejectionNote(notes);
            post.setPostedDate((DateFormater.formatLocalDateTime(LocalDateTime.now())));
            post.setUpdate((DateFormater.formatLocalDateTime(LocalDateTime.now())));
            postRepository.save(post);
        }
    }

    @Override
    public PostDto getPostById(String id) {

        if (id == null) {
            return null;
        }
        final Optional<Post> byId = postRepository.findById(id);
        if (byId.isEmpty()) {
            return null;
        }
        return convertToDto(byId.get());
    }

    /**
     * Updates an existing Post entity or creates a new one with comprehensive processing of associated data.
     *
     * <p>This method performs the following operations:
     * <ul>
     *   <li>Validates that only one main image is selected among media attachments</li>
     *   <li>Processes and validates tags, creating new ones if necessary</li>
     *   <li>Sets the post status to {@link PackageStatus#posted}</li>
     *   <li>Validates and assigns the user to the post and all associated media</li>
     *   <li>Manages timestamps for creation and update dates</li>
     *   <li>Preserves existing metrics (views, reacts) for post updates</li>
     *   <li>Creates or updates corresponding search indices based on post type</li>
     * </ul>
     *
     * <p>For new posts (obj.getId() == null):
     * <ul>
     *   <li>Sets creation timestamp to current time</li>
     *   <li>Initializes metrics to default values</li>
     * </ul>
     *
     * <p>For existing posts (obj.getId() != null):
     * <ul>
     *   <li>Preserves original creation date</li>
     *   <li>Maintains existing view and reaction counts</li>
     *   <li>Updates the modification timestamp</li>
     * </ul>
     *
     * <p>Search Index Management:
     * <ul>
     *   <li>For {@link PostType#Story}: Creates/updates searchStory index</li>
     *   <li>For {@link PostType#Post}: Creates/updates searchPost index</li>
     *   <li>Associates package information if available</li>
     * </ul>
     *
     * @param obj The Post entity to be updated or created. Must contain:
     *            <ul>
     *              <li>A valid User reference with existing user ID</li>
     *              <li>Media list with at most one main image</li>
     *              <li>Valid PostType (Story or Post)</li>
     *              <li>For updates: existing post ID that can be found in database</li>
     *            </ul>
     * @throws CustomException with status code 500 if more than one main image is selected
     * @throws RuntimeException if the referenced user is not found in the database
     * @throws RuntimeException if attempting to update a post that doesn't exist in the database
     *
     * @see PackageStatus#posted
     * @see PostType
     * @see MediaWrapper#isMainImage()
     * @see TagService#checkNewTags(List)
     */
    @Override
    public void update(Post obj) {
        if (obj.getMedia() == null || obj.getMedia().isEmpty()) {
            throw new CustomException(400, "At least one media item is required.");
        }
        logger.info("update Start");

        // Validate that only one main image exists
        List<MediaWrapper> wrappers = obj.getMedia().stream().filter(MediaWrapper::isMainImage).toList();
        if (wrappers.size() > 1) {
            throw new CustomException(500, "Only one Main Image Should be Selected");
        }

        // Process and validate tags
        obj.setTags(tagService.checkNewTags(obj.getTags()));
        logger.info("Tags Checked");
        obj.setPostStatus(PackageStatus.posted);

        // Fetch and validate user existence, then set it to the post
        User user = userRepository.findById(obj.getUser().getId())
                .orElseThrow(() -> new RuntimeException("User not found"));

        // Handle tagged users
        if (obj.getTaggedUsers() != null && !obj.getTaggedUsers().isEmpty()) {
            List<ObjectId> userIds = obj.getTaggedUsers().stream()
                    .map(u -> new ObjectId(u.getId()))
                    .collect(Collectors.toList());
            Aggregation taggedQuery = queryNormalizeService.getUsersList(Criteria.where("_id").in(userIds));
            List<User> taggedUsers = mongoTemplate.aggregate(taggedQuery, "user", User.class).getMappedResults();
            obj.setTaggedUsers(taggedUsers);
        }

        if (obj.getPostType().equals(PostType.Story)) {
            Setting storySetting = settingService.getByName("Story-Limit-PerUser");
            int limit = (storySetting.getValue() == null || storySetting.getValue().isEmpty())
                    ? -1
                    : Integer.parseInt(storySetting.getValue());

            Criteria criteria = Criteria.where("username").is(user.getUsername());
            Pageable pageable = PageRequest.of(0, limit);
            Aggregation aggregation = queryNormalizeService.getUsersWithRecentStoriesSplash(criteria, null, limit, pageable, false);
            var results = mongoTemplate.aggregate(aggregation, "user", UserStoriesResultDto.class);
            List<UserStories> users = Objects.requireNonNull(results.getUniqueMappedResult()).getFilteredResults();
            if (!users.isEmpty() && limit > 0) {
                UserStories userStory = users.getFirst();
                if (userStory.getStories().size() >= limit) {
                    throw new CustomException(500, "User reached the max limit of added Stories");
                }
            }
        }

        obj.getMedia().forEach(media -> {
            media.getMedia().setUser(user);
            media.getMedia().setPost(obj);
        });

        logger.info("User Checked");
        obj.setUser(user);
        logger.info("User set");

        // Handle timestamp management
        String now = String.valueOf(DateFormatter.formatLocalDateTime(LocalDateTime.now()));
        if (obj.getId() != null) {
            if(obj.getSlug()==null || obj.getSlug().isEmpty()){

                String slug = user.getUsername()+"-"+(obj.getPostType().equals(PostType.Story)? "Story":"Post");
                slug= generateSlug(slug);
                obj.setSlug(slug);
            }else {
                obj.setSlug(slugify(obj.getSlug()));
            }
            // For existing posts: preserve original creation date and metrics
            Post existingPost = postRepository.findById(obj.getId())
                    .orElseThrow(() -> new RuntimeException("Post not found"));
            obj.setCreated(existingPost.getCreated());
            obj.setViewsCount(existingPost.getViewsCount());
            obj.setReactsCount(existingPost.getReactsCount());
        } else {

                if(obj.getSlug()==null || obj.getSlug().isEmpty()){
                    String slug = user.getUsername()+"-"+(obj.getPostType().equals(PostType.Story)? "Story":"Post");
                    slug= generateSlug(slug);
                    obj.setSlug(slug);
                }else {
                    obj.setSlug(slugify(obj.getSlug()));
                }

            // For new posts: set creation time
            obj.setCreated(LocalDateTime.parse(now));
        }
        obj.setUpdate(LocalDateTime.parse(now));
        logger.info("PostObject processed");

        // Persist the post to the database
        Post savedPost = postRepository.save(obj);
        logger.info("PostObject saved");

        // Prepare package information for search indices
        simplePackageInfo simplePackageInfo = new simplePackageInfo();
        if (obj.getPackage() != null) {
         Optional<Package> _package= packageRepository.findById(obj.getPackage().getId());
         if(_package.isPresent()){
             simplePackageInfo.setName(_package.get().getName());
             simplePackageInfo.setSlug(_package.get().getSlug());
             simplePackageInfo.setDescription(_package.get().getDescription());
             simplePackageInfo.setId(_package.get().getId());
         }

        }

        // Update search indices based on post type
        if (obj.getPostType().equals(PostType.Story)) {
            searchStory searchStory = convertPostToSearchStory(savedPost);
            searchStory.setPackage(simplePackageInfo);
            searchStory.setPostStatus(PackageStatus.posted);
            searchStoryRepository.save(searchStory);
            logger.info("searchStory saved");
        } else if (obj.getPostType().equals(PostType.Post)) {
            searchPost searchPost = convertPostToSearchPost(savedPost);
            searchPost.setPackage(simplePackageInfo);
            searchPost.setPostStatus(PackageStatus.posted);
            searchPostRepository.save(searchPost);
            logger.info("searchPost saved");
        }
    }
    private PostDto convertToDto(Post post) {
        PostDto dto = modelMapper.map(post, PostDto.class);

        if (post.getTaggedUsers() != null && !post.getTaggedUsers().isEmpty()) {
            List<SimpleUserinfoDto> tagged = post.getTaggedUsers().stream()
                    .map(user -> {
                        SimpleUserinfoDto u = new SimpleUserinfoDto();
                        u.setId(user.getId());
                        u.setFirstName(user.getFirstName());
                        u.setLastName(user.getLastName());
                        u.setProfileImage(user.getProfileImage());
                        u.setCoverImage(user.getCoverImage());
                        u.setUsertype(user.getUsertype());
                        return u;
                    })
                    .collect(Collectors.toList());
            dto.setTaggedUsers(tagged);
        }

        return dto;
    }


    public String generateSlug(String name) {
        String baseSlug = slugify(name); // Convert name to URL-friendly slug
        String slug = baseSlug;
        int counter = 1;

        while (!isSlugUnique(slug)) {
            slug = baseSlug + "-" + counter;
            counter++;
        }

        return slug;
    }

    public String slugify(String input) {
        return input.toLowerCase()
                .replaceAll("[^a-z0-9\\s-]", "")   // remove special characters
                .replaceAll("\\s+", "-")           // replace spaces with hyphens
                .replaceAll("-+", "-")             // replace multiple hyphens
                .replaceAll("^-|-$", "");           // trim hyphens from start/end
    }

    @Override
    public void delete(String id) {


        postRepository.deleteById(id);
        searchStoryRepository.deleteById(id);
        searchPostRepository.deleteById(id);
    }

    @Override
    public boolean isSlugUnique(String slug) {
        return !postRepository.existsBySlugIgnoreCase(slug);
    }

    @Override
    public Page<CommentDto> getPostComments(String postId, int pageNumber, int itemsPerPage) {
        final Pageable pageable = ApplicationUtil.createPageRequest(pageNumber, itemsPerPage, "createdDate", "DESC");

        long start = System.currentTimeMillis(); //start of the function

        long gettingComment = System.currentTimeMillis(); //start of the function

        long executionTime = gettingComment - start;
        logger.error("getting comment executed in {} ms", executionTime);
        final Aggregation countAggregation = queryNormalizeService.getPostComments(Criteria.where("post._id").is(new ObjectId(postId)), pageable);

        AggregationResults<CommentResultDto> Results = mongoTemplate.aggregate(countAggregation, "comment", CommentResultDto.class);
        long totalCount = Results.getUniqueMappedResult() != null ? Results.getUniqueMappedResult().getTotalCount() : 0;
        List<CommentDto> comments = Results.getUniqueMappedResult().getFilteredResults();
        logger.error("start getting reacts");

        long gettingReacts = System.currentTimeMillis(); //start of the function

        long executionReactTime = gettingReacts - gettingComment;
        logger.error("getting comment reacts in {} ms", executionReactTime);
        final Page<CommentDto> page = new PageImpl<>(comments, pageable, totalCount);

        return page;
    }

    @Override
    public List<ReactionDto> getPostReacts(String postId) {
        List<ReactionDto> reacts =
                mongoTemplate.find(Query.query(Criteria.where("post.id").is(postId)), PostReaction.class)
                        .stream().map(x -> convertToDto(x)).collect(Collectors.toList());
        return reacts;
    }

    @Override
    public CommentDto addComment(String postId, CommentDto commentDtoInput) {
        // 1) Find the currently logged‐in employee
        String userId = userSessionData.getId();
        Employee user = employeeRepository.findById(userId)
                .orElseThrow(() -> new CustomException(404, "User not found!"));

        // 2) Load the Post to which we’re adding a comment
        Post post = postRepository.findById(postId)
                .orElseThrow(() -> new CustomException(404, "Post not found!"));

        // 3) Map incoming CommentDto → new Comment entity
        Comment commentEntity = modelMapper.map(commentDtoInput, Comment.class);
        commentEntity.setUser(new User(userId));
        commentEntity.setPost(post);
        commentEntity.setCreatedDate(DateFormatter.formatLocalDateTime(LocalDateTime.now()));

        // 4) Save the new Comment
        commentEntity = commentRepository.save(commentEntity);

        // 5) Recompute the total comments count
        Aggregation countAggregation = queryNormalizeService.getPostComments(
                Criteria.where("post._id").is(new ObjectId(postId)),
                null // if you really want “no pagination”
        );
        AggregationResults<CommentDto> results =
                mongoTemplate.aggregate(countAggregation, "comment", CommentDto.class);
        int totalCount = results.getMappedResults().size();

        // 6) Update the Post’s commentCount (and related searchStory/searchPost)
        post.setCommentsCount(totalCount);
        if (post.getPostType().equals(PostType.Story)) {
            searchStory storyIndex = searchStoryRepository.findById(postId).orElse(null);
            if (storyIndex != null) {
                storyIndex.setCommentsCount(totalCount);
                searchStoryRepository.save(storyIndex);
            }
        }
        if (post.getPostType().equals(PostType.Post)) {
            searchPost postIndex = searchPostRepository.findById(postId).orElse(null);
            if (postIndex != null) {
                postIndex.setCommentsCount(totalCount);
                searchPostRepository.save(postIndex);
            }
        }
        postRepository.save(post);

        // 7) Map saved Comment entity → CommentDto for the response
        CommentDto savedDto = modelMapper.map(commentEntity, CommentDto.class);
        return savedDto;
    }



    private ReactionDto convertToDto(Reaction react) {
        ReactionDto reactionDto = modelMapper.map(react, ReactionDto.class);
        return reactionDto;
    }
    @Override
    public String deleteComment(String CommentId) {
        String id = userSessionData.getId();
        Optional<Comment> optionalComment = commentRepository.findById(CommentId);
        if (!optionalComment.isEmpty()) {
            Comment comment = optionalComment.get();
            if (comment.getUser().getId().equals(id)) {
                Optional<Post> optionalPost = postRepository.findById(comment.getPost().getId());
                Post post = optionalPost.get();
                searchStory searchStory = new searchStory();
                searchPost searchPost = new searchPost();

                if (post.getPostType().equals(PostType.Story))
                    searchStory = searchStoryRepository.findById(comment.getPost().getId()).get();
                if (post.getPostType().equals(PostType.Post))
                    searchPost = searchPostRepository.findById(comment.getPost().getId()).get();
                if (post.getCommentsCount() - 1 > 0) {
                    post.setCommentsCount(post.getCommentsCount() - 1);
                    if (post.getPostType().equals(PostType.Story))
                        searchStory.setCommentsCount(post.getCommentsCount() - 1);
                    if (post.getPostType().equals(PostType.Post))
                        searchPost.setCommentsCount(post.getCommentsCount() - 1);
                } else {
                    if (post.getPostType().equals(PostType.Story))
                        searchStory.setCommentsCount(0);
                    if (post.getPostType().equals(PostType.Post))
                        searchPost.setCommentsCount(0);
                    post.setCommentsCount(0);
                }
                postRepository.save(post);
                if (post.getPostType().equals(PostType.Story))
                    searchStoryRepository.save(searchStory);
                if (post.getPostType().equals(PostType.Post))
                    searchPostRepository.save(searchPost);
                commentRepository.delete(comment);
            }
        }
        return CommentId;
    }

    @Override
    public Page<PostDto> getStoriesByUserId(String userId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "update"));

        // Create criteria for user ID and story type
        Criteria criteria = Criteria.where("user.id").is(userId)
                .and("postType").is(PostType.Story);

        Query query = new Query(criteria).with(pageable);

        // Execute query
        List<Post> posts = mongoTemplate.find(query, Post.class);
        long total = mongoTemplate.count(Query.query(criteria), Post.class);

        // Convert to DTOs
        List<PostDto> dtos = posts.stream()
                .filter(Objects::nonNull)
                .map(this::convertToDto)
                .collect(Collectors.toList());

        return new PageImpl<>(dtos, pageable, total);
    }

    private Criteria createSearchSpecification(Map<String, Object> obj) {
        List<Criteria> criteriaList = new ArrayList<>();

        // Global text search criteria
        String searchTerm = obj.get("text") != null ? obj.get("text").toString() : "";
        if (!searchTerm.isEmpty()) {
            Criteria searchCriteria = new Criteria().orOperator(
                    Criteria.where("user.firstName").regex(searchTerm, "i"),
                    Criteria.where("text").regex(searchTerm, "i")
            );
            criteriaList.add(searchCriteria);
        }

        // Package filter criteria
        String packageId = obj.get("package") != null ? obj.get("package").toString() : "";
        if (!packageId.isEmpty()) {
            // Convert packageId to ObjectId if needed
            criteriaList.add(Criteria.where("Package._id").is(new ObjectId(packageId)));
        }

        // New: Filter on postType if provided
        String postType = obj.get("postType") != null ? obj.get("postType").toString() : "";
        if (!postType.isEmpty()) {
            criteriaList.add(Criteria.where("postType").is(postType));
        }

        // Combine all criteria with an AND operator
        Criteria query = new Criteria();
        if (!criteriaList.isEmpty()) {
            query.andOperator(criteriaList.toArray(new Criteria[0]));
        }

        System.out.println("Constructed Criteria: " + query.getCriteriaObject().toJson());
        return query;
    }

    private MediaWrapperSearch convertMediaWrapperToSearch(MediaWrapper wrapper) {
        MediaWrapperSearch search = new MediaWrapperSearch();
        search.setType(wrapper.getType());
        search.setCaption(wrapper.getCaption());
        search.setAsset(wrapper.getAsset());
        search.setUrl(wrapper.getUrl());
        if(wrapper.getMedia()!=null){
           SearchMedia searchMedia=  modelMapper.map(wrapper.getMedia(), SearchMedia.class);
           search.setMedia(searchMedia);
        }

        search.setMainImage(wrapper.isMainImage());
        return search;
    }

    //convertMediaWrapperToMediaWrapperDto
    private MediaWrapperDto convertMediaWrapperToDto(MediaWrapper wrapper) {
        MediaWrapperDto dto = new MediaWrapperDto();
        dto.setType(wrapper.getType());
        dto.setCaption(wrapper.getCaption());
        dto.setAsset(wrapper.getAsset());
        dto.setUrl(wrapper.getUrl());
        dto.setMainImage(wrapper.isMainImage());
        return dto;
    }

    private searchPost convertPostToSearchPost(Post post) {
        searchPost searchPost = new searchPost();
        // Map all fields manually
        searchPost.setId(post.getId());
        searchPost.setCreated(post.getCreated());
        searchPost.setUpdate(post.getUpdate());
        searchPost.setText(post.getText());

        // Manually convert media list
        if (post.getMedia() != null) {
            List<MediaWrapperSearch> mediaList = post.getMedia().stream()
                    .map(this::convertMediaWrapperToSearch)
                    .collect(Collectors.toList());
            searchPost.setMedia(mediaList);
        }

        // Map user
        if (post.getUser() != null) {
            simpleUserInfo userInfo = new simpleUserInfo();
            userInfo.setId(post.getUser().getId());
            userInfo.setUsername(post.getUser().getUsername());
            // Add other user properties if needed
            searchPost.setUser(userInfo);
        }

        // Map other fields
        searchPost.setTags(post.getTags());
        searchPost.setPostStatus(post.getPostStatus());
        searchPost.setRejectionNote(post.getRejectionNote());
        searchPost.setCommentsCount(post.getCommentsCount());
        searchPost.setReactsCount(post.getReactsCount());
        searchPost.setViewsCount(post.getViewsCount());
        searchPost.setSlug(post.getSlug());

        return searchPost;
    }

    private searchStory convertPostToSearchStory(Post post) {
        searchStory searchStory = new searchStory();
        // Same manual mapping as above
        searchStory.setId(post.getId());
        searchStory.setCreated(post.getCreated());
        searchStory.setUpdate(post.getUpdate());
        searchStory.setText(post.getText());
        // Manually convert media list
        if (post.getMedia() != null) {
            List<MediaWrapperSearch> mediaList = post.getMedia().stream()
                    .map(this::convertMediaWrapperToSearch)
                    .collect(Collectors.toList());
            searchStory.setMedia(mediaList);
        }

        // Map user
        if (post.getUser() != null) {
            simpleUserInfo userInfo = new simpleUserInfo();
            userInfo.setId(post.getUser().getId());
            userInfo.setUsername(post.getUser().getUsername());
            // Add other user properties if needed
            searchStory.setUser(userInfo);
        }

        // Map other fields
        searchStory.setTags(post.getTags());
        searchStory.setPostStatus(post.getPostStatus());
        searchStory.setRejectionNote(post.getRejectionNote());
        searchStory.setCommentsCount(post.getCommentsCount());
        searchStory.setReactsCount(post.getReactsCount());
        searchStory.setViewsCount(post.getViewsCount());
        searchStory.setSlug(post.getSlug());



        return searchStory;
    }

    // post  to post dto like the convertPostToSearchPost
    private PostDto convertPostToDto(Post post) {
        PostDto postDto = new PostDto();
        // Map all fields manually
        postDto.setId(post.getId());
        postDto.setCreated(post.getCreated());
        postDto.setUpdate(post.getUpdate());
        postDto.setText(post.getText());

        // Manually convert media list


        // Map user
        if (post.getUser() != null) {
            UserInfoDto userInfo = new UserInfoDto();
            userInfo.setId(post.getUser().getId());
            userInfo.setUsername(post.getUser().getUsername());
            // Add other user properties if needed
            postDto.setUser(userInfo);
        }

        // Map other fields
        postDto.setTags(post.getTags());
        postDto.setPostStatus(post.getPostStatus());
        postDto.setRejectionNote(post.getRejectionNote());
        postDto.setCommentsCount(post.getCommentsCount());
        postDto.setReactsCount(post.getReactsCount());
        postDto.setViewsCount(post.getViewsCount());
        postDto.setSlug(post.getSlug());

        return postDto;
    }

}
