package com.hb.crm.admin.dto.posts;

 import com.hb.crm.core.dtos.SimpleUserinfoDto;
 import com.hb.crm.core.searchBeans.simplePostInfo;
 import lombok.Data;
import org.springframework.data.annotation.Id;

import java.util.List;

@Data
public class HighLightViewDto {
    @Id
    private String id;
    private SimpleUserinfoDto influencer;
    private String name;
    private List<simplePostInfo> stories; // List of posts (stories)
    private String image; // URL or path to the image
}
