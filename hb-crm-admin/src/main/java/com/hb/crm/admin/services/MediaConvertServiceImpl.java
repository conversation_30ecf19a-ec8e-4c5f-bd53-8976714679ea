package com.hb.crm.admin.services;

import com.hb.crm.admin.services.interfaces.MediaConvertService;
import com.hb.crm.core.beans.Setting;
import com.hb.crm.core.repositories.SettingRepository;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.mediaconvert.MediaConvertClient;
import software.amazon.awssdk.services.mediaconvert.model.*;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
public class MediaConvertServiceImpl implements MediaConvertService {

    private final MediaConvertClient mediaConvertClient;
    private final S3Presigner s3Presigner;
    private  final MongoTemplate mongoTemplate;
     @Value("${cloud.aws.bucketName}")
      String bucketName;
     private  final SettingRepository settingRepository;

    public MediaConvertServiceImpl(MediaConvertClient mediaConvertClient, S3Presigner s3Presigner, MongoTemplate mongoTemplate, SettingRepository settingRepository) {
        this.mediaConvertClient = mediaConvertClient;
         this.s3Presigner = s3Presigner;
        this.mongoTemplate = mongoTemplate;
        this.settingRepository = settingRepository;
    }

    public String videoToTranscodedHls(String videoKey) {
        // Input video URL (e.g., s3://bucket-name/vids/video.mp4)
        String inputUrl = String.format("s3://%s/%s", bucketName, videoKey);

        // Replace any file extension with "_transcoded" (no extension needed for HLS)
        String outputHlsKey = videoKey.replaceAll("\\.[^.]+$", "_transcoded");
        // Output URL for the transcoded HLS files
        String outputVideoUrl = String.format("s3://%s/%s/", bucketName, outputHlsKey); // Add a trailing slash for the HLS folder

        String baseFileName = videoKey.substring(videoKey.lastIndexOf("/") + 1, videoKey.lastIndexOf("."));
        String m3u8Key = String.format("%s/%s.m3u8", outputHlsKey, baseFileName);



        // Define the AudioDescription
        AudioDescription audioDescription = AudioDescription.builder()
                .codecSettings(AudioCodecSettings.builder()
                        .codec(AudioCodec.AAC)
                        .aacSettings(AacSettings.builder()
                                .bitrate(128000) // Standard bitrate for stereo audio
                                .codingMode(AacCodingMode.CODING_MODE_2_0) // 2-channel stereo audio
                                .sampleRate(44100) // Common AAC sample rate
                                .build())
                        .build())
                .build();
        AudioSelector audioSelector = AudioSelector.builder()
                .defaultSelection(AudioDefaultSelection.DEFAULT) // Auto-select primary audio track
                .build();

        // Define the OutputGroup for transcoding to HLS
        OutputGroup outputGroup = OutputGroup.builder()
                .name("HLS HighLight")
                .customName("transcoded_hls")
                .outputGroupSettings(OutputGroupSettings.builder()
                        .type(OutputGroupType.HLS_GROUP_SETTINGS)
                        .hlsGroupSettings(HlsGroupSettings.builder()
                                .destination(outputVideoUrl) // Specify the output URL for the HLS files
                                .segmentLength(3) // Shorter HLS segment length (3 seconds for better seeking)
                                .minSegmentLength(0) // Minimum segment length
                                .directoryStructure(HlsDirectoryStructure.SINGLE_DIRECTORY) // All HLS files in a single directory
                                .manifestDurationFormat(HlsManifestDurationFormat.INTEGER) // Integer duration format for the manifest
                                .build())
                        .build())
                .outputs(Output.builder()
                        .containerSettings(ContainerSettings.builder()
                                .container(ContainerType.M3_U8) // Use M3U8 container for HLS
                                .build())
                        .videoDescription(VideoDescription.builder()
                                .codecSettings(VideoCodecSettings.builder()
                                        .codec(VideoCodec.H_264) // Keep H.264 codec
                                        .h264Settings(H264Settings.builder()
                                                .rateControlMode(H264RateControlMode.QVBR) // Use QVBR for quality-based encoding
                                                .maxBitrate(1500000) // Increase max bitrate for better quality
                                                .qualityTuningLevel(H264QualityTuningLevel.SINGLE_PASS_HQ) // Single-pass HQ for faster high-quality encoding
                                                .gopSize(2.0) // GOP size of 2 seconds (improves quality)
                                                .gopSizeUnits(H264GopSizeUnits.SECONDS)
                                                .numberBFramesBetweenReferenceFrames(2)
                                                .repeatPps(H264RepeatPps.DISABLED)
                                                .build())
                                        .build())
                                .build())
                        .audioDescriptions(Collections.singletonList(audioDescription))
                        .nameModifier("_hls") // Specify name modifier (required)
                        .build())
                .build();

        // MediaConvert job settings
        JobSettings jobSettings = JobSettings.builder()
                .inputs(Input.builder()
                        .fileInput(inputUrl)
                        .timecodeSource(InputTimecodeSource.ZEROBASED)
                        .audioSelectors(Collections.singletonMap("Audio Selector 1", audioSelector)) // Add audio selector
                        .build())
                .outputGroups(outputGroup) // Add output group for HLS
                .build();

        // Create the MediaConvert job request
        CreateJobRequest createJobRequest = CreateJobRequest.builder()
                .role("arn:aws:iam::891377395363:role/MediaConvertRole") // Specify your IAM role
                .settings(jobSettings)
                .build();

        // Submit the MediaConvert job
        CreateJobResponse createJobResponse = mediaConvertClient.createJob(createJobRequest);
        System.out.println("MediaConvert job submitted for HLS transcoding.");

        return m3u8Key; // Return the key of the HLS folder (no extension)
    }

    public String submitVideoToMp4Gif(String videoKey, long videoDuration) {
        // Input video URL (e.g., s3://bucket-name/vids/video.mp4)
        String inputUrl = String.format("s3://%s/%s", bucketName, videoKey);
            // Replace any file extension (e.g., .mp4, .avi, .mkv) with "_clip"
        String outputMp4Key = videoKey.replaceAll("\\.[^.]+$", "_clip");  // This will replace the file extension with "_clip"

        String outputVideoUrl = String.format("s3://%s/%s", bucketName, outputMp4Key);

        List<InputClipping> inputClippings = new ArrayList<>();

         Setting setting = settingRepository.findByName("ClipsNumber");
        Setting minimumDistanceSetting =  settingRepository.findByName("MinimumIntervalBetweenClips");

        long minimumIntervalBetweenClips  = Long.parseLong(minimumDistanceSetting.getValue()) ;
        // Divide the video into 10 equal intervals
        long totalClips = Long.parseLong(setting.getValue()); // We need exactly 10 clips
        long intervalBetweenClips = Math.max((videoDuration * 1000) / totalClips,minimumIntervalBetweenClips); // Interval between each clip in milliseconds

        if (intervalBetweenClips * totalClips > videoDuration * 1000) {
            totalClips = (videoDuration * 1000) / minimumIntervalBetweenClips;
        }


        // Create exactly 10 clips of 1 second each, spaced evenly over the video duration
        long startTime = 0;
        for (int i = 0; i < totalClips; i++) {
            long endTime = startTime + 1000; // Each clip lasts exactly 1 second

            String startTimecode = convertMillisToTimecode(startTime);
            String endTimecode = convertMillisToTimecode(endTime);

            inputClippings.add(InputClipping.builder()
                    .startTimecode(startTimecode)
                    .endTimecode(endTimecode)
                    .build());

            startTime += intervalBetweenClips; // Move to the next interval for the next clip
            // Stop if the next start time exceeds video duration
            if (startTime >= videoDuration * 1000) {
                break; // Don't add more clips if the start time is beyond the video duration
            }
        }

        // Create the OutputGroup for the MP4 videos
        OutputGroup thumbs = OutputGroup.builder()
                .name("File HighLight")
                .customName("clips")
                .outputGroupSettings(OutputGroupSettings.builder()
                        .type(OutputGroupType.FILE_GROUP_SETTINGS)
                        .fileGroupSettings(FileGroupSettings.builder()
                                .destination(outputVideoUrl) // Specify the output bucket URL
                                .build())
                        .build())
                .outputs(Output.builder()
                        .extension("mp4") // Set output as MP4
                        .containerSettings(ContainerSettings.builder()
                                .container(ContainerType.MP4) // Use MP4 container
                                .build())
                        .videoDescription(VideoDescription.builder()
                                .codecSettings(VideoCodecSettings.builder()
                                        .codec(VideoCodec.H_264) // Use H.264 Codec
                                        .h264Settings(H264Settings.builder()
                                                .rateControlMode(H264RateControlMode.CBR) // Constant Bitrate Mode
                                                .bitrate(300000) // Set desired bitrate
                                                .framerateControl(H264FramerateControl.SPECIFIED) // Specify frame rate
                                                .framerateNumerator(1) // Capture 1 frame per second
                                                .framerateDenominator(1) // Adjust frames based on the total duration
                                                .gopSize(1.0) // Set GOP size to 1 second
                                                .gopSizeUnits(H264GopSizeUnits.SECONDS) // Set GOP units to seconds
                                                .numberBFramesBetweenReferenceFrames(2) // Number of B-frames
                                                .repeatPps(H264RepeatPps.DISABLED) // Disable repeating PPS
                                                .build())
                                        .build())
                                .build())
                        .build())
                .build();

        // MediaConvert job settings
        JobSettings jobSettings = JobSettings.builder()
                .inputs(Input.builder()
                        .fileInput(inputUrl)
                        .timecodeSource(InputTimecodeSource.ZEROBASED) // Ensure timecode starts from zero
                        .inputClippings(inputClippings) // Add dynamic input clippings
                        .build())
                .outputGroups(thumbs) // Add the output group
                .build();

        CreateJobRequest createJobRequest = CreateJobRequest.builder()
                .role("arn:aws:iam::891377395363:role/MediaConvertRole") // Specify your IAM role
                .settings(jobSettings)
                .build();

        var x = mediaConvertClient.createJob(createJobRequest);
        System.out.println("Job submitted to convert video to MP4 with dynamic clipping.");

        return outputMp4Key;
    }
    public String submitVideoToMp4Job(String videoKey, long videoDuration) {
        // Input video URL (e.g., s3://bucket-name/vids/video.mp4)
        String inputUrl = String.format("s3://%s/%s", bucketName, videoKey);
        // Replace any file extension (e.g., .mp4, .avi, .mkv) with "_clip"
        String outputMp4Key = videoKey.replaceAll("\\.[^.]+$", "_clip");  // This will replace the file extension with "_clip"

        String outputVideoUrl = String.format("s3://%s/%s", bucketName, outputMp4Key);

        List<InputClipping> inputClippings = new ArrayList<>();

        Setting clipDurationsetting = settingRepository.findByName("clipDuration");

        // Define a single clip of 3 seconds
        long clipDuration = Long.parseLong(clipDurationsetting.getValue()); // 3 seconds in milliseconds
        long startTime = 0; // Start at the beginning of the video
        long endTime = startTime + clipDuration;

        // Convert start and end times to timecodes
        String startTimecode = convertMillisToTimecode(startTime);
        String endTimecode = convertMillisToTimecode(endTime);

        // Add the single clipping
        inputClippings.add(InputClipping.builder()
                .startTimecode(startTimecode)
                .endTimecode(endTimecode)
                .build());

        // Create the OutputGroup for the MP4 videos
        OutputGroup thumbs = OutputGroup.builder()
                .name("File HighLight")
                .customName("clips")
                .outputGroupSettings(OutputGroupSettings.builder()
                        .type(OutputGroupType.FILE_GROUP_SETTINGS)
                        .fileGroupSettings(FileGroupSettings.builder()
                                .destination(outputVideoUrl)
                                .build())
                        .build())
                .outputs(Output.builder()
                        .extension("mp4")
                        .containerSettings(ContainerSettings.builder()
                                .container(ContainerType.MP4)
                                .build())
                        .videoDescription(VideoDescription.builder()
                                .codecSettings(VideoCodecSettings.builder()
                                        .codec(VideoCodec.H_264)
                                        .h264Settings(H264Settings.builder()
                                                .rateControlMode(H264RateControlMode.VBR)
                                                .bitrate(550000)
                                                .build())
                                        .build())
                                .build())
                        .build())
                .build();
        // MediaConvert job settings
        JobSettings jobSettings = JobSettings.builder()
                .inputs(Input.builder()
                        .fileInput(inputUrl)
                        .timecodeSource(InputTimecodeSource.ZEROBASED) // Ensure timecode starts from zero
                        .inputClippings(inputClippings) // Add dynamic input clippings
                        .build())
                .outputGroups(thumbs) // Add the output group
                .build();

        CreateJobRequest createJobRequest = CreateJobRequest.builder()
                .role("arn:aws:iam::891377395363:role/MediaConvertRole") // Specify your IAM role
                .settings(jobSettings)
                .build();
        var x = mediaConvertClient.createJob(createJobRequest);
        System.out.println("Job submitted to convert video to MP4 with dynamic clipping.");

        return outputMp4Key+".mp4";
    }

    private String convertMillisToTimecode(long millis) {
        long hours = millis / (1000 * 60 * 60);
        millis %= (1000 * 60 * 60);
        long minutes = millis / (1000 * 60);
        millis %= (1000 * 60);
        long seconds = millis / 1000;
        long frames = (millis % 1000) / 40; // Assuming 25 frames per second
        return String.format("%02d:%02d:%02d:%02d", hours, minutes, seconds, frames);
    }






 }
