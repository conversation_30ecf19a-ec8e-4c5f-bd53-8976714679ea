package com.hb.crm.admin.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hb.crm.client.config.CustomSerializer.LocalDateTimeSerializer;
import com.hb.crm.core.Enums.PackageStatus;
import com.hb.crm.core.Enums.PackageType;
import com.hb.crm.core.Enums.State;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class TrashedSubPackageDto {
    private String id;
    private String name;
    private BigDecimal totalPrice;
    private PackageStatus packageStatus;
    private State state;
    private boolean privateDate;
    private int capacity;
    
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime start;
    
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime end;
    
    private String rejectionNote;
    private PackageType packageType;
    
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime creationDate;
    
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime updateDate;
    
    private int followMeDiscount;
    private int numberOfRoom;
    private int subscribeCount;
    private String slug;
    private String followMeSlug;
    
    // Trash-specific fields
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime deletedAt;
    
    private String deletionReason;
    private String originalSubPackageId;
    
    // Package information
    private String packageId;
    private String packageName;
    private String influencerName;
    private String influencerId;
}
