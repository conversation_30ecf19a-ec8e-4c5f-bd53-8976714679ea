package com.hb.crm.admin.controllers;

import com.hb.crm.admin.beans.UserSessionData;
import com.hb.crm.core.Enums.ConversationMessageType;
import com.hb.crm.core.Enums.chat.ChatMessageType;
import com.hb.crm.core.beans.chat.GroupChat.GroupConversation;
import com.hb.crm.core.dtos.EmployeeDto;
import com.hb.crm.core.dtos.PageDto;
import com.hb.crm.core.dtos.UserDto;
import com.hb.crm.core.dtos.chat.response.*;
import com.hb.crm.core.services.chat.ChatMessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/v1/chat", produces = {MediaType.APPLICATION_JSON_VALUE})
public class ChatController {

    private final ModelMapper modelMapper;
    private final UserSessionData userSessionData;
    private final ChatMessageService chatMessageService;

    @PostMapping("/group-conversation/package/{packageId}")
    @Operation(summary = "Create group conversation for package", 
              description = "Creates a new group conversation for a given package if one doesn't already exist")
    public ResponseEntity<GroupConversationResponseDto> createGroupConversation(
            @Parameter(description = "Package ID", required = true)
            @PathVariable String packageId
    ) {
        GroupConversation result = chatMessageService.createGroupConversation(packageId);
        return ResponseEntity.ok(modelMapper.map(result, GroupConversationResponseDto.class));
    }

    @PostMapping("/group-conversation/{conversationId}/users/{userId}")
    @Operation(summary = "Add user to group conversation", 
              description = "Adds a user to an existing group conversation and returns conversation details with recent messages")
    public ResponseEntity<UserConversationWithMassagesDto> addUserToGroupConversation(
            @Parameter(description = "HighLight conversation ID", required = true)
            @PathVariable String conversationId,
            
            @Parameter(description = "User ID to add")
            @PathVariable String userId
    ) {
        UserConversationWithMassagesDto result = chatMessageService.addUserToGroupConversation(conversationId, userId);
        return ResponseEntity.ok(result);
    }

    @DeleteMapping("/group-conversation/{conversationId}/users/{userId}")
    @Operation(summary = "Remove user from group conversation",
              description = "Removes a user from a group conversation")
    public ResponseEntity<Void> removeUserFromGroupConversation(
            @Parameter(description = "HighLight conversation ID", required = true)
            @PathVariable String conversationId,
            
            @Parameter(description = "User ID to remove")
            @PathVariable String userId
    ) {
        chatMessageService.removeUserFromGroupConversation(conversationId, userId);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/conversation/{conversationId}/history")
    @Operation(summary = "Get conversation history", description = "Retrieves paginated chat messages for a one-to-one conversation")
    public Page<ChatMessageResponseDto> getConversationHistory(
            @Parameter(description = "Conversation ID", required = true)
            @PathVariable String conversationId,
            
            @Parameter(description = "Page number (0-based)", schema = @Schema(type = "integer", minimum = "0"))
            @RequestParam(defaultValue = "0") int page,
            
            @Parameter(description = "Number of items per page", schema = @Schema(type = "integer", minimum = "1", maximum = "100"))
            @RequestParam(defaultValue = "20") int size,

            @Parameter(description = "Type of the messages")
            @RequestParam(required = false) List<ChatMessageType> type
    ) {
        return chatMessageService.getConversationHistory(conversationId, page, size, type);
    }

    @GetMapping("/group-conversation/{conversationId}/history")
    @Operation(summary = "Get group conversation history", description = "Retrieves paginated chat messages for a group conversation")
    public Page<GroupChatMessageResponseDto> getGroupConversationHistory(
            @Parameter(description = "HighLight conversation ID", required = true)
            @PathVariable String conversationId,
            
            @Parameter(description = "Page number (0-based)", schema = @Schema(type = "integer", minimum = "0"))
            @RequestParam(defaultValue = "0") int page,
            
            @Parameter(description = "Number of items per page", schema = @Schema(type = "integer", minimum = "1", maximum = "100"))
            @RequestParam(defaultValue = "20") int size,

            @Parameter(description = "Type of the messages")
            @RequestParam(required = false) List<ConversationMessageType> types
            ) {
        return chatMessageService.getGroupConversationHistory(conversationId, page, size, types);
    }

    @GetMapping("/group-conversation/{conversationId}/members")
    @Operation(summary = "Get group conversation members",
            description = "Retrieves paginated list of members in a group conversation. Filter by member status using includeInactive parameter.")
    public ResponseEntity<PageDto<UserDto>> getGroupMembers(
            @Parameter(description = "HighLight conversation ID", required = true)
            @PathVariable String conversationId,

            @Parameter(description = "Include inactive members", schema = @Schema(type = "boolean"))
            @RequestParam(defaultValue = "false") boolean includeInactive,

            @Parameter(description = "Page number (0-based)", schema = @Schema(type = "integer", minimum = "0"))
            @RequestParam(defaultValue = "0") int page,

            @Parameter(description = "Number of items per page", schema = @Schema(type = "integer", minimum = "1", maximum = "100"))
            @RequestParam(defaultValue = "20") int size
    ) {
        PageDto<com.hb.crm.core.dtos.UserDto> members = chatMessageService.getGroupMembers(conversationId, includeInactive, page, size);
        return ResponseEntity.ok(members);
    }

    @GetMapping("/group-conversation/{conversationId}/admins")
    @Operation(summary = "Get group conversation admins", 
              description = "Retrieves paginated list of admins for a group conversation. Filter by admin status using includeInactive parameter.")
    public ResponseEntity<PageDto<EmployeeDto>> getGroupAdmins(
            @Parameter(description = "HighLight conversation ID", required = true)
            @PathVariable String conversationId,
            
            @Parameter(description = "Include inactive admins", schema = @Schema(type = "boolean"))
            @RequestParam(defaultValue = "false") boolean includeInactive,
            
            @Parameter(description = "Page number (0-based)", schema = @Schema(type = "integer", minimum = "0"))
            @RequestParam(defaultValue = "0") int page,
            
            @Parameter(description = "Number of items per page", schema = @Schema(type = "integer", minimum = "1", maximum = "100"))
            @RequestParam(defaultValue = "20") int size
    ) {
        PageDto<EmployeeDto> admins = chatMessageService.getGroupAdmins(conversationId, includeInactive, page, size);
        return ResponseEntity.ok(admins);
    }

    @PostMapping("/group-conversation/{conversationId}/admins/{employeeId}")
    @Operation(summary = "Add admin to group conversation", 
              description = "Adds an employee as an admin to the specified group conversation")
    public ResponseEntity<UserConversationWithMassagesDto> addAdminToGroupConversation(
            @Parameter(description = "HighLight conversation ID", required = true)
            @PathVariable String conversationId,
            
            @Parameter(description = "Employee ID to add as admin", required = true)
            @PathVariable String employeeId
    ) {
        UserConversationWithMassagesDto result = chatMessageService.addAdminToGroupConversation(conversationId, employeeId);
        return ResponseEntity.ok(result);
    }


    @DeleteMapping("/group-conversation/{conversationId}/admins/{employeeId}")
    @Operation(summary = "Remove admin from group conversation",
            description = "Removes an employee from admin role in the specified group conversation")
    public ResponseEntity<Void> removeAdmin(
            @Parameter(description = "HighLight conversation ID", required = true)
            @PathVariable String conversationId,

            @Parameter(description = "Employee ID to remove from admin role", required = true)
            @PathVariable String employeeId
    ) {
        chatMessageService.removeAdminFromGroupConversation(conversationId, employeeId);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/user-chat/conversation")
    @Operation(summary = "Create new conversation",
            description = "Creates a new conversation and associates it with a user")
    public ResponseEntity<ConversationDto> createConversation(
            @Parameter(description = "User ID", required = true)
            @RequestParam String userId,

            @Parameter(description = "Conversation topic", required = true)
            @RequestParam String topic
    ) {
        var result = chatMessageService.createConversation(userId, topic);
        return ResponseEntity.ok(result);
    }

    @PutMapping("/user-chat/{conversationId}/settings")
    @Operation(summary = "Update conversation settings",
            description = "Updates mute and closed status for a conversation")
    public ResponseEntity<ConversationDto> updateConversationSettings(
            @Parameter(description = "Conversation ID", required = true)
            @PathVariable String conversationId,

            @Parameter(description = "Mute status (true to mute, false to unmute)")
            @RequestParam(defaultValue = "false") Boolean isMuted,

            @Parameter(description = "Closed status (true to close, false to open)")
            @RequestParam(defaultValue = "false") Boolean isClosed
    ) {
        ConversationDto result = chatMessageService.updateConversationSettings(conversationId, isMuted, isClosed);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/chats/unread")
    @Operation(summary = "Get all chats with unread counts",
            description = "Retrieves all chats (both one-to-one and group) with their unread message counts")
    public ResponseEntity<List<ChatWithUnreadCountDto>> getAllChatsWithUnreadCount(
            @Parameter(description = "User ID", required = true)
            @RequestParam String userId
    ) {
        List<ChatWithUnreadCountDto> chats = chatMessageService.getAllChatsWithUnreadCount(userId);
        return ResponseEntity.ok(chats);
    }

    @GetMapping("/messages/search/{userId}")
    @Operation(summary = "Search user messages",
              description = "Search messages across all conversations for a specific user with text and date filters")
    public ResponseEntity<PageDto<MessageSearchResultDto>> searchUserMessages(
            @Parameter(description = "Search text")
            @RequestParam(required = false) String searchText,
            
            @Parameter(description = "Start date (ISO format)")
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            
            @Parameter(description = "End date (ISO format)")
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            
            @Parameter(description = "Page number (0-based)")
            @RequestParam(defaultValue = "0") int page,
            
            @Parameter(description = "Number of items per page")
            @RequestParam(defaultValue = "20") int size
    ) {
        PageDto<MessageSearchResultDto> results = chatMessageService.searchMessages(searchText, startDate, endDate,
                page, size
        );
        return ResponseEntity.ok(results);
    }

    @PostMapping("/queue/consume")
    @Operation(
        summary = "Consume a conversation from the main queue",
        description = "Assigns the oldest conversation from the main queue to the specified agent"
    )
    
    public ResponseEntity<ConversationDto> consumeConversationFromQueue(
            @Parameter(description = "Agent ID who will receive the conversation", required = true)
            @RequestParam String agentId
    ) {
        ConversationDto conversation = chatMessageService.consumeChatFromQueue(agentId);
        return ResponseEntity.ok(conversation);
    }

    @PostMapping("/queue/transfer-chat")
    @Operation(
        summary = "Transfer a chat from one agent to another",
        description = "Moves a specific conversation from one agent's queue to another agent's queue"
    )
    public ResponseEntity<Map<String, String>> transferChatToAgent(
            @Parameter(description = "Conversation ID to transfer", required = true)
            @RequestParam String conversationId,
            
            @Parameter(description = "Source agent ID", required = true)
            @RequestParam String fromAgentId,
            
            @Parameter(description = "Target agent ID", required = true)
            @RequestParam String toAgentId
    ) {
        chatMessageService.transferChatToAgent(conversationId, fromAgentId, toAgentId);
        Map<String, String> response = new HashMap<>();
        response.put("message", "Chat successfully transferred");
        return ResponseEntity.ok(response);
    }


    @PostMapping("/queue/assign")
    @Operation(
            summary = "Assign a conversation to an agent",
            description = "Adds a conversation to an agent's queue without removing it from its current location"
    )
    public ResponseEntity<Map<String, String>> assignChatToAgent(
            @Parameter(description = "Conversation to assign", required = true)
            @RequestParam String conversationId,

            @Parameter(description = "Agent ID who will receive the conversation", required = true)
            @RequestParam String agentId
    ) {
        chatMessageService.assignChatToAgent(conversationId, agentId);
        Map<String, String> response = new HashMap<>();
        response.put("message", "Conversation successfully assigned to agent");
        return ResponseEntity.ok(response);
    }

    @GetMapping("/agent/{agentId}/consume")
    @Operation(
            summary = "Consume a conversation from an agent's queue",
            description = "Retrieves and removes the oldest conversation from the specified agent's queue"
    )
    public ResponseEntity<ConversationDto> consumeChatFromAgentQueue(
            @Parameter(description = "ID of the agent whose queue to consume from", required = true)
            @PathVariable String agentId) {
        return ResponseEntity.ok(chatMessageService.consumeChatFromAgentQueue(agentId));
    }

    @GetMapping("/agent/{agentId}")
    @Operation(
            summary = "Get all conversations in an agent's queue",
            description = "Retrieves all conversations currently assigned to the specified agent's queue"
    )
    public ResponseEntity<List<ConversationDto>> getAgentQueue(
            @Parameter(description = "ID of the agent whose queue to retrieve", required = true)
            @PathVariable String agentId) {
        return ResponseEntity.ok(chatMessageService.getAllAgentQueueContent(agentId));
    }

    @GetMapping("/main")
    @Operation(
            summary = "Get all conversations in the main queue",
            description = "Retrieves all conversations currently in the main queue, ordered by timestamp"
    )
    public ResponseEntity<List<ConversationDto>> getMainQueue() {
        return ResponseEntity.ok(chatMessageService.getMainQueue());
    }

    @PostMapping("/{conversationId}/close/{agentId}")
    public ResponseEntity<Map<String, String>> closeConversationForAgent(
            @PathVariable String conversationId,
            @PathVariable String agentId) {
        chatMessageService.closeConversationForAgent(conversationId, agentId);
        return ResponseEntity.ok(new HashMap<>(){{put("message", "Closed successfully");}});
    }

    @PostMapping("/{conversationId}/open/{agentId}")
    public ResponseEntity<Map<String, String>> openConversationForAgent(
            @PathVariable String conversationId,
            @PathVariable String agentId) {
        chatMessageService.openConversationForAgent(conversationId, agentId);
        return ResponseEntity.ok(new HashMap<>(){{put("message", "Opened successfully");}});
    }

    @GetMapping("/queue-position/{conversationId}")
    public ResponseEntity<Map<String, Object>> getConversationQueuePosition(
            @PathVariable String conversationId) {

        int position = chatMessageService.getConversationQueuePosition(conversationId);
        Map<String, Object> response = new HashMap<>();
        response.put("conversationId", conversationId);
        response.put("position", position);

        return ResponseEntity.ok(response);
    }
}
