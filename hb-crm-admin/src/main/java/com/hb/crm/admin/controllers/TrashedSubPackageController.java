package com.hb.crm.admin.controllers;

import com.hb.crm.admin.dto.PageDto;
import com.hb.crm.admin.dto.SoftDeleteRequest;
import com.hb.crm.admin.dto.TrashedSubPackageDto;
import com.hb.crm.admin.services.interfaces.TrashedSubPackageService;
import com.hb.crm.core.Enums.PackageType;
import com.hb.crm.core.beans.SubPackage;
import com.hb.crm.core.beans.Trash.TrashedSubPackage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/admin/trash/subpackages")
@Tag(name = "Trashed SubPackages", description = "APIs for managing trashed (soft deleted) SubPackages")
public class TrashedSubPackageController {

    @Autowired
    private TrashedSubPackageService trashedSubPackageService;

    @Autowired
    private ModelMapper modelMapper;

    @Operation(summary = "Soft delete a SubPackage", 
               description = "Moves a SubPackage to trash instead of permanently deleting it")
    @PostMapping("/soft-delete")
    public ResponseEntity<Map<String, Object>> softDeleteSubPackage(@RequestBody SoftDeleteRequest request) {
        try {
            TrashedSubPackage trashedSubPackage = trashedSubPackageService.softDeleteSubPackage(
                request.getSubPackageId(), 
                request.getDeletionReason()
            );
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "SubPackage moved to trash successfully");
            response.put("trashedSubPackageId", trashedSubPackage.getId());
            response.put("deletedAt", trashedSubPackage.getDeletedAt());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("message", "Error moving SubPackage to trash: " + e.getMessage()));
        }
    }

    @Operation(summary = "Restore a SubPackage from trash", 
               description = "Restores a trashed SubPackage back to active state")
    @PostMapping("/restore/{trashedSubPackageId}")
    public ResponseEntity<Map<String, Object>> restoreSubPackage(
            @Parameter(description = "ID of the trashed SubPackage to restore") 
            @PathVariable String trashedSubPackageId) {
        try {
            SubPackage restoredSubPackage = trashedSubPackageService.restoreSubPackage(trashedSubPackageId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "SubPackage restored successfully");
            response.put("restoredSubPackageId", restoredSubPackage.getId());
            response.put("restoredAt", LocalDateTime.now());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("message", "Error restoring SubPackage: " + e.getMessage()));
        }
    }

    @Operation(summary = "Get all trashed SubPackages with pagination", 
               description = "Retrieves all trashed SubPackages with pagination support")
    @GetMapping
    public ResponseEntity<PageDto<TrashedSubPackage>> getTrashedPackages(
            @Parameter(description = "Page number (zero-based)") 
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Number of items per page") 
            @RequestParam(defaultValue = "10") int size) {
        
        PageDto<TrashedSubPackage> result = trashedSubPackageService.getTrashedPackages(page, size);
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "Get trashed SubPackages by type with pagination", 
               description = "Retrieves trashed SubPackages filtered by package type")
    @GetMapping("/by-type/{packageType}")
    public ResponseEntity<PageDto<TrashedSubPackage>> getTrashedPackagesByType(
            @Parameter(description = "Package type filter") 
            @PathVariable PackageType packageType,
            @Parameter(description = "Page number (zero-based)") 
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Number of items per page") 
            @RequestParam(defaultValue = "10") int size) {
        
        PageDto<TrashedSubPackage> result = trashedSubPackageService.getTrashedPackagesByType(packageType, page, size);
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "Search trashed SubPackages with fuzzy search", 
               description = "Searches trashed SubPackages using fuzzy search with pagination")
    @GetMapping("/search")
    public ResponseEntity<PageDto<TrashedSubPackage>> searchTrashedPackages(
            @Parameter(description = "Search query for fuzzy search") 
            @RequestParam(required = false) String query,
            @Parameter(description = "Package type filter (optional)") 
            @RequestParam(required = false) PackageType packageType,
            @Parameter(description = "Page number (zero-based)") 
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Number of items per page") 
            @RequestParam(defaultValue = "10") int size) {
        
        PageDto<TrashedSubPackage> result;
        if (packageType != null) {
            result = trashedSubPackageService.searchTrashedPackages(query, packageType, page, size);
        } else {
            result = trashedSubPackageService.searchTrashedPackages(query, page, size);
        }
        
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "Get trashed SubPackages by date range", 
               description = "Retrieves trashed SubPackages deleted within a specific date range")
    @GetMapping("/by-date-range")
    public ResponseEntity<PageDto<TrashedSubPackage>> getTrashedPackagesByDateRange(
            @Parameter(description = "Start date (ISO format: yyyy-MM-dd'T'HH:mm:ss)") 
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "End date (ISO format: yyyy-MM-dd'T'HH:mm:ss)") 
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            @Parameter(description = "Page number (zero-based)") 
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Number of items per page") 
            @RequestParam(defaultValue = "10") int size) {
        
        PageDto<TrashedSubPackage> result = trashedSubPackageService.getTrashedPackagesByDateRange(startDate, endDate, page, size);
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "Get a specific trashed SubPackage by ID", 
               description = "Retrieves details of a specific trashed SubPackage")
    @GetMapping("/{trashedSubPackageId}")
    public ResponseEntity<TrashedSubPackage> getTrashedPackageById(
            @Parameter(description = "ID of the trashed SubPackage") 
            @PathVariable String trashedSubPackageId) {
        
        TrashedSubPackage trashedSubPackage = trashedSubPackageService.getTrashedPackageById(trashedSubPackageId);
        return ResponseEntity.ok(trashedSubPackage);
    }

    @Operation(summary = "Permanently delete a trashed SubPackage", 
               description = "Permanently deletes a trashed SubPackage (cannot be undone)")
    @DeleteMapping("/permanent/{trashedSubPackageId}")
    public ResponseEntity<Map<String, String>> permanentlyDeleteTrashedPackage(
            @Parameter(description = "ID of the trashed SubPackage to permanently delete") 
            @PathVariable String trashedSubPackageId) {
        try {
            trashedSubPackageService.permanentlyDeleteTrashedPackage(trashedSubPackageId);
            return ResponseEntity.ok(Map.of("message", "TrashedSubPackage permanently deleted"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("message", "Error permanently deleting TrashedSubPackage: " + e.getMessage()));
        }
    }

    @Operation(summary = "Empty entire trash", 
               description = "Permanently deletes all trashed SubPackages (cannot be undone)")
    @DeleteMapping("/empty-trash")
    public ResponseEntity<Map<String, String>> emptyTrash() {
        try {
            trashedSubPackageService.emptyTrash();
            return ResponseEntity.ok(Map.of("message", "Trash emptied successfully"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("message", "Error emptying trash: " + e.getMessage()));
        }
    }

    @Operation(summary = "Get trash statistics", 
               description = "Returns count statistics for trashed SubPackages")
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getTrashStats(
            @Parameter(description = "Package type filter (optional)") 
            @RequestParam(required = false) PackageType packageType) {
        
        Map<String, Object> stats = new HashMap<>();
        
        if (packageType != null) {
            stats.put("count", trashedSubPackageService.getTrashedPackagesCount(packageType));
            stats.put("packageType", packageType);
        } else {
            stats.put("totalCount", trashedSubPackageService.getTotalTrashedPackagesCount());
            stats.put("travelWithMeCount", trashedSubPackageService.getTrashedPackagesCount(PackageType.TravelWithMe));
            stats.put("followMeCount", trashedSubPackageService.getTrashedPackagesCount(PackageType.FollowMe));
        }
        
        return ResponseEntity.ok(stats);
    }

    @Operation(summary = "Check if SubPackage is in trash", 
               description = "Checks if a specific SubPackage is currently in trash")
    @GetMapping("/check/{subPackageId}")
    public ResponseEntity<Map<String, Object>> checkIfInTrash(
            @Parameter(description = "Original SubPackage ID to check") 
            @PathVariable String subPackageId) {
        
        boolean inTrash = trashedSubPackageService.isSubPackageInTrash(subPackageId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("subPackageId", subPackageId);
        response.put("inTrash", inTrash);
        
        return ResponseEntity.ok(response);
    }
}
