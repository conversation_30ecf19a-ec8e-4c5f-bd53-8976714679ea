package com.hb.crm.admin.services.interfaces;

import com.hb.crm.admin.dto.PageDto;
import com.hb.crm.admin.dto.posts.*;
import com.hb.crm.admin.dto.search.searchResultDto;

import java.util.List;
public interface HighLightService {

    HighLightViewDto createOrUpdateHighLight(GroupDTO groupDTO , String influencerId);
    // Add stories to the group (replace existing ones)
    HighLightViewDto addStoryToHighLight(String groupId, List<String> storyIds);
    PageDto<HighLightListDTO> getHighLightsForInfluencer(String username, String influencerId, String keyword, int page, int size);
    // Get stories by group ID with a keyword search
    searchResultDto getPostsByGroupId(String id, String query, int page, int limit);
    searchResultDto getStoriesByInfulancer(String influencerId,String query, int page, int limit);

    void deleteHighLight(String groupId);
    HighLightDetailDto getHighLightById(String groupId);

    PageDto<HighLightWithStoryCountDTO> getHighLightsWithStoryCount(String influencerId, String keyword, int page, int size);
}
