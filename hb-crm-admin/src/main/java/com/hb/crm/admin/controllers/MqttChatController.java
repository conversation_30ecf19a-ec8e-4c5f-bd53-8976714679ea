package com.hb.crm.admin.controllers;

import com.hb.crm.admin.services.MqttPublisherService;
import com.hb.crm.core.Enums.chat.MQTTMessageType;
import com.hb.crm.core.dtos.MessageWrapper;
import com.hb.crm.core.dtos.chat.request.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/v1/mqtt/chat", produces = {MediaType.APPLICATION_JSON_VALUE})
@Tag(name = "MQTT Chat", description = "MQTT chat messaging endpoints (((((Only For Testing))))")
public class MqttChatController {

    private final MqttPublisherService mqttPublisherService;
    private final ObjectMapper objectMapper;

    @PostMapping("/one-to-one")
    @Operation(summary = "Send one-to-one chat message via MQTT",
            description = "Publishes a ChatMessage to the appropriate MQTT topic or update existing one")
    public ResponseEntity<String> sendChatMessage(
            @Parameter(description = "Chat message details", required = true)
            @RequestBody ChatMessageRequestDto chatMessage,
            
            @Parameter(description = "Topic to publish to", required = true)
            @RequestParam String topic) {

        // Create message wrapper
        MessageWrapper wrapper = new MessageWrapper();
        wrapper.setType(MQTTMessageType.Chat);
        wrapper.setPayload(chatMessage);
        String messageJson;

        try {
            // Convert to JSON
            messageJson = objectMapper.writeValueAsString(wrapper);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }

        // Publish to MQTT
        mqttPublisherService.publishChatMessage(topic, messageJson);
        return ResponseEntity.ok("Success");
    }

    @PostMapping("/group")
    @Operation(summary = "Send group chat message via MQTT",
            description = "Publishes a GroupChatMessage to the appropriate MQTT topic or update existing one")
    public ResponseEntity<String> sendGroupChatMessage(
            @Parameter(description = "HighLight chat message details", required = true)
            @RequestBody GroupChatMessageRequestDto groupChatMessage,
            
            @Parameter(description = "Topic to publish to", required = true)
            @RequestParam String topic) {

        // Create message wrapper
        MessageWrapper wrapper = new MessageWrapper();
        wrapper.setType(MQTTMessageType.Chat);
        wrapper.setPayload(groupChatMessage);
        String messageJson;

        try {
            // Convert to JSON
            messageJson = objectMapper.writeValueAsString(wrapper);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }

        // Publish to MQTT
        mqttPublisherService.publishChatMessage(topic, messageJson);
        return ResponseEntity.ok("Success");
    }
    
    @PostMapping("/one-to-one/vote")
    @Operation(summary = "Send vote for one-to-one chat message via MQTT",
            description = "Publishes a vote message to the appropriate MQTT topic")
    public ResponseEntity<String> sendVoteForChatMessage(
            @Parameter(description = "Vote message details", required = true)
            @RequestBody VoteMessageRequestDto voteMessage,
            
            @Parameter(description = "Topic to publish to", required = true)
            @RequestParam String topic) {

        // Create message wrapper
        MessageWrapper wrapper = new MessageWrapper();
        wrapper.setType(MQTTMessageType.Vote);
        wrapper.setPayload(voteMessage);
        String messageJson;

        try {
            // Convert to JSON
            messageJson = objectMapper.writeValueAsString(wrapper);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }

        // Publish to MQTT
        mqttPublisherService.publishChatMessage(topic, messageJson);
        return ResponseEntity.ok("Success");
    }
    
    @PostMapping("/group/vote")
    @Operation(summary = "Send vote for group chat message via MQTT",
            description = "Publishes a vote message to the appropriate group MQTT topic")
    public ResponseEntity<String> sendVoteForGroupChatMessage(
            @Parameter(description = "Vote message details", required = true)
            @RequestBody VoteMessageRequestDto voteMessage,
            
            @Parameter(description = "Topic to publish to", required = true)
            @RequestParam String topic) {

        // Create message wrapper
        MessageWrapper wrapper = new MessageWrapper();
        wrapper.setType(MQTTMessageType.Vote);
        wrapper.setPayload(voteMessage);
        String messageJson;

        try {
            // Convert to JSON
            messageJson = objectMapper.writeValueAsString(wrapper);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }

        // Publish to MQTT
        mqttPublisherService.publishChatMessage(topic, messageJson);
        return ResponseEntity.ok("Success");
    }
    
    @PostMapping("/one-to-one/status")
    @Operation(summary = "Update one-to-one chat message status via MQTT",
            description = "Publishes a status update message to the appropriate MQTT topic")
    public ResponseEntity<String> updateChatMessageStatus(
            @Parameter(description = "Status update details", required = true)
            @RequestBody UpdateChatMessageStatusRequest statusUpdate,
            
            @Parameter(description = "Topic to publish to", required = true)
            @RequestParam String topic) {
        // Create message wrapper
        MessageWrapper wrapper = new MessageWrapper();
        wrapper.setType(MQTTMessageType.SeeEvent);
        wrapper.setPayload(statusUpdate);
        String messageJson;

        try {
            // Convert to JSON
            messageJson = objectMapper.writeValueAsString(wrapper);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }

        // Publish to MQTT
        mqttPublisherService.publishChatMessage(topic, messageJson);
        return ResponseEntity.ok("Success");
    }
    
    @PostMapping("/group/status")
    @Operation(summary = "Update group chat message status via MQTT",
            description = "Publishes a status update message to the appropriate group MQTT topic")
    public ResponseEntity<String> updateGroupChatMessageStatus(
            @Parameter(description = "Status update details", required = true)
            @RequestBody UpdateGroupChatMessageStatusRequest statusUpdate,
            
            @Parameter(description = "Topic to publish to", required = true)
            @RequestParam String topic) {

        // Create message wrapper
        MessageWrapper wrapper = new MessageWrapper();
        wrapper.setType(MQTTMessageType.SeeEvent);
        wrapper.setPayload(statusUpdate);
        String messageJson;

        try {
            // Convert to JSON
            messageJson = objectMapper.writeValueAsString(wrapper);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }

        // Publish to MQTT
        mqttPublisherService.publishChatMessage(topic, messageJson);
        return ResponseEntity.ok("Success");
    }

}
