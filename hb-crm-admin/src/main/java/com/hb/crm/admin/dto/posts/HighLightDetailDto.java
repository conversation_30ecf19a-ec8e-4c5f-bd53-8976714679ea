package com.hb.crm.admin.dto.posts;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor

public class HighLightDetailDto {
    private String id;
    private String name;
    private String image;

    // Influencer information
    private String influencerId;
    private String influencerFirstName;
    private String influencerLastName;
    private String influencerProfileImage;
//

    // Stories information
    private List<PostDto> stories;

}
