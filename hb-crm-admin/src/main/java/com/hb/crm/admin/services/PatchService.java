package com.hb.crm.admin.services;

import com.hb.crm.admin.config.CustomHundlerar.CustomException;
import com.hb.crm.admin.config.DateFormater;
import com.hb.crm.admin.services.interfaces.MediaConvertService;
import com.hb.crm.core.CombinedKeys.FollowsKey;
import com.hb.crm.core.Enums.*;
import com.hb.crm.core.beans.*;
import com.hb.crm.core.beans.FlightPackage.Airport;
import com.hb.crm.core.beans.Notification.NotificationSetting;
import com.hb.crm.core.beans.Package;
import com.hb.crm.core.beans.Notification.PackageNotificationSetting;
import com.hb.crm.core.beans.PackagePlaces.PackageArea;
import com.hb.crm.core.beans.PackagePlaces.PackageCity;
import com.hb.crm.core.beans.PackagePlaces.PackageCountry;
import com.hb.crm.core.repositories.*;
import com.hb.crm.core.searchBeans.*;
import com.hb.crm.core.searchRepositories.*;
import com.hb.crm.core.util.UsernameUtil;
import com.mongodb.client.result.UpdateResult;
import org.bson.types.ObjectId;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.LookupOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;

@Service
 public class PatchService {

    private static final Map<String, AtomicBoolean> locks = new ConcurrentHashMap<>();

    // Mapping of old state names (String) to new integer values
    private static final Map<String, Integer> STATE_MAP = Map.of(
            "NotStarted", 0,
            "Canceled", 1,
            "Completed", 2,
            "OnGoing", 3
    );
    private final MongoTemplate mongoTemplate;
    private final MongoTemplate mongoTemplate2;
    private final UserRepository userRepository;
    private final FollowsRepository followsRepository;
    private final SearchUserRepository searchUserRepository;
    private final SubPackageRepository subPackageRepository;
    private final PostReactionRepository postReactionRepository;
    private final SearchPackageRepository searchPackageRepository;
    private final ReactionSearchRepository reactionSearchRepository;
    private final PostRepository postRepository;
    private final TagRepository tagRepository;
    private final SearchStoryRepository searchStoryRepository;
    private final AirportRepository airportRepository;
    private final MediaRepository mediaRepository;
    private final SearchPostRepository searchPostRepository;
    private final ModelMapper modelMapper;
    private final PackageRepository packageRepository;
    private final SubscribeRepository subscribeRepository;
    private final DataFixRepository dataFixRepository;
    private final CountryRepository countryRepository;
    private final CityRepository cityRepository;
    private final AreaRepository areaRepository;
    private  final MediaConvertService mediaConvertService;

    private final NotificationSettingRepository notificationSettingRepository;
    private final Logger logger = LoggerFactory.getLogger(PatchService.class);
    public PatchService(@Qualifier("mongoTemplate2") MongoTemplate mongoTemplate2, MongoTemplate mongoTemplate, UserRepository userRepository, FollowsRepository followsRepository, SearchUserRepository searchUserRepository, SubPackageRepository subPackageRepository, PostReactionRepository postReactionRepository, SearchPackageRepository searchPackageRepository, ReactionSearchRepository reactionSearchRepository, PostRepository postRepository, TagRepository tagRepository, SearchStoryRepository searchStoryRepository, AirportRepository airportRepository, MediaRepository mediaRepository, SearchPostRepository searchPostRepository, ModelMapper modelMapper, PackageRepository packageRepository, SubscribeRepository subscribeRepository, DataFixRepository dataFixRepository, CountryRepository countryRepository, CityRepository cityRepository, AreaRepository areaRepository, MediaConvertService mediaConvertService, NotificationSettingRepository notificationSettingRepository) {
        this.mongoTemplate = mongoTemplate;
        this.userRepository = userRepository;
        this.followsRepository = followsRepository;
        this.searchUserRepository = searchUserRepository;
        this.subPackageRepository = subPackageRepository;
        this.postReactionRepository = postReactionRepository;
        this.searchPackageRepository = searchPackageRepository;
        this.reactionSearchRepository = reactionSearchRepository;
        this.postRepository = postRepository;
        this.tagRepository = tagRepository;
        this.searchStoryRepository = searchStoryRepository;
        this.airportRepository = airportRepository;
        this.mediaRepository = mediaRepository;
        this.searchPostRepository = searchPostRepository;
        this.modelMapper = modelMapper;
        this.packageRepository = packageRepository;
        this.subscribeRepository = subscribeRepository;
        this.dataFixRepository = dataFixRepository;
        this.countryRepository = countryRepository;
        this.cityRepository = cityRepository;
        this.areaRepository = areaRepository;
        this.mediaConvertService = mediaConvertService;
        this.notificationSettingRepository = notificationSettingRepository;

        this.mongoTemplate2 = mongoTemplate2;
    }

    public List<DataFix> getAllDataFix() {
        return dataFixRepository.findAll();
    }

    public DataFix getDataFixByName(String name) {
        var dataFix = dataFixRepository.findByName(name);
        if (dataFix == null)
            throw new CustomException(400, "Something went wrong, the name of data fix record not found!");
        return dataFix;
    }

    @Async
    @Transactional
    public void matchAndUpdateSearchUsers() {
        if (!acquireLock("SearchUsers")) {
            throw new CustomException(400, "Search Packages update process is already running.");
        }

        DataFix dataFix = dataFixRepository.findByName("Search Users");
        if (dataFix == null) {
            dataFix = new DataFix(null, "Search Users", "Update search users data",
                    "POST", "/update-search-users", LocalDateTime.now(), DataFixStatus.IN_PROGRESS);
        }

        try {
            dataFix.setStatus(DataFixStatus.IN_PROGRESS);
            dataFixRepository.save(dataFix);

            List<SearchUser> searchUsers = searchUserRepository.findAll();
            Map<String, User> userMap = getUserFields().stream()
                    .collect(Collectors.toMap(User::getId, user -> user));

            System.out.println("the number of users is " + userMap.size());

            userMap.forEach((userId, user) -> {
                if (!searchUserRepository.existsById(userId)) {
                    SearchUser searchUser = new SearchUser(user);
                    searchUserRepository.save(searchUser);
                }
            });

            var count = 0;
            for (SearchUser searchUser : searchUsers) {
                User user = userMap.get(searchUser.getId());

                if (user == null) {
                    searchUserRepository.deleteById(searchUser.getId());
                    continue;
                }

                searchUser.setAbout(user.getAbout());
                searchUser.setFirstName(user.getFirstName());
                searchUser.setLastName(user.getLastName());
                searchUser.setGuestEmail(user.getGuestEmail());
                searchUser.setUsername(user.getUsername());
                searchUser.setCity(user.getCity());
                searchUser.setCountry(user.getCountry());
                searchUser.setProfileImage(user.getProfileImage());
                searchUser.setCoverImage(user.getCoverImage());
                searchUser.setGender(user.getGender());
                searchUser.setUsertype(user.getUsertype());
                searchUser.setFollwerscount(user.getFollwerscount());
                searchUser.setFollowingcount(user.getFollowingcount());
                searchUser.setMedias(user.getMedias().stream().map(z->modelMapper.map(z, MediaWrapperSearch.class)).collect(Collectors.toList()));
                searchUser.setMoods(user.getMoods());

                if (user.getUserInfo() != null) {
                    searchUser.setEmail(user.getUserInfo().getEmail());
                    searchUser.setMobile(user.getUserInfo().getMobile());
                }

                searchUserRepository.save(searchUser);
                count++;
            }

            dataFix.setStatus(DataFixStatus.SUCCEEDED);
            dataFix.setDate(LocalDateTime.now());
            dataFixRepository.save(dataFix);
            System.out.println("The count is " + count);

        } catch (Exception e) {
            dataFix.setStatus(DataFixStatus.FAILED);
            dataFixRepository.save(dataFix);
            throw new RuntimeException(e);

        } finally {
            releaseLock("SearchUsers");
        }
    }

    @Async
    @Transactional
    public void updateSearchPackageDataAsync() {
        if (!acquireLock("SearchPackages")) {
            logger.warn("Search Packages update process is already running.");
            throw new CustomException(400, "Search Packages update process is already running.");
        }

        DataFix dataFix = dataFixRepository.findByName("Search Packages");
        if (dataFix == null) {
            dataFix = new DataFix(null, "Search Packages", "Update search packages data",
                    "POST", "/update-search-package", LocalDateTime.now(), DataFixStatus.IN_PROGRESS);
        }

        try {
            dataFix.setStatus(DataFixStatus.IN_PROGRESS);
            dataFixRepository.save(dataFix);
            updateAllSearchPackages();
            dataFix.setStatus(DataFixStatus.SUCCEEDED);
            dataFix.setDate(LocalDateTime.now());
            dataFixRepository.save(dataFix);
        } catch (Exception e) {
            dataFix.setStatus(DataFixStatus.FAILED);
            dataFixRepository.save(dataFix);
            logger.error(e.getMessage(), e);
            throw new RuntimeException(e);
        } finally {
            releaseLock("SearchPackages");
        }
    }

    @Async
    @Transactional
    public void RegenerateClips() {
        if (!acquireLock("RegenerateClips")) {
            logger.warn("Regenerate media Clips.");
            throw new CustomException(400, "Regenerate media Clips. process is already running.");
        }

        DataFix dataFix = dataFixRepository.findByName("Regenerate Clips");
        if (dataFix == null) {
            dataFix = new DataFix(null, "Regenerate Clips", "Regenerate media Clips",
                    "POST", "/Regenerate-media-Clips", LocalDateTime.now(), DataFixStatus.IN_PROGRESS);
        }

        try {
            dataFix.setStatus(DataFixStatus.IN_PROGRESS);
            dataFixRepository.save(dataFix);
           List<Media> VideoMedias =  mediaRepository.findAllByMediaType(MediaType.video);
           for (Media mediaItem : VideoMedias) {
               if(mediaItem.getVideoDuration()!=null)
                 mediaConvertService.submitVideoToMp4Job(mediaItem.getVideoUrl(),mediaItem.getVideoDuration().longValue());
           }

           List<Media> ReelMedia=  mediaRepository.findAllByMediaType(MediaType.reel);
           for (Media mediaItem : ReelMedia) {
               if(mediaItem.getVideoDuration()!=null)
                   mediaConvertService.submitVideoToMp4Job(mediaItem.getVideoUrl(),mediaItem.getVideoDuration().longValue());
           }
             dataFix.setStatus(DataFixStatus.SUCCEEDED);
            dataFix.setDate(LocalDateTime.now());
            dataFixRepository.save(dataFix);
        } catch (Exception e) {
            dataFix.setStatus(DataFixStatus.FAILED);
            dataFixRepository.save(dataFix);
            logger.error(e.getMessage(), e);
            throw new RuntimeException(e);
        } finally {
            releaseLock("RegenerateClips");
        }
    }

    @Async
    @Transactional
    public void updatePackagePlacesDataAsyc() {
        if (!acquireLock("updatePackagePlaces")) {
            logger.warn("update Package Places process is already running.");
            throw new CustomException(400, "Search Packages update process is already running.");
        }

        DataFix dataFix = dataFixRepository.findByName("update Package Placess");
        if (dataFix == null) {
            dataFix = new DataFix(null, "update Package Places", "update Package Places data",
                    "POST", "/update-package-places", LocalDateTime.now(), DataFixStatus.IN_PROGRESS);
        }

        try {
            dataFix.setStatus(DataFixStatus.IN_PROGRESS);
            dataFixRepository.save(dataFix);
            updatePackagePlaces();
            dataFix.setStatus(DataFixStatus.SUCCEEDED);
            dataFix.setDate(LocalDateTime.now());
            dataFixRepository.save(dataFix);
        } catch (Exception e) {
            dataFix.setStatus(DataFixStatus.FAILED);
            dataFixRepository.save(dataFix);
            logger.error(e.getMessage(), e);
            throw new RuntimeException(e);
        } finally {
            releaseLock("updatePackagePlaces");
        }
    }

    @Async
    @Transactional
    public void updatePackageSubscribe() {
        if (!acquireLock("updatePackageSubscribe")) {
            logger.warn("update Packages Subscribe count process is already running.");
            throw new CustomException(400, "Search Packages update process is already running.");
        }

        DataFix dataFix = dataFixRepository.findByName("Package Subscribe");
        if (dataFix == null) {
            dataFix = new DataFix(null, "Package Subscribe", "update Packages Subscribe count",
                    "POST", "/update-package-subscribers", LocalDateTime.now(), DataFixStatus.IN_PROGRESS);
        }

        try {
            dataFix.setStatus(DataFixStatus.IN_PROGRESS);
            dataFixRepository.save(dataFix);
            updatePackagesSubscribe();
            dataFix.setStatus(DataFixStatus.SUCCEEDED);
            dataFix.setDate(LocalDateTime.now());
            dataFixRepository.save(dataFix);
        } catch (Exception e) {
            dataFix.setStatus(DataFixStatus.FAILED);
            dataFixRepository.save(dataFix);
            logger.error(e.getMessage(), e);
            throw new RuntimeException(e);
        } finally {
            releaseLock("updatePackageSubscribe");
        }
    }
    @Async
    @Transactional
    public void updateSearchPosts() {
        if (!acquireLock("SearchPosts")) {
            throw new CustomException(400, "Search Posts update process is already running.");
        }
        DataFix dataFix = dataFixRepository.findByName("Search Posts");
        if (dataFix == null) {
            dataFix = new DataFix(null, "Search Posts", "Update search post data",
                    "POST", "/update-search-post", LocalDateTime.now(), DataFixStatus.IN_PROGRESS);
        }
        try {
            dataFix.setStatus(DataFixStatus.IN_PROGRESS);
            dataFixRepository.save(dataFix);
            List<Post> posts = postRepository.findAll();
            List<Post> removingPosts = new ArrayList<>();
            for (Post post : posts) {
                if (post.getUser() != null) {
                    searchData(post);
                } else {
                    removingPosts.add(post);
                }
            }
            postRepository.deleteAll(removingPosts);
            dataFix.setStatus(DataFixStatus.SUCCEEDED);
            dataFix.setDate(LocalDateTime.now());
            dataFixRepository.save(dataFix);
        } catch (Exception e) {
            dataFix.setStatus(DataFixStatus.FAILED);
            dataFixRepository.save(dataFix);
            logger.error(e.getMessage(), e);
            throw new RuntimeException(e);
        } finally {
            releaseLock("SearchPosts");
        }
    }

    @Async
    @Transactional
    public void verifyAndSyncFollowData() {
        if (!acquireLock("FollowData")) {
            throw new CustomException(400, "Follow data sync process is already running.");
        }

        DataFix dataFix = dataFixRepository.findByName("Follow me");
        if (dataFix == null) {
            dataFix = new DataFix(null, "Follow me", "Fix follow me data",
                    "POST", "/update-follow-me", LocalDateTime.now(), DataFixStatus.IN_PROGRESS);
        }

        try {
            dataFix.setStatus(DataFixStatus.IN_PROGRESS);
            dataFixRepository.save(dataFix);

            List<ReactionSearch> reactionSearches = reactionSearchRepository.findByEntityType("Follow");

            for (ReactionSearch reaction : reactionSearches) {
                Optional<User> userOpt = userRepository.findById(reaction.getEntityId());
                Optional<User> followerOpt = userRepository.findById(reaction.getUserId());

                if (userOpt.isPresent() && followerOpt.isPresent()) {
                    User user = userOpt.get();
                    User follower = followerOpt.get();

                    boolean existsInFollows = followsRepository.existsById(new FollowsKey(user, follower));

                    if (!existsInFollows)
                        reactionSearchRepository.delete(reaction);

                } else {
                    reactionSearchRepository.delete(reaction);
                }
            }

            dataFix.setStatus(DataFixStatus.SUCCEEDED);
            dataFix.setDate(LocalDateTime.now());
            dataFixRepository.save(dataFix);

        } catch (Exception e) {
            dataFix.setStatus(DataFixStatus.FAILED);
            dataFixRepository.save(dataFix);

            logger.error(e.getMessage(), e);
            throw new RuntimeException(e);

        } finally {
            releaseLock("FollowData");
        }
    }

    @Async
    @Transactional
    public void deleteReactionsWithNonExistingUserAsync() {
        if (!acquireLock("Reactions")) {
            throw new CustomException(400, "Reactions update process is already running.");
        }

        DataFix dataFix = dataFixRepository.findByName("Reactions");
        if (dataFix == null) {
            dataFix = new DataFix(null, "Reactions", "Delete reactions with non existing user",
                    "POST", "/update-reactions", LocalDateTime.now(), DataFixStatus.IN_PROGRESS);
        }

        try {
            dataFix.setStatus(DataFixStatus.IN_PROGRESS);
            dataFixRepository.save(dataFix);

            List<PostReaction> reactionsToDelete = postReactionRepository.findByUserNotIn(userRepository.findAll());
            postReactionRepository.deleteAll(reactionsToDelete);

            dataFix.setStatus(DataFixStatus.SUCCEEDED);
            dataFix.setDate(LocalDateTime.now());
            dataFixRepository.save(dataFix);

        } catch (Exception e) {
            dataFix.setStatus(DataFixStatus.FAILED);
            dataFixRepository.save(dataFix);

            logger.error(e.getMessage(), e);
            throw new RuntimeException(e);

        } finally {
            releaseLock("Reactions");
        }
    }

    @Async
    @Transactional
    public void migrateSubPackageStatesAsync() {
        if (!acquireLock("SubPackages")) {
            throw new CustomException(400, "Sub Packages update process is already running.");
        }

        DataFix dataFix = dataFixRepository.findByName("Sub Packages");
        if (dataFix == null) {
            dataFix = new DataFix(null, "Sub Packages",
                    "Update Sub packages data (convert state enum from string to integer)",
                    "POST", "/update-subpackage", LocalDateTime.now(), DataFixStatus.IN_PROGRESS);
        }

        try {
            dataFix.setStatus(DataFixStatus.IN_PROGRESS);
            dataFixRepository.save(dataFix);

            Query query = new Query(Criteria.where("state").type(2));
            List<SubPackage> subPackages = mongoTemplate.find(query, SubPackage.class, "subPackage");
            for (SubPackage subPackage : subPackages) {
                Object stateValue = mongoTemplate.getConverter().convertToMongoType(subPackage.getState());

                if (stateValue instanceof String oldStateString) {
                    Integer newState = STATE_MAP.get(oldStateString);

                    if (newState != null) {
                        mongoTemplate.updateFirst(
                                Query.query(Criteria.where("id").is(subPackage.getId())),
                                Update.update("state", newState),
                                "subPackage"
                        );
                    } else {
                        System.out.println("⚠️ Unknown state found: " + oldStateString);
                    }
                }
            }

            dataFix.setStatus(DataFixStatus.SUCCEEDED);
            dataFix.setDate(LocalDateTime.now());
            dataFixRepository.save(dataFix);

        } catch (Exception e) {
            dataFix.setStatus(DataFixStatus.FAILED);
            dataFixRepository.save(dataFix);

            logger.error(e.getMessage(), e);
            throw new RuntimeException(e);

        } finally {
            releaseLock("SubPackages");
        }
    }

    @Async
    @Transactional
    public void updateMedias() {
        if (!acquireLock("Medias")) {
            throw new CustomException(400, "Media update process is already running.");
        }

        DataFix dataFix = dataFixRepository.findByName("Medias");
        if (dataFix == null) {
            dataFix = new DataFix(null, "Medias", "Update media after add user id to it",
                    "POST", "/update-medias", LocalDateTime.now(), DataFixStatus.IN_PROGRESS);
        }

        try {
            dataFix.setStatus(DataFixStatus.IN_PROGRESS);
            dataFixRepository.save(dataFix);

            logger.error("update post media started");

            var posts = postRepository.findAll();
            List<Media> updatedMediaPost = new ArrayList<>();
            List<MediaWrapper> removedPostMediaWrappers = new ArrayList<>();

            for (Post post : posts) {

                var Infulacneropt = userRepository.findById(post.getUser().getId());
                if (Infulacneropt.isEmpty()) {
                    logger.warn("⚠️ Infulacner  with ID {} not found. Skipping...", post.getId());
                    continue;
                }
                User infulancer = Infulacneropt.get();
                for (MediaWrapper mediaWrapper : post.getMedia()) {
                    if (mediaWrapper.getMedia() != null){
                        Media media = mediaWrapper.getMedia();
                        if(media.getTags()!=null && !media.getTags().isEmpty()){
                            List<Tag> tagList = tagRepository.findAllById(media.getTags().stream().map(Tag::getId).collect(Collectors.toList()));
                            media.setTags(tagList);

                        }
                        media.setUser(infulancer);
                        media.setPost(post);
                        updatedMediaPost.add(media);
                        logger.error("Media updated in post.");
                    }else {
                        removedPostMediaWrappers.add(mediaWrapper);
                    }


                }
                post.getMedia().removeAll(removedPostMediaWrappers);
                post.setMedia(solveType(post.getMedia()));
                if(post.getPostType().equals(PostType.Post)){
                    Optional<searchPost> searchPostOptional= searchPostRepository.findById(post.getId());
                    if(searchPostOptional.isPresent()){
                        searchPost searchPost = searchPostOptional.get();
                        searchPost.setMedia(mapToMediaSearch(post.getMedia()));
                        searchPostRepository.save(searchPost);
                    }

                }else {
                    Optional<searchStory> searchStoryOptional= searchStoryRepository.findById(post.getId());
                    if(searchStoryOptional.isPresent()){
                        searchStory searchStory = searchStoryOptional.get();
                        searchStory.setMedia(mapToMediaSearch(post.getMedia()));
                        searchStoryRepository.save(searchStory);
                    }
                }
                postRepository.save(post);
                mediaRepository.saveAll(updatedMediaPost);

            }

            logger.error("update package media started");
            var subPackages = subPackageRepository.findAll();
            logger.error("✅ {} sub-packages fetched", subPackages.size());

            for (SubPackage subPackage : subPackages) {
                if (subPackage.get_package() == null || subPackage.get_package().getId() == null) {
                    logger.warn("⚠️ Sub-package with missing package reference skipped. ID: {}", subPackage.getId());
                    continue;
                }

                var packageOpt = packageRepository.findById(subPackage.get_package().getId());
                if (packageOpt.isEmpty()) {
                    logger.warn("⚠️ Package with ID {} not found. Skipping...", subPackage.get_package().getId());
                    continue;
                }

                var _package = packageOpt.get();
                var updatedPackageOpt = searchPackageRepository.findById(subPackage.getId());

                if (updatedPackageOpt.isEmpty()) {
                    logger.warn("⚠️ Search package with ID {} not found. Skipping...", subPackage.getId());
                    continue;
                }
                var Infulacneropt = userRepository.findById(_package.getInfulancer().getId());
                if (Infulacneropt.isEmpty()) {
                    logger.warn("⚠️ Infulacner  with ID {} not found. Skipping...", subPackage.getId());
                    continue;
                }
                User infulancer = Infulacneropt.get();
                var updatedPackage = updatedPackageOpt.get();

                if (_package.getMedias() == null || _package.getMedias().isEmpty()) {
                    logger.warn("⚠️ Package with ID {} has no media. Skipping...", _package.getId());
                    continue;
                }
                List<Media> updatedMedias = new ArrayList<>();
                List<MediaWrapper> removedMediaWrappers = new ArrayList<>();
                for (MediaWrapper mediaWrapper: _package.getMedias()){
                    if(mediaWrapper.getMedia()!=null){
                        Media media = mediaWrapper.getMedia();
                        if(media.getTags()!=null && !media.getTags().isEmpty()){
                            List<Tag> tagList = tagRepository.findAllById(media.getTags().stream().map(Tag::getId).collect(Collectors.toList()));
                            media.setTags(tagList);

                        }
                        media.set_package(_package);
                        media.setUser(infulancer);
                        updatedMedias.add(media);
                    }else {
                        removedMediaWrappers.add(mediaWrapper);
                    }

                }
                _package.getMedias().removeAll(removedMediaWrappers);
                _package.setMedias(solveType(_package.getMedias()));
                updatedPackage.setMedias(mapToMediaSearch(_package.getMedias()));
                packageRepository.save(_package);
                searchPackageRepository.save(updatedPackage);
                mediaRepository.saveAll(updatedMedias);
                logger.info("✅ Package with ID {} successfully updated", updatedPackage.getId());
            }

            logger.error("update package media ended");


            dataFix.setStatus(DataFixStatus.SUCCEEDED);
            dataFix.setDate(LocalDateTime.now());
            dataFixRepository.save(dataFix);

        } catch (Exception e) {
            dataFix.setStatus(DataFixStatus.FAILED);
            dataFixRepository.save(dataFix);

            logger.error(e.getMessage(), e);
            throw new RuntimeException(e);

        } finally {
            releaseLock("Medias");
        }
    }





    @Async
    @Transactional
    public void fixPackageMedia() {
        if (!acquireLock("FixPackageMedia")) {
            throw new CustomException(400, "Package media fixing process is already running.");
        }

        DataFix dataFix = dataFixRepository.findByName("Package Medias");
        if (dataFix == null) {
            dataFix = new DataFix(null, "Package Medias", "Fixing media of the search entity from the package entity",
                    "POST", "/update-search-package-media", LocalDateTime.now(), DataFixStatus.IN_PROGRESS);
        }

        try {
            dataFix.setStatus(DataFixStatus.IN_PROGRESS);
            dataFixRepository.save(dataFix);

            logger.error("🔄 Fixing search package media data started at {}", LocalDateTime.now());
            logger.error("🔍 Fetching sub-packages...");

            var subPackages = subPackageRepository.findAll();
            logger.error("✅ {} sub-packages fetched", subPackages.size());

            for (SubPackage subPackage : subPackages) {
                if (subPackage.get_package() == null || subPackage.get_package().getId() == null) {
                    logger.warn("⚠️ Sub-package with missing package reference skipped. ID: {}", subPackage.getId());
                    continue;
                }

                var packageOpt = packageRepository.findById(subPackage.get_package().getId());
                if (packageOpt.isEmpty()) {
                    logger.warn("⚠️ Package with ID {} not found. Skipping...", subPackage.get_package().getId());
                    continue;
                }

                var _package = packageOpt.get();
                var updatedPackageOpt = searchPackageRepository.findById(subPackage.getId());

                if (updatedPackageOpt.isEmpty()) {
                    logger.warn("⚠️ Search package with ID {} not found. Skipping...", subPackage.getId());
                    continue;
                }

                var updatedPackage = updatedPackageOpt.get();

                if (_package.getMedias() == null || _package.getMedias().isEmpty()) {
                    logger.warn("⚠️ Package with ID {} has no media. Skipping...", _package.getId());
                    continue;
                }
                List<Media> updatedMedias = new ArrayList<>();
                List<MediaWrapper> removedMediaWrappers = new ArrayList<>();
                for (MediaWrapper mediaWrapper: _package.getMedias()){
                    if(mediaWrapper.getMedia()!=null){
                        Media media = mediaWrapper.getMedia();
                        media.set_package(_package);
                        updatedMedias.add(media);
                    }else {
                        removedMediaWrappers.add(mediaWrapper);
                    }

                 }
                _package.getMedias().removeAll(removedMediaWrappers);
                _package.setMedias(solveType(_package.getMedias()));
                updatedPackage.setMedias(mapToMediaSearch(_package.getMedias()));
                packageRepository.save(_package);
                searchPackageRepository.save(updatedPackage);
                mediaRepository.saveAll(updatedMedias);
                logger.info("✅ Package with ID {} successfully updated", updatedPackage.getId());
            }

            logger.error("✅ Fixing search package media data finished at {}", LocalDateTime.now());

            dataFix.setStatus(DataFixStatus.SUCCEEDED);
            dataFix.setDate(LocalDateTime.now());
            dataFixRepository.save(dataFix);

        } catch (Exception e) {
            dataFix.setStatus(DataFixStatus.FAILED);
            dataFixRepository.save(dataFix);

            logger.error(e.getMessage(), e);
            throw new RuntimeException(e);

        } finally {
            releaseLock("FixPackageMedia");
        }
    }

    @Async
    @Transactional
    public void deleteSubscribes() {
        subscribeRepository.deleteAll();

    }

    /// /////////////////////////////////////////// Helpers //////////////////////////////////////////////

    @SuppressWarnings("BooleanMethodIsAlwaysInverted")
    private boolean acquireLock(String key) {
        return locks.computeIfAbsent(key, k -> new AtomicBoolean(false)).compareAndSet(false, true);
    }

    private void releaseLock(String key) {
        AtomicBoolean lock = locks.get(key);
        if (lock != null) {
            lock.set(false);
        }
    }

    @Transactional
    public searchPackage convertPackageToSearchPackage(SubPackage _package) {
        searchPackage packageEntity = modelMapper.map(_package, searchPackage.class);
        packageEntity.setMoods(_package.get_package().getMoods().stream().filter(Objects::nonNull).map(z-> modelMapper.map(z,SearchMood.class)).collect(Collectors.toList()));
        packageEntity.setDescription(_package.get_package().getDescription());
        packageEntity.setInfulancer(modelMapper.map(_package.get_package().getInfulancer(), simpleUserInfo.class));
        return packageEntity;
    }

    @Transactional
    public void searchData(Post post) {
        User user = post.getUser();
        post.setUpdate(DateFormater.formatLocalDateTime(LocalDateTime.now()));

        Package sub = post.getPackage();
        simpleUserInfo simpleUserInfo = new simpleUserInfo();
        simpleUserInfo.setId(user.getId());
        simpleUserInfo.setFirstName(user.getFirstName());
        simpleUserInfo.setLastName(user.getLastName());
        simpleUserInfo.setUsername(user.getUsername());
        simpleUserInfo.setUsertype(user.getUsertype());
        simpleUserInfo.setProfileImage(user.getProfileImage());
        simpleUserInfo.setCoverImage(user.getCoverImage());
        logger.error("simpleUserInfo created");

        simplePackageInfo simplePackageInfo = new simplePackageInfo();
        if (sub != null) {
            logger.info("Package present");
            simplePackageInfo.setName(sub.getName());
            simplePackageInfo.setId(sub.getId());
            simplePackageInfo.setSlug(sub.getSlug());
            logger.error("simpleUserInfo created");
        }

        if (post.getPostType() != null) {
            if (post.getPostType().equals(PostType.Story)) {
                logger.error("creating story index");
                searchStory searchStorey = convertPostToSearchStory(post);
                if (sub != null)
                    searchStorey.setPackage(simplePackageInfo);

                searchStorey.setUser(simpleUserInfo);
                searchStoryRepository.save(searchStorey);
            } else {
                searchPost searchPost = convertPostToSearchPost(post);
                if (sub != null)
                    searchPost.setPackage(simplePackageInfo);

                searchPost.setUser(simpleUserInfo);
                searchPostRepository.save(searchPost);
            }
            postRepository.save(post);
            logger.error("post {} done", post.getText());
        }

    }

    private searchStory convertPostToSearchStory(Post post) {
        return modelMapper.map(post, searchStory.class);
    }

    private searchPost convertPostToSearchPost(Post post) {
        return modelMapper.map(post, searchPost.class);
    }

    @Transactional
    @Async
    public void removeDuplicates() {
        List<Airport> allAirports = airportRepository.findAll();

        // HighLight airports by code
        Map<String, List<Airport>> groupedByCode = allAirports.stream()
                .filter(a -> a.getCode() != null)
                .collect(Collectors.groupingBy(Airport::getCode));

        List<Airport> toDelete = new ArrayList<>();

        for (Map.Entry<String, List<Airport>> entry : groupedByCode.entrySet()) {
            List<Airport> airportsWithSameCode = entry.getValue();

            // If there's more than one airport with the same code, pick the best one to keep
            if (airportsWithSameCode.size() > 1) {
                // Prefer airports with valid coordinates
                Optional<Airport> preferred = airportsWithSameCode.stream()
                        .filter(a -> a.getLatitude() != 0.0 && a.getLongitude() != 0.0)
                        .findFirst();

                Airport toKeep = preferred.orElse(airportsWithSameCode.get(0)); // fallback if none have coordinates

                // Add all others to delete list
                for (Airport airport : airportsWithSameCode) {
                    if (!airport.getId().equals(toKeep.getId())) {
                        toDelete.add(airport);
                    }
                }
            }
        }

        // Perform deletion
        if (!toDelete.isEmpty()) {
            airportRepository.deleteAll(toDelete);
        }
    }





    @Transactional
    public void updateAllSearchPackages() {
        List<searchPackage> searchPackages = searchPackageRepository.findAll();
        List<searchPackage> removingPackages = new ArrayList<>();
        List<SubPackage> removingsubPackages = new ArrayList<>();
        List<Package> removingPackage = new ArrayList<>();
        for (searchPackage packageEntity : searchPackages) {
            SubPackage subPackage = subPackageRepository.findById(packageEntity.getId()).orElse(null);

            if (subPackage == null) {
                removingPackages.add(packageEntity);
                continue;
            }
             subPackage.setUpdateDate(DateFormater.formatLocalDateTime(LocalDateTime.now()));

            if (subPackage.get_package() != null && subPackage.get_package().getId() == null) {
                removingPackages.add(packageEntity);
                removingPackage.add(subPackage.get_package());
                removingsubPackages.add(subPackage);
                continue;
            }

            var packageOpt = packageRepository.findById(subPackage.get_package().getId());

            if (subPackage.get_package() != null && subPackage.get_package().getInfulancer() != null && packageOpt.isPresent()) {
                var _package = packageOpt.get();
                if(subPackage.getSlug()==null || subPackage.getSlug().isEmpty())
                {
                    if(subPackage.getPackageType().equals(PackageType.FollowMe)){
                        String slug=subPackage.getName()+"-follow-me";
                        subPackage.setSlug(generateSlug(slug));
                    }else {
                        _package.setSlug(generateSlug(subPackage.getName()));
                        subPackage.setSlug(generateSlug(subPackage.getName()));
                    }
                }else
                {
                     _package.setSlug(slugify(subPackage.getSlug()));
                    subPackage.setSlug(slugify(subPackage.getSlug()));
                }
                searchPackage updatedPackage = convertPackageToSearchPackage(subPackage);
                updatedPackage.setId(packageEntity.getId());
                List<String> moodIds = _package.getMoods().stream().filter(Objects::nonNull)
                        .map(Mood::getId)
                        .collect(Collectors.toList());

                if( _package.getTags()==null)
                    _package.setTags(new ArrayList<>());

                List<String> tagsIds = _package.getTags().stream().filter(Objects::nonNull)
                        .map(Tag::getId)
                        .toList();
                // Fetch all moods at once
                List<Mood> fullMoods = moodIds.isEmpty() ? Collections.emptyList() :
                        mongoTemplate.find(
                                Query.query(Criteria.where("_id").in(moodIds)),
                                Mood.class
                        );
                List<Tag> fullTags = tagsIds.isEmpty() ? Collections.emptyList() :
                        mongoTemplate.find(Query.query(Criteria.where("_id").in(tagsIds)), Tag.class);
                updatedPackage.setTags(fullTags);
                List<SearchMood> list = new ArrayList<>();
                for (Mood z : fullMoods) {
                    SearchMood map = modelMapper.map(z, SearchMood.class);
                    list.add(map);
                }
                updatedPackage.setMoods(list);
                updatedPackage.setPackagePlaces(subPackage.getPackagePlaces());
                updatedPackage.setMedias(mapToMediaSearch(_package.getMedias()));
                updatedPackage.setState(subPackage.getState());
                updatedPackage.setPackageId(subPackage.get_package().getId());


                searchPackageRepository.save(updatedPackage);
                subPackageRepository.save(subPackage);
            } else {
                removingsubPackages.add(subPackage);
                removingPackage.add(subPackage.get_package());
                removingPackages.add(packageEntity);
            }
        }
        subPackageRepository.deleteAll(removingsubPackages);
        packageRepository.deleteAll(removingPackage);
        searchPackageRepository.deleteAll(removingPackages);
    }
     public boolean isSlugUnique(String slug) {
        return !subPackageRepository.existsBySlugIgnoreCase(slug);
    }

    public String generateSlug(String name) {
        String baseSlug = slugify(name); // Convert name to URL-friendly slug
        String slug = baseSlug;
        int counter = 1;

        while (!isSlugUnique(slug)) {
            slug = baseSlug + "-" + counter;
            counter++;
        }

        return slug;
    }

    public String slugify(String input) {
        return input.toLowerCase()
                .replaceAll("[^a-z0-9\\s-]", "")   // remove special characters
                .replaceAll("\\s+", "-")           // replace spaces with hyphens
                .replaceAll("-+", "-")             // replace multiple hyphens
                .replaceAll("^-|-$", "");           // trim hyphens from start/end
    }
    @Transactional

    public  void  updatePackagePlaces(){
        List<searchPackage> searchPackages = searchPackageRepository.findAll();
        List<searchPackage> removingPackages = new ArrayList<>();
        List<SubPackage> removingsubPackages = new ArrayList<>();
        List<Package> removingPackage = new ArrayList<>();
        for (searchPackage packageEntity : searchPackages) {
            SubPackage subPackage = subPackageRepository.findById(packageEntity.getId()).orElse(null);

            if (subPackage == null) {
                removingPackages.add(packageEntity);
                continue;
            }
            subPackage.setUpdateDate(DateFormater.formatLocalDateTime(LocalDateTime.now()));

            if (subPackage.get_package() != null && subPackage.get_package().getId() == null) {
                removingPackages.add(packageEntity);
                removingPackage.add(subPackage.get_package());
                removingsubPackages.add(subPackage);
                continue;
            }

            var packageOpt = packageRepository.findById(subPackage.get_package().getId());

            if (subPackage.get_package() != null && subPackage.get_package().getInfulancer() != null && packageOpt.isPresent()) {
                var _package = packageOpt.get();
                searchPackage updatedPackage = convertPackageToSearchPackage(subPackage);
                updatedPackage.setId(packageEntity.getId());
                List<PackageCountry> packageCountries = new ArrayList<>();
                packageCountries=  patchPackagePlaces(subPackage);
                _package.setPackagePlaces(packageCountries);
                subPackage.setPackagePlaces(packageCountries);
                updatedPackage.setPackagePlaces(packageCountries);
                searchPackageRepository.save(updatedPackage);
                subPackageRepository.save(subPackage);
                packageRepository.save(_package);
            } else {
                removingsubPackages.add(subPackage);
                removingPackage.add(subPackage.get_package());
                removingPackages.add(packageEntity);
            }
        }
        subPackageRepository.deleteAll(removingsubPackages);
        packageRepository.deleteAll(removingPackage);
        searchPackageRepository.deleteAll(removingPackages);

    }

    @Transactional
    public void updatePackagesSubscribe() {
        List<searchPackage> searchPackages = searchPackageRepository.findAll();
        for (searchPackage packageEntity : searchPackages) {
            SubPackage subPackage = subPackageRepository.findById(packageEntity.getId()).orElse(null);
           if(subPackage == null) {
               continue;
           }
          var subscribes=  subscribeRepository.findSubscribedUserByPackageId(new ObjectId(subPackage.getId()),null);
            int totalSubscribers = subscribes.stream()
                    .mapToInt(s -> s.getAdultCount() + s.getChildCount())
                    .sum();

            subPackage.setSubscribeCount(totalSubscribers);
            searchPackage searchPackage=  searchPackageRepository.findById(packageEntity.getId()).orElse(null);
            if(searchPackage != null) {
                 searchPackage.setSubscribeCount(subscribes.size());
                searchPackageRepository.save(searchPackage);
                subPackageRepository.save(subPackage);
            }
        }

    }
    private List<MediaWrapperSearch> mapToMediaSearch(List<MediaWrapper> medias) {
        if(medias==null)
            return new ArrayList<>();
        return medias
                .stream()
                .map(item -> modelMapper.map(item, MediaWrapperSearch.class))
                .toList();
    }

    private List<MediaWrapper> solveType(List<MediaWrapper> medias) {
          for(var item:medias){
           var mediatype=  item.getMedia().getMediaType();
                      item.setType(mediatype);
          }
          return  medias;
    }

    public List<User> getUserFields() {
        List<AggregationOperation> operations = new ArrayList<>();

        operations.add(LookupOperation.newLookup()
                .from("mood")
                .localField("moods.$id")
                .foreignField("_id")
                .as("moods"));

        operations.add(project()
                .andInclude("id", "about", "firstName", "lastName", "username", "guestEmail", "city", "country",
                        "medias", "follwerscount", "followingcount", "profileImage", "coverImage",
                        "gender", "usertype", "moods", "userInfo.email", "userInfo.mobile"));

        return mongoTemplate.aggregate(Aggregation.newAggregation(operations),
                "user", User.class).getMappedResults();
    }


    /**
     * Generates username for User entity where types in Traveler & Influencer
     *
     * @see User
     * @see UsernameUtil
     * @see UserType
     */
    @Async
    @Transactional
    public void generateUsernames() {
        try {
            int pageSize = 100;
            PageRequest pageRequest = PageRequest.of(0, pageSize);
            Page<User> page;

            final List<UserType> userTypes = new ArrayList<>();
            userTypes.add(UserType.Influencer);
            userTypes.add(UserType.Traveler);

            do {
                page = userRepository.findByUsertypeIn(userTypes, pageRequest);
                logger.info(page.getContent().getFirst().getId());
                List<User> updatedUsers = page.getContent().stream()
                        .filter(user -> (user.getFirstName() != null && user.getLastName() != null))
                        .peek(user -> {
                            logger.info(user.getFirstName() + "  " + user.getLastName());
                            String username = UsernameUtil.generateUniqueUsername(user.getFirstName(), user.getLastName());
                            user.setUsername(username);
                            var searchUser = searchUserRepository.findById(user.getId());
                            if (searchUser.isPresent()) {
                                searchUser.get().setUsername(username);
                                searchUserRepository.save(searchUser.get());
                            }
                        })
                        .toList();

                userRepository.saveAll(updatedUsers);
                pageRequest = pageRequest.next();
            } while (page.hasNext());
        } catch (Exception e) {
            logger.error("Failed to generate usernames ", e);
        }
    }
    /**
     * Sets gender to null for users without gender field of type Influencer and Traveler
     *
     * @see User
     * @see UserType
     */
    @Async
    @Transactional
    public void matchAndUpdateGender() {
        if (!acquireLock("Gender")) {
            throw new CustomException(400, "Gender update process is already running.");
        }

        DataFix dataFix = dataFixRepository.findByName("Gender");
        if (dataFix == null) {
            // Create the DataFix record if it doesn't exist
            dataFix = new DataFix(
                    "Set gender to null for users",
                    "Gender",
                    "Set gender to null for users without gender field of type Influencer and Traveler",
                    "POST",
                    "/update-gender-patch",
                    LocalDateTime.now(),
                    DataFixStatus.IN_PROGRESS
            );
            dataFix = dataFixRepository.save(dataFix); // Save the new record
        }

        try {
            dataFix.setStatus(DataFixStatus.IN_PROGRESS);


            Query query = new Query(Criteria
                    .where("gender").exists(false)
                    .and("usertype").in(UserType.Traveler, UserType.Influencer)
            );

            Update update = new Update().set("gender", null);
            UpdateResult result = mongoTemplate.updateMulti(query, update, User.class);

            dataFix.setMatchedCount(result.getMatchedCount());
            dataFix.setModifiedCount(result.getModifiedCount());
            dataFix.setStatus(DataFixStatus.SUCCEEDED);
            dataFix.setDate(LocalDateTime.now());
            dataFixRepository.save(dataFix);

        } catch (Exception e) {
            if (dataFix != null) {
                dataFix.setStatus(DataFixStatus.FAILED);
                dataFixRepository.save(dataFix);
            }
            logger.error("Failed to update gender: ", e);
            throw new RuntimeException(e);
        } finally {
            releaseLock("Gender");
        }
    }
    
    @Async
    @Transactional
    public void createNotificationSettingsForAllUsers() {
        if (!acquireLock("NotificationSettings")) {
            throw new CustomException(400, "Notification settings creation process is already running.");
        }

        DataFix dataFix = dataFixRepository.findByName("NotificationSettings");
        if (dataFix == null) {
            dataFix = new DataFix(null, "NotificationSettings", "Create notification settings for all users",
                    "POST", "/create-notification-settings", LocalDateTime.now(), DataFixStatus.IN_PROGRESS);
        }

        try {
            dataFix.setStatus(DataFixStatus.IN_PROGRESS);
            dataFixRepository.save(dataFix);

            List<User> users = userRepository.findAll();
            int processedCount = 0;
            int createdCount = 0;

            for (User user : users) {
                List<NotificationSetting> existSetting = notificationSettingRepository.findByUser(user);
                if(existSetting != null && !existSetting.isEmpty())
                    continue;

                // Create default notification setting
                NotificationSetting setting = new NotificationSetting();
                setting.setUser(user);


                mongoTemplate.save(setting);
                createdCount++;

                
                processedCount++;
                if (processedCount % 100 == 0) {
                    logger.info("Processed {} users, created {} notification settings", processedCount, createdCount);
                }
            }

            dataFix.setMatchedCount(processedCount);
            dataFix.setModifiedCount(createdCount);
            dataFix.setStatus(DataFixStatus.SUCCEEDED);
            dataFix.setDate(LocalDateTime.now());
            dataFixRepository.save(dataFix);
            
            logger.info("Notification settings creation completed. Processed: {}, Created: {}", processedCount, createdCount);

        } catch (Exception e) {
            dataFix.setStatus(DataFixStatus.FAILED);
            dataFixRepository.save(dataFix);

            logger.error("Failed to create notification settings: ", e);
            throw new RuntimeException(e);
        } finally {
            releaseLock("NotificationSettings");
        }
    }

    @Async
    @Transactional
    public void createPackageNotificationSettingsForAllSubPackages() {
        if (!acquireLock("PackageNotificationSettings")) {
            throw new CustomException(400, "Package notification settings creation process is already running.");
        }

        DataFix dataFix = dataFixRepository.findByName("PackageNotificationSettings");
        if (dataFix == null) {
            dataFix = new DataFix(null, "PackageNotificationSettings", "Create package notification settings for all sub packages",
                    "POST", "/create-package-notification-settings", LocalDateTime.now(), DataFixStatus.IN_PROGRESS);
        }

        try {
            dataFix.setStatus(DataFixStatus.IN_PROGRESS);
            dataFixRepository.save(dataFix);

            // Get all SubPackages
            List<SubPackage> subPackages = subPackageRepository.findAll();
            int processedCount = 0;
            int createdCount = 0;

            // Query to find existing PackageNotificationSettings
            Query query = new Query();
            query.fields().include("_package.$id");
            List<PackageNotificationSetting> existingSettings = mongoTemplate.find(query, PackageNotificationSetting.class);

            // Create a set of SubPackage IDs that already have notification settings
            Set<String> subPackageIdsWithSettings = existingSettings.stream()
                    .filter(setting -> setting.get_package() != null)
                    .map(setting -> setting.get_package().getId())
                    .collect(Collectors.toSet());

            logger.info("Found {} existing package notification settings", subPackageIdsWithSettings.size());

            for (SubPackage subPackage : subPackages) {
                processedCount++;

                // Skip if this SubPackage already has a notification setting
                if (subPackageIdsWithSettings.contains(subPackage.getId())) {
                    continue;
                }

                // Create new PackageNotificationSetting
                PackageNotificationSetting setting = new PackageNotificationSetting();
                setting.set_package(new SubPackage(subPackage.getId()));

                mongoTemplate.save(setting);
                createdCount++;

                if (processedCount % 100 == 0) {
                    logger.info("Processed {} sub packages, created {} package notification settings",
                            processedCount, createdCount);
                }
            }

            dataFix.setMatchedCount(processedCount);
            dataFix.setModifiedCount(createdCount);
            dataFix.setStatus(DataFixStatus.SUCCEEDED);
            dataFix.setDate(LocalDateTime.now());
            dataFixRepository.save(dataFix);

            logger.info("Package notification settings creation completed. Processed: {}, Created: {}",
                    processedCount, createdCount);

        } catch (Exception e) {
            dataFix.setStatus(DataFixStatus.FAILED);
            dataFixRepository.save(dataFix);

            logger.error("Failed to create package notification settings: ", e);
            throw new RuntimeException(e);
        } finally {
            releaseLock("PackageNotificationSettings");
        }
    }

    private Country getCountryById(String countryId) {
        List<Country> countries = countryRepository.findAll();
        return countries.stream().filter(z -> z.getId().equals(countryId)).findFirst().orElse(null);
    }

    private City getCityById(String cityId) {
        return cityRepository.findById(cityId).orElse(null);
    }

    private Area getAreaById(String areaId) {
        return areaRepository.findById(areaId).orElse(null);
    }
    public List<PackageCountry> patchPackagePlaces(SubPackage subPackage) {
        List<PackageCountry> patchedCountries = new ArrayList<>();

        for (PackageCountry pkgCountry : subPackage.getPackagePlaces()) {
            Country country = getCountryById(pkgCountry.getPropertyId());
            if (country == null) continue;

            pkgCountry.setCountryName(country.getName());

            List<PackageCity> patchedCities = patchCities(pkgCountry.getCities());
            pkgCountry.setCities(patchedCities);

            patchedCountries.add(pkgCountry);
        }
        return patchedCountries;
     }

    private List<PackageCity> patchCities(List<PackageCity> cities) {
        List<PackageCity> patchedCities = new ArrayList<>();

        for (PackageCity city : cities) {
            City dbCity = getCityById(city.getPropertyId());
            if (dbCity == null) continue;

            city.setCityName(dbCity.getName());

            List<PackageArea> patchedAreas = patchAreas(city.getAreas());
            city.setAreas(patchedAreas);

            patchedCities.add(city);
        }

        return patchedCities;
    }

    private List<PackageArea> patchAreas(List<PackageArea> areas) {
        List<PackageArea> patchedAreas = new ArrayList<>();

        for (PackageArea area : areas) {
            Area dbArea = getAreaById(area.getPropertyId());
            if (dbArea == null) continue;

            area.setAreaName(dbArea.getName());

            patchedAreas.add(area);
        }

        return patchedAreas;
    }

}


