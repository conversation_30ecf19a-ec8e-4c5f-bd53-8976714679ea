package com.hb.crm.admin.services;

import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.UserRecord;
import com.hb.crm.admin.dto.CountDto;
import com.hb.crm.admin.dto.MoodDto.MoodDto;
import com.hb.crm.admin.dto.PageDto;
import com.hb.crm.admin.dto.users.UserDto;
import com.hb.crm.admin.dto.users.UserStatsDto;
import com.hb.crm.admin.dto.users.fcmToken;
import com.hb.crm.admin.services.interfaces.QueryNormalizeService;
import com.hb.crm.admin.services.interfaces.UserService;
import com.hb.crm.core.Enums.MediaType;
import com.hb.crm.core.Enums.PostType;
import com.hb.crm.core.Enums.UserType;
import com.hb.crm.core.beans.*;
import com.hb.crm.core.beans.LiveStream.LiveStream;
import com.hb.crm.core.beans.Notification.NotificationSetting;
import com.hb.crm.core.beans.Package;
import com.hb.crm.core.exceptions.CustomException;
import com.hb.crm.core.repositories.*;
import com.hb.crm.core.searchBeans.SearchUser;
import com.hb.crm.core.searchBeans.simpleUserInfo;
import com.hb.crm.core.searchRepositories.SearchUserRepository;
import com.hb.crm.core.util.UpdateUserUtil;
import com.hb.crm.core.util.UsernameUtil;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class UserServiceImpl implements UserService {

    private static final int ALL_LIMIT = 99999;
    private final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);
    private final UserRepository userRepository;
    private final PasswordEncoder bcryptEncoder;
    private final ModelMapper modelMapper;
    private final QueryNormalizeService queryNormalizeService;
    private final ReplyReactionRepository replyReactionRepository;
    private final CommentReactionRepository commentReactionRepository;
    private final PackageReactionRepository packageReactionRepository;
    private final PostReactionRepository postReactionRepository;
    private final SearchUserRepository searchUserRepository;
    private final CommentRepository commentsRepository;
    private final ReplyRepository replyRepository;
    private final MongoTemplate mongoTemplate;
    private final MqttPublisherService mqttPublisherService;
    private final UpdateUserUtil updateUserUtil;
    private final NotificationSettingRepository notificationSettingRepository;
    private final MongoTemplate mongoTemplate2;
    @Autowired
    public UserServiceImpl(UserRepository userRepository, PasswordEncoder bcryptEncoder, ModelMapper modelMapper, QueryNormalizeService queryNormalizeService, ReplyReactionRepository replyReactionRepository, CommentReactionRepository commentReactionRepository, PackageReactionRepository packageReactionRepository, PostReactionRepository postReactionRepository, SearchUserRepository searchUserRepository, CommentRepository commentsRepository, ReplyRepository replyRepository,
                           @Qualifier("mongoTemplate1") MongoTemplate mongoTemplate, MqttPublisherService mqttPublisherService, UpdateUserUtil updateUserUtil, NotificationSettingRepository notificationSettingRepository,        @Qualifier("mongoTemplate2")MongoTemplate mongoTemplate2) {
        this.userRepository = userRepository;
        this.bcryptEncoder = bcryptEncoder;
        this.modelMapper = modelMapper;
        this.queryNormalizeService = queryNormalizeService;
        this.replyReactionRepository = replyReactionRepository;
        this.commentReactionRepository = commentReactionRepository;
        this.packageReactionRepository = packageReactionRepository;
        this.postReactionRepository = postReactionRepository;
        this.searchUserRepository = searchUserRepository;
        this.commentsRepository = commentsRepository;
        this.replyRepository = replyRepository;
        this.mongoTemplate = mongoTemplate;
        this.mqttPublisherService = mqttPublisherService;
        this.updateUserUtil = updateUserUtil;
        this.notificationSettingRepository = notificationSettingRepository;
        this.mongoTemplate2 = mongoTemplate2;
    }

    @Override
    public User save(User user) throws Exception {

        if (user == null)
            throw new Exception("Error in update admin User Cannot be null");

        //validateUserInput(user);
        if (user.getId() == null) {
            UserRecord.CreateRequest request = new UserRecord.CreateRequest()
                    .setEmail(user.getUserInfo().getEmail())
                    .setEmailVerified(true) // <--- Mark email as verified
                    .setPassword(user.getPassword());
            UserRecord userRecord = FirebaseAuth.getInstance().createUser(request);
            System.out.println("Successfully created user: " + userRecord.getUid());
            user.setPassword(bcryptEncoder.encode(user.getPassword()));

            user.setFirebaseId(userRecord.getUid());


            var saveduser = userRepository.save(user);

            // create search user for search
            searchUserRepository.save(new SearchUser(saveduser));

            // create notification setting for user
            notificationSettingRepository.save(new NotificationSetting(saveduser));

            return saveduser;
        }

        final Optional<User> byId = userRepository.findById(user.getId());

        if (byId.isEmpty())
            throw new Exception("Error in update admin User Cannot update");

        final User userBean = byId.get();
        userBean.setUserInfo(user.getUserInfo());
        userBean.setUsername(user.getUsername());
        userBean.setCity(user.getCity());
        userBean.setCountry(user.getCountry());
        userBean.setGender(user.getGender());
        userBean.setAccountLocked(user.isAccountLocked());
        userBean.setUsertype(user.getUsertype());
        userBean.setFailedLoginAttempts(0);
        userBean.setFirstName(user.getFirstName());
        userBean.setLastName(user.getLastName());
        userBean.setAbout(user.getAbout());
        userBean.setProfileImage(user.getProfileImage());

        if ((userBean.getUsername() == null || userBean.getUsername().isEmpty())
                && user.getFirstName() != null
                && user.getLastName() != null) {
            userBean.setUsername(UsernameUtil.generateUniqueUsername(user.getFirstName(), user.getLastName()));
        }


        UserInfo info;
        if (user.getUserInfo() != null && user.getUserInfo().getMobile() != null && !user.getUserInfo().getMobile().isEmpty()) {
            info = userBean.getUserInfo();
            info.setMobile(user.getUserInfo().getMobile());
        } else {
            info = user.getUserInfo();
        }
        userBean.setUserInfo(info);

        userBean.setCollectionDeviceId(user.getCollectionDeviceId());

        if (user.getPassword() != null && !user.getPassword().isEmpty()) {
            // Update the database password (hashed)
            userBean.setPassword(bcryptEncoder.encode(user.getPassword()));

            // Update the Firebase account password using the plain text password
            UserRecord.UpdateRequest updateRequest = new UserRecord.UpdateRequest(userBean.getFirebaseId())
                    .setPassword(user.getPassword());
            FirebaseAuth.getInstance().updateUser(updateRequest);
        }
        userBean.setSocialMediaLinks(user.getSocialMediaLinks());
        userBean.setActive(user.isActive());
        userBean.setLimited(user.isLimited());
        // Handle moods as DBRef (create id-only Mood objects if incoming has partial data)
        if (user.getMoods() != null) {
            userBean.setMoods(user.getMoods().stream()
                    .map(m -> new Mood(m.getId())) // Assume Mood has id constructor
                    .collect(Collectors.toList()));
        }

        Map<String, Object> claims = new HashMap<>();
        claims.put("appId", userBean.getId());
        claims.put("role", userBean.getUsertype().toString());
        claims.put("moodFlag", userBean.getMoods() != null && !userBean.getMoods().isEmpty());
        claims.put("LastTimeUserChecked", LocalDateTime.now().toString());
        FirebaseAuth.getInstance().setCustomUserClaims(userBean.getFirebaseId(), claims);

        var saveduser =  userRepository.save(userBean);
        simpleUserInfo simpleUserInfo = new simpleUserInfo();
        simpleUserInfo.setFirstName(saveduser.getFirstName());
        simpleUserInfo.setUsername(saveduser.getUsername());
        simpleUserInfo.setUsertype(saveduser.getUsertype());
        simpleUserInfo.setLastName(saveduser.getLastName());
        simpleUserInfo.setCoverImage(saveduser.getCoverImage());
        simpleUserInfo.setId(saveduser.getCoverImage());
        simpleUserInfo.setProfileImage(saveduser.getProfileImage());
        updateUserUtil.updateSimpleUserInfoInSearchPosts(userBean.getId(),simpleUserInfo);
        // create search user for search
        searchUserRepository.save(new SearchUser(saveduser));

        // create notification setting for user if it does not exist
        var userSetting = notificationSettingRepository.findByUser(saveduser);
        if(userSetting.isEmpty())
            notificationSettingRepository.save(new NotificationSetting(saveduser));

        return saveduser;
    }

    @Override
    public PageDto<UserDto> getUsers() {
        return search(new HashMap<>(), -1, 999);
    }

    @Override
    public PageDto<UserDto> getInfluence() {
        PageDto<UserDto> pageDto = new PageDto<>();
        Criteria FollowCriteria = new Criteria("usertype").is(UserType.Influencer);
        Aggregation aggregation = queryNormalizeService.getInfluence(FollowCriteria);

        List<UserDto> users = mongoTemplate.aggregate(aggregation, "user", User.class).getMappedResults().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
        long count = users.size();
        pageDto.setTotalNoOfItems(count);

        pageDto.setItems(users);
        return pageDto;
    }
    @Override
    public PageDto<UserDto> getsysUsers() {
        PageDto<UserDto> pageDto = new PageDto<>();
        Criteria Criteria = new Criteria()
                .orOperator
                    (
                        new Criteria("usertype").is(UserType.Influencer),
                        new Criteria("usertype").is(UserType.Traveler)
                    );
        
        Aggregation aggregation = queryNormalizeService.getInfluence(Criteria);
        List<UserDto> users = mongoTemplate.aggregate(aggregation, "user", User.class).getMappedResults().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
        long count = users.size();
        pageDto.setTotalNoOfItems(count);

        pageDto.setItems(users);
        return pageDto;
    }


    @Override
    public UserDto findById(String id) {
        return userRepository.findById(id).isPresent() ? convertToDto(userRepository.findById(id).get()) : null;
    }

    @Override
    public UserDto findByUsername(String username) {
        return userRepository.findByUsername(username).isPresent() ? convertToDto(userRepository.findByUsername(username).get()) : null;
    }


    @Override
    public PageDto<UserDto> search(Map<String, Object> obj, int page, int limit) {

        if (page < 0) {
            limit = ALL_LIMIT;
            page = 0;
        }

        PageDto<UserDto> pageDto = new PageDto<>();
        final Pageable pageable = PageRequest.of(page, limit);
        final  Criteria searchQuery = createSearchSpecification(obj);
        Aggregation aggregation = queryNormalizeService.getUserFileds(searchQuery, pageable, false);
        Aggregation countAggregation = queryNormalizeService.getUserFileds(searchQuery, pageable, true);

        List<UserDto> aggregationResults = mongoTemplate.aggregate(aggregation, "user", UserDto.class).getMappedResults();
        AggregationResults<CountDto> countResults = mongoTemplate.aggregate(countAggregation, "user", CountDto.class);

        long totalCount = countResults.getUniqueMappedResult() != null ? countResults.getUniqueMappedResult().getCount() : 0;
        pageDto.setTotalNoOfItems(totalCount);
        pageDto.setItems(aggregationResults);
        return pageDto;
    }

    @Override
    public boolean checkEmailExists(String email) {
            // Query MongoDB to check if the email exists
            return userRepository.existsByUserinfoEmail(email);
    }

    @Override
    public void delete(String id) throws Exception {
        User user = userRepository.findById(id).orElse(null);

        final Aggregation ReplyAggregation = queryNormalizeService.getReplayReacts(Criteria.where("user._id").is(new ObjectId(id)));
        List<ReplyReaction> Reactions = mongoTemplate.aggregate(ReplyAggregation, "replyReaction", ReplyReaction.class).getMappedResults().stream().toList();
        final Aggregation CommentAggregation = queryNormalizeService.getCommentReacts(Criteria.where("user._id").is(new ObjectId(id)));
        List<CommentReaction> CommentReactionList = mongoTemplate.aggregate(CommentAggregation, "commentReaction", CommentReaction.class).getMappedResults().stream().toList();
        final Aggregation PackageAggregation = queryNormalizeService.getPackageReacts(Criteria.where("_id.user._id").is(new ObjectId(id)));
        List<PackageReaction> PackageReactions = mongoTemplate.aggregate(PackageAggregation, "packageReaction", PackageReaction.class).getMappedResults().stream().toList();
        final Aggregation PostAggregation = queryNormalizeService.getPostReactionFields(Criteria.where("_id.user._id").is(new ObjectId(id)));
        List<PostReaction> PostReactions = mongoTemplate.aggregate(PostAggregation, "postReaction", PostReaction.class).getMappedResults().stream().toList();
        final Aggregation CommentsAggregation = queryNormalizeService.getPostComments(Criteria.where("user._id").is(new ObjectId(id)), null);
        List<Comment> Comments = mongoTemplate.aggregate(CommentsAggregation, "comment", Comment.class).getMappedResults().stream().toList();
        final Aggregation replyAggregation = queryNormalizeService.getPostComments(Criteria.where("user._id").is(new ObjectId(id)), null);
        List<Reply> replies = mongoTemplate.aggregate(replyAggregation, "reply", Reply.class).getMappedResults().stream().toList();

        replyReactionRepository.deleteAllById(Reactions.stream().map(ReplyReaction::getId).collect(Collectors.toList()));
        commentReactionRepository.deleteAllById(CommentReactionList.stream().map(CommentReaction::getId).collect(Collectors.toList()));
        packageReactionRepository.deleteAllById(PackageReactions.stream().map(PackageReaction::getId).collect(Collectors.toList()));
        replyRepository.deleteAllById(replies.stream().map(Reply::getId).collect(Collectors.toList()));
        commentsRepository.deleteAllById(Comments.stream().map(Comment::getId).collect(Collectors.toList()));
        postReactionRepository.deleteAllById(PostReactions.stream().map(PostReaction::getId).collect(Collectors.toList()));

        logger.info("user Reaction Deleted");
        if (user != null) {
            if (user.getFirebaseId() != null) {
                try {
                    FirebaseAuth.getInstance().deleteUser(user.getFirebaseId());
                } catch (Exception ex) {
                    logger.error(ex.getMessage());
                }
                mqttPublisherService.sendNotificationToUser(user.getFirebaseId(), "deleted");
            } else {
                try {
                    var userRecord = FirebaseAuth.getInstance().getUserByEmail(user.getUserInfo().getEmail());
                    FirebaseAuth.getInstance().deleteUser(userRecord.getUid());
                    mqttPublisherService.sendNotificationToUser(userRecord.getUid(), "deleted");
                } catch (Exception ex) {
                    logger.error(ex.getMessage());
                }
            }

            userRepository.delete(user);
            searchUserRepository.deleteById(user.getId());
        }

        logger.info("user ");
    }

    public void sendNotification(String userId) {
        mqttPublisherService.sendNotificationToUser(userId, "deleted");
    }

    public void subscribeNotification(String userId) {
        mqttPublisherService.subscribeToTopic(userId);
    }

    private Criteria createSearchSpecification(Map<String, Object> obj) {
        List<Criteria> criteriaList = new ArrayList<>();

        // Global search term criteria (if provided)
        String searchTerm = obj.get("searchTerm") != null ? obj.get("searchTerm").toString() : "";
        if (!searchTerm.isEmpty()) {
            String escapedSearchTerm = Pattern.quote(searchTerm); // Escape regex special characters
            Criteria searchCriteria = new Criteria().orOperator(
                    Criteria.where("firstName").regex(escapedSearchTerm, "i"),
                    Criteria.where("lastName").regex(escapedSearchTerm, "i"),
                    Criteria.where("userInfo.email").regex(escapedSearchTerm, "i"), // Updated field path
                    Criteria.where("username").regex(escapedSearchTerm, "i")
            );
            criteriaList.add(searchCriteria);
        }

        // Selected list filter: user type (if provided)
        String usertype = obj.get("usertype") != null ? obj.get("usertype").toString() : "";
        if (!usertype.isEmpty()) {
            criteriaList.add(Criteria.where("usertype").is(usertype));
        }

        // Combine all provided criteria using AND
        Criteria query = new Criteria();
        if (!criteriaList.isEmpty()) {
            query.andOperator(criteriaList.toArray(new Criteria[0]));
        }

        // Log the query for debugging purposes
        System.out.println("Constructed Criteria: " + query.getCriteriaObject().toJson());
        return query;
    }


    private UserDto convertToDto(User user) {
        UserDto dto = modelMapper.map(user, UserDto.class);
        // Manual mapping for new fields
        dto.setSocialMediaLinks(user.getSocialMediaLinks());
        dto.setActive(user.isActive());
        dto.setLimited(user.isLimited());
        if (user.getMoods() != null) {
            dto.setMoods(user.getMoods().stream()
                    .map(m -> modelMapper.map(m, MoodDto.class))
                    .collect(Collectors.toList()));
        }
        return dto;
    }

    public void updateUserConnectionStatus(String userId, boolean isConnected) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new CustomException(404, "User not found!"));

        user.setConnected(isConnected);
        userRepository.save(user);
    }


    @Override
    public fcmToken AddFcmToken(fcmToken fcm, String userId) throws FirebaseAuthException {
        final Optional<User> byId = userRepository.findById(userId);

        if (byId.isEmpty())
            throw new CustomException(500, " User Not Found ");

        // Remove the FCM token from all other users first
        removeTokenFromOtherUsers(fcm.getToken(), userId);

        User user = byId.get();
        List<String> fcmTokens = user.getFcmTokens() == null ? new ArrayList<>() : user.getFcmTokens();
        if (!fcmTokens.contains(fcm.getToken()))
            fcmTokens.add(fcm.getToken());
        user.setFcmTokens(fcmTokens);
        userRepository.save(user);
        //Map<String, Object> claims = new HashMap<>();
       // claims.put("fcmTokens", user.getFcmTokens());
        //FirebaseAuth.getInstance().setCustomUserClaims(user.getFirebaseId(), claims);

        return fcm;
    }

    // In UserServiceImpl class

    @Override
    public UserStatsDto getUserStats(String userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new CustomException(404, "User not found"));

        UserStatsDto stats = new UserStatsDto();
        stats.setFollowers(user.getFollwerscount());

        // Posts count (inspired by searchUserItems filtering)
        long postsCount = mongoTemplate.count(
                new Query(Criteria.where("user.$id").is(new ObjectId(userId)).and("postType").is(PostType.Post)),
                Post.class
        );
        stats.setPosts((int) postsCount);

        // Stories count
        long storiesCount = mongoTemplate.count(
                new Query(Criteria.where("user.$id").is(new ObjectId(userId)).and("postType").is(PostType.Story)),
                Post.class
        );
        stats.setStories((int) storiesCount);

        // Media counts (inspired by getUserMedia filtering by mediaType)
        long reelsCount = mongoTemplate.count(
                new Query(Criteria.where("user.$id").is(new ObjectId(userId)).and("mediaType").is(MediaType.reel)),
                Media.class
        );
        stats.setReels((int) reelsCount);

        long imagesCount = mongoTemplate.count(
                new Query(Criteria.where("user.$id").is(new ObjectId(userId)).and("mediaType").is(MediaType.image)),
                Media.class
        );
        stats.setImages((int) imagesCount);

        long filesCount = mongoTemplate.count(
                new Query(Criteria.where("user.$id").is(new ObjectId(userId)).and("mediaType").is(MediaType.file)),
                Media.class
        );
        stats.setFiles((int) filesCount);

        long videosCount = mongoTemplate.count(
                new Query(Criteria.where("user.$id").is(new ObjectId(userId)).and("mediaType").is(MediaType.video)),
                Media.class
        );
        stats.setVideos((int) videosCount);


        Criteria packageCriteria = new Criteria("_class").is("com.hb.crm.core.searchBeans.searchPackage")
                .and("infulancer._id").is(new ObjectId(userId));
        long packagesCount = mongoTemplate2.count(new Query(packageCriteria), "search");  // Use "search" collection
        stats.setPackages((int) packagesCount);


        // NEW: Live Streams count
        long liveStreamsCount = mongoTemplate.count(
                new Query(Criteria.where("user.$id").is(new ObjectId(userId))),
                LiveStream.class
        );
        stats.setLiveStreams((int) liveStreamsCount);

        // NEW: Total Likes (sum reactions on posts + media)
        // Posts likes sum
        Aggregation postAgg = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("user.$id").is(new ObjectId(userId))),
                Aggregation.group().sum("reactsCount").as("total")
        );
        AggregationResults<Document> postResults = mongoTemplate.aggregate(postAgg, "post", Document.class);
        int postLikes = postResults.getMappedResults().isEmpty() ? 0 : postResults.getMappedResults().get(0).getInteger("total", 0);

        // Media likes sum
        Aggregation mediaAgg = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("user.$id").is(new ObjectId(userId))),
                Aggregation.group().sum("numberOfReactions").as("total")
        );
        AggregationResults<Document> mediaResults = mongoTemplate.aggregate(mediaAgg, "media", Document.class);
        int mediaLikes = mediaResults.getMappedResults().isEmpty() ? 0 : mediaResults.getMappedResults().get(0).getInteger("total", 0);

        stats.setLikes(postLikes + mediaLikes);

        return stats;
    }
    /**
     * Removes the specified FCM token from all users except the target user
     * @param fcmToken The FCM token to remove
     * @param targetUserId The user ID that should keep the token
     */
    private void removeTokenFromOtherUsers(String fcmToken, String targetUserId) throws FirebaseAuthException {
        List<User> usersWithToken = userRepository.findByFcmTokensContaining(fcmToken);

        for (User user : usersWithToken) {
            if (!user.getId().equals(targetUserId)) {
                List<String> fcmTokens = user.getFcmTokens();
                if (fcmTokens != null && fcmTokens.contains(fcmToken)) {
                    fcmTokens.remove(fcmToken);
                    user.setFcmTokens(fcmTokens);
                    userRepository.save(user);

                    // Update Firebase custom claims
                    if (user.getFirebaseId() != null) {
                        //Map<String, Object> claims = new HashMap<>();
                        //claims.put("fcmTokens", user.getFcmTokens());
                        //  FirebaseAuth.getInstance().setCustomUserClaims(user.getFirebaseId(), claims);
                    }
                }
            }
        }
    }

}
