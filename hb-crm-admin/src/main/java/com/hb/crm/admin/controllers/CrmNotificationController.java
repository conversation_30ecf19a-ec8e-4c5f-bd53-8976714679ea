//package com.hb.crm.admin.controllers;
//
//import com.hb.crm.admin.dto.notification.FilterAdminNotificationDto;
//import com.hb.crm.core.beans.Notification.CrmNotification;
//import com.hb.crm.core.dtos.notification.CreateCrmNotificationDto;
//import com.hb.crm.core.services.interfaces.CrmNotificationService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.media.Content;
//import io.swagger.v3.oas.annotations.media.Schema;
//import io.swagger.v3.oas.annotations.responses.ApiResponse;
//import io.swagger.v3.oas.annotations.responses.ApiResponses;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import lombok.RequiredArgsConstructor;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
//@RestController
//@RequestMapping("/crm-notification")
//@RequiredArgsConstructor
//@Tag(name = "Notification Management", description = "Endpoints for managing admin notifications")
//public class CrmNotificationController {
//
//    private final CrmNotificationService crmNotificationService;
//
//
//    @Operation(
//            summary = "Create new notification entry"
//    )
//    @ApiResponses(value = {
//            @ApiResponse(responseCode = "200", description = "Notification created",
//                    content = @Content(schema = @Schema(implementation = CrmNotification.class))),
//    })
//    @PostMapping("/new")
//    public ResponseEntity<CrmNotification> createNotification(@RequestBody CreateCrmNotificationDto dto) {
//        return ResponseEntity.ok(crmNotificationService.createNotification(dto));
//    }
//
//    @Operation(
//            summary = "Retrieve a notification by ID",
//            description = "Fetches a specific notification details using its unique identifier. Accessible by administrators."
//    )
//    @ApiResponses(value = {
//            @ApiResponse(responseCode = "200", description = "Successfully retrieved notification",
//                    content = @Content(schema = @Schema(implementation = CrmNotification.class))),
//    })
//    @GetMapping("/get/{id}")
//    public ResponseEntity<CrmNotification> getNotification(@PathVariable("id") String id) {
//        var notifications = crmNotificationService.getNotificationItem(id);
//        return ResponseEntity.ok(notifications);
//    }
//
//
//    @Operation(
//            summary = "Update notification read status",
//            description = "Updates the read status of a specific notification. Requires admin privileges."
//    )
//    @ApiResponses(value = {
//            @ApiResponse(responseCode = "200", description = "Notification status updated successfully",
//                    content = @Content(schema = @Schema(implementation = CrmNotification.class))),
//    })
//    @PostMapping("/update")
//    public ResponseEntity<CrmNotification> setReadStatus(@RequestBody boolean read, @RequestBody String notificationId) {
//        var notification = crmNotificationService.setReadStatus(read, notificationId);
//        return ResponseEntity.ok(notification);
//    }
//
//
//    @Operation(
//            summary = "Search and filter notifications",
//            description = "Retrieves a paginated list of notifications based on filter criteria. Admin-only access."
//    )
//    @ApiResponses(value = {
//            @ApiResponse(responseCode = "200", description = "Successfully retrieved filtered notifications",
//                    content = @Content(schema = @Schema(implementation = CrmNotification[].class))),
//    })
//    @PostMapping("/search")
//    public ResponseEntity<List<CrmNotification>> filterNotifications(@RequestBody FilterAdminNotificationDto filter) {
//        var notifications = crmNotificationService.filterAdminNotifications(
//                filter.getSearchText(),
//                filter.getFromDate(),
//                filter.getToDate(),
//                filter.getRead(),
//                filter.getType(),
//                filter.getPageNumber(),
//                filter.getPageSize()
//        );
//        return ResponseEntity.ok(notifications);
//    }
//
//
//    @DeleteMapping("/{id}")
//    @Operation(summary = "Delete Notification Item")
//    public ResponseEntity<Void> deleteNotificationChannel(@PathVariable String id) {
//        crmNotificationService.delete(id);
//        return ResponseEntity.noContent().build();
//    }
//
//}
