package com.hb.crm.admin.services;

import com.hb.crm.admin.beans.CustomUser;
import com.hb.crm.admin.dto.AuthenticationResponse;
import com.hb.crm.admin.dto.PageDto;
import com.hb.crm.admin.dto.PasswordResetRequest;
import com.hb.crm.admin.services.interfaces.EmployeeService;
import com.hb.crm.core.Enums.chat.AgentConversationStatus;
import com.hb.crm.core.beans.Employee;
import com.hb.crm.core.beans.Role;
import com.hb.crm.core.exceptions.CustomException;
import com.hb.crm.core.repositories.EmployeeRepository;
import com.hb.crm.core.repositories.SettingRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class EmployeeServiceImpl implements EmployeeService {

    private static final int ALL_LIMIT = 99999;

    private final Logger logger = LoggerFactory.getLogger(EmployeeServiceImpl.class);

    private final EmployeeRepository employeeRepository;
    private final PasswordEncoder bcryptEncoder;
    private final SettingRepository settingRepository;
    private final MongoTemplate mongoTemplate;


    @Autowired
    public EmployeeServiceImpl(EmployeeRepository employeeRepository, PasswordEncoder bcryptEncoder, SettingRepository settingRepository, @Qualifier("mongoTemplate1") MongoTemplate mongoTemplate) {
        this.employeeRepository = employeeRepository;
        this.bcryptEncoder = bcryptEncoder;
        this.settingRepository = settingRepository;
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public Employee save(Employee user) throws Exception {

        logger.debug("Start save");
        if(user==null){
            throw new Exception("Error in update admin User Cannot be null");
        }
       // validateUserInput(user);

        if (user.getId() == null) {
            user.setPassword(bcryptEncoder.encode(user.getPassword()));
            return employeeRepository.save(user);
        }

        final Optional<Employee> byId = employeeRepository.findById(user.getId());
        if(byId.isEmpty()){
            throw new Exception("Error in update admin User Cannot update");
        }


        final Employee userBean = byId.get();
        userBean.setUsername(user.getUsername());
        userBean.setAccountLocked(user.isAccountLocked());
        userBean.setFailedLoginAttempts(0);
        userBean.setFullName(user.getFullName());
        userBean.setGender(user.getGender());
        userBean.setRoles(user.getRoles());
        userBean.setMobile(user.getMobile());
        userBean.setProfilePicture(user.getProfilePicture());
        if(user.getPassword()!=null && !user.getPassword().isEmpty()){
           userBean.setPassword(bcryptEncoder.encode(user.getPassword()));
        }

        return employeeRepository.save(userBean);
    }

    @Override
    public PageDto<Employee> getUsers() {

        return search(new HashMap<String, Object>(), -1, 999);
    }
    @Override
    public void  delete(String id){
        employeeRepository.deleteById(id);
    }

    @Override
    public Optional<Employee> findById(String id) {
        return employeeRepository.findById(id);
    }

    @Override
    public PageDto<Employee> search(Map<String, Object> obj, int page, int limit) {
        if (page < 0) {
            limit = ALL_LIMIT;
            page = 0;
        }

        PageDto<Employee> pageDto = new PageDto<>();
        final Pageable pageable = PageRequest.of(page, limit);

        // Build the query using our criteria method
        final Query searchQuery = createSearchSpecification(obj);

        // Apply sorting and paging
        searchQuery.with(Sort.by(Sort.Direction.DESC, "creationDate"));
        searchQuery.with(pageable);

        // Retrieve the filtered employee list
        List<Employee> employees = mongoTemplate.find(searchQuery, Employee.class);

        // Count the number of documents that match the search query
        long count = mongoTemplate.count(searchQuery, Employee.class);

        // Optionally, remove sensitive fields (for example, password)
        employees.forEach(emp -> emp.setPassword(null));

        pageDto.setTotalNoOfItems(count);
        pageDto.setItems(employees);
        return pageDto;
    }

    @Override
    public CustomUser loadUserByUsername(String username) throws UsernameNotFoundException {

        List<SimpleGrantedAuthority> roles = null;
        Employee user = this.employeeRepository.findByUsername(username);
        if (user != null) {
            List<Role> userRoles = user.getRoles();
            if(userRoles!=null ){
                if(!userRoles.isEmpty()) {
                    roles = new ArrayList<>();
                    for (Role userRole : userRoles) {
                        roles.add(new SimpleGrantedAuthority("ROLE_" + userRole.getType()));
                    }
                }
            }
            return new CustomUser(user.getUsername(), user.getPassword(), roles, user.getId());
        }
        throw new UsernameNotFoundException("User not found with the name " + username);
    }

    @Override
    public Employee getEmployeeByUserName(String username) {
        return employeeRepository.findByUsername(username);
    }

    private Query createSearchSpecification(Map<String, Object> obj) {
        List<Criteria> criteriaList = new ArrayList<>();

        // Global search term criteria (if provided)
        String searchTerm = obj.get("searchTerm") != null ? obj.get("searchTerm").toString() : "";
        if (!searchTerm.isEmpty()) {
            // Create an OR criteria across multiple fields (case-insensitive)
            Criteria searchCriteria = new Criteria().orOperator(
                    Criteria.where("firstName").regex(searchTerm, "i"),
                    Criteria.where("lastName").regex(searchTerm, "i"),
                    Criteria.where("email").regex(searchTerm, "i"),
                    Criteria.where("username").regex(searchTerm, "i")
            );
            criteriaList.add(searchCriteria);
        }

        // Selected list filter: user type (if provided)
        String usertype = obj.get("usertype") != null ? obj.get("usertype").toString() : "";
        if (!usertype.isEmpty()) {
            criteriaList.add(Criteria.where("usertype").is(usertype));
        }

        // Combine all criteria using an AND operator
        Query query = new Query();
        if (!criteriaList.isEmpty()) {
            query.addCriteria(new Criteria().andOperator(criteriaList.toArray(new Criteria[0])));
        }

        // Log the constructed query for debugging
        System.out.println("Constructed Query: " + query.getQueryObject().toJson());
        return query;
    }
    public AuthenticationResponse restUsersPasswordConfirm(PasswordResetRequest passwordResetRequest) throws Exception {

        final String passKey = passwordResetRequest.getPassKey();
        if(passKey ==null || passKey.isEmpty()){
            throw new Exception("Invalid Token null");
        }

        if(passwordResetRequest.getPassword() ==null || passwordResetRequest.getPassword().isEmpty()
                || !passwordResetRequest.getPassword().equals(passwordResetRequest.getConfirmPassword())){
            throw new Exception("Invalid Password null");
        }
        AuthenticationResponse response= new AuthenticationResponse();
        final Optional<Employee> byPassResetKey = employeeRepository.findByPassResetKey(passKey);
        if(byPassResetKey.isPresent()){
            final Employee user = byPassResetKey.get();
            user.setPassword(bcryptEncoder.encode(passwordResetRequest.getPassword()));
            user.setPassResetKey(null);
            employeeRepository.save(user);
            response.setStatus("SUCCESS");
            response.setMessage("user Exist");
            return response;
        }
        throw new Exception("Invalid Token");
    }

    public AuthenticationResponse restUsersPasswordValidate(String passkey) throws Exception {
        AuthenticationResponse response= new AuthenticationResponse();
        final Optional<Employee> byPassResetKey = employeeRepository.findByPassResetKey(passkey);
        if(byPassResetKey.isPresent()){
            response.setStatus("SUCCESS");
            response.setMessage("user Exist");
            return response;
        }
        throw new Exception("Invalid Token");
    }

    @Override
    @Transactional
    public void updateEmployeeConnectionStatus(String userId, boolean isConnected) {
        Employee employee = employeeRepository.findById(userId)
                .orElseThrow(() -> new CustomException(404, "User not found!"));

        if(!isConnected)
            employee.setAgentConversationStatus(AgentConversationStatus.OFFLINE);

        else {
            if(employee.getAgentQueue().size() < Integer.parseInt(settingRepository
                    .findByName("MaxAgentQueueSizeForMainQueue").getValue()))
                employee.setAgentConversationStatus(AgentConversationStatus.BUSY);
            else
                employee.setAgentConversationStatus(AgentConversationStatus.ACTIVE);
        }
        employeeRepository.save(employee);
    }

    @Override
    public List<Employee> getOnlineEmployees() {
        return employeeRepository.findByAgentConversationStatusIn(List.of(AgentConversationStatus.ACTIVE,
                AgentConversationStatus.BUSY));
    }
}
