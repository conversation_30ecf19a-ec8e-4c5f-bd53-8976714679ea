package com.hb.crm.admin.services;

import com.hb.crm.admin.beans.UserSessionData;
import com.hb.crm.admin.config.CustomHundlerar.CustomException;
import com.hb.crm.admin.dto.PageDto;
import com.hb.crm.admin.dto.ReferenceModelDto;
import com.hb.crm.admin.dto.packageDto.MediaWrapperDto;
import com.hb.crm.admin.dto.posts.*;
import com.hb.crm.admin.dto.search.searchResultDto;
import com.hb.crm.admin.dto.users.UserDto;
import com.hb.crm.admin.services.interfaces.HighLightService;
import com.hb.crm.admin.services.interfaces.QueryNormalizeService;
import com.hb.crm.admin.services.interfaces.UserService;
import com.hb.crm.core.Enums.SearchEnum;
import com.hb.crm.core.Enums.UserType;
import com.hb.crm.core.beans.HighLight;
import com.hb.crm.core.beans.Post;
import com.hb.crm.core.beans.User;
import com.hb.crm.core.repositories.HighLightRepository;
import com.hb.crm.core.repositories.PackageRepository;
import com.hb.crm.core.repositories.PostRepository;
import com.mongodb.BasicDBObject;
import org.bson.types.ObjectId;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
@Service
public class HighLightServiceImpl implements HighLightService {


    private final HighLightRepository highLightRepository;
    private final UserSessionData userSessionData;
    private final MongoTemplate mongoTemplate2;
     private final MongoTemplate mongoTemplate;
     private final QueryNormalizeService queryNormalazieService;
     private final UserService userService;
     // postRepository
     private final PostRepository postRepository;
    private final PackageRepository packageRepository;
    private final ModelMapper modelMapper;


    public HighLightServiceImpl(HighLightRepository highLightRepository, UserSessionData userSessionData, MongoTemplate mongoTemplate2, MongoTemplate mongoTemplate, QueryNormalizeService queryNormalazieService, UserService userService, PostRepository postRepository, PackageRepository packageRepository, ModelMapper modelMapper) {
        this.highLightRepository = highLightRepository;
        this.userSessionData = userSessionData;
        this.mongoTemplate2 = mongoTemplate2;
        this.mongoTemplate = mongoTemplate;
        this.queryNormalazieService = queryNormalazieService;
        this.userService = userService;
        this.postRepository = postRepository;
        this.packageRepository = packageRepository;
        this.modelMapper = modelMapper;
    }

    @Override
    public HighLightViewDto createOrUpdateHighLight(GroupDTO groupDTO , String influencerId) {

        // try to see in the influencerId is null or not or exit in the db using the userService find by id service
        if (influencerId == null || influencerId.isEmpty()) {
            throw new IllegalArgumentException("Influencer ID is missing or invalid");
        }
        // Check if the influencer exists
        UserDto user = userService.findById(influencerId);
        if (user == null) {
            throw new CustomException(404, "Influencer not found");
        }
        // if the user type is not influencer then throw an exception
        if (user.getUsertype() != UserType.Influencer) {
            throw new CustomException(403, "User is not an influencer");
        }

        // check if the id exit if thier any highLight with that id


        if (groupDTO.getId() != null && !groupDTO.getId().isEmpty()) {
            Optional<HighLight> existingGroup = highLightRepository.findById(groupDTO.getId());
            if (existingGroup.isPresent()) {
                HighLight highLight = existingGroup.get();
                highLight.setImage(groupDTO.getImage());
                highLight.setStories(getPostReferences(groupDTO.getStoryIds()));
                highLight.setName(groupDTO.getName());
                highLight= highLightRepository.save(highLight);

                return  modelMapper.map(highLight, HighLightViewDto.class);
            }
        }
            HighLight highLight;
            highLight = new HighLight();
            highLight.setInfluencer(new User(influencerId));
            highLight.setName(groupDTO.getName());
            highLight.setImage(groupDTO.getImage());
            highLight.setStories(getPostReferences(groupDTO.getStoryIds()));
            highLight= highLightRepository.save(highLight);

        return  modelMapper.map(highLight, HighLightViewDto.class);
    }

    @Override
    public HighLightViewDto addStoryToHighLight(String groupId, List<String> storyIds) {
        HighLight highLight = highLightRepository.findById(groupId)
                .orElseThrow(() -> new CustomException(404,"HighLight not found"));

        highLight.setStories(getPostReferences(storyIds)); // Replace the existing list with new stories



         highLight = highLightRepository.save(highLight);
        HighLightViewDto item = modelMapper.map(highLight,HighLightViewDto.class);

        return item ;
    }

    @Override
    public PageDto<HighLightListDTO> getHighLightsForInfluencer(String username, String influencerId, String keyword, int page, int size) {
        List<AggregationOperation> operations = new ArrayList<>();

        // Match by influencer ID if provided
        if (influencerId != null && !influencerId.isEmpty()) {
            operations.add(Aggregation.match(Criteria.where("influencer.$id").is(new ObjectId(influencerId))));
        }

        // Match by group name (keyword)
        if (keyword != null && !keyword.trim().isEmpty()) {
            operations.add(Aggregation.match(Criteria.where("name").regex(keyword, "i")));
        }

        // If username is provided, perform lookup and filter by username
        boolean filterByUsername = username != null && !username.trim().isEmpty();
        if (filterByUsername) {
            operations.add(Aggregation.lookup("user", "influencer.$id", "_id", "influencerDoc"));
            operations.add(Aggregation.unwind("influencerDoc"));
            operations.add(Aggregation.match(Criteria.where("influencerDoc.username").is(username)));
        }

        // Pagination
        operations.add(Aggregation.sort(Sort.Direction.DESC, "created"));
        operations.add(Aggregation.skip((long) page * size));
        operations.add(Aggregation.limit(size));

        Aggregation aggregation = Aggregation.newAggregation(operations);
        List<HighLightListDTO> items = mongoTemplate.aggregate(aggregation, "group", HighLightListDTO.class).getMappedResults();

        // Count aggregation (without pagination stages)
        List<AggregationOperation> countOps = new ArrayList<>(operations);
        countOps.removeIf(op -> op instanceof SkipOperation || op instanceof LimitOperation || op instanceof SortOperation);
        countOps.add(Aggregation.count().as("total"));
        AggregationResults<BasicDBObject> countResults = mongoTemplate.aggregate(Aggregation.newAggregation(countOps), "group", BasicDBObject.class);
        long total = countResults.getUniqueMappedResult() != null ? countResults.getUniqueMappedResult().getLong("total") : 0;

        // Wrap into PageDto
        PageDto<HighLightListDTO> pageDto = new PageDto<>(size, page, "created");
        pageDto.setItems(items);
        pageDto.setTotalNoOfItems(total);
        return pageDto;
    }


    @Override
    public searchResultDto getPostsByGroupId(String id, String query,int page, int limit) {
        HighLight highLight = highLightRepository.findById(id)
                .orElseThrow(() -> new CustomException(404,"HighLight not found"));
        List<ObjectId> storyIds = highLight.getStories().stream()
                .map(post -> new ObjectId(post.getId()))  // Convert String ID to ObjectId
                .collect(Collectors.toList());

        Criteria criteria = new Criteria();
        criteria.and("_id").in(storyIds);  // Match the post IDs in the list of story IDs
        // Create pageable with descending sort on "updated" field.
        final Pageable pageable = PageRequest.of(page, limit, Sort.Direction.DESC, "updated");
        // Retrieve the search aggregation pipeline based on query and classFilter.
        Aggregation aggregation = queryNormalazieService.getSearch(query, SearchEnum.story, pageable, criteria);
        // Execute the aggregation query against the "search" collection and map the results to the searchResultDto class.
        var result = mongoTemplate2.aggregate(aggregation, "search", searchResultDto.class);
        // Return a unique mapped result from the aggregation query.
        return result.getUniqueMappedResult();
    }


    @Override
    public searchResultDto getStoriesByInfulancer( String influencerId,  String query,int page, int limit) {

        if (influencerId == null || influencerId.isEmpty()) {
            throw new IllegalArgumentException("Influencer ID is missing or invalid");
        }
        // Check if the influencer exists
        UserDto user = userService.findById(influencerId);
        if (user == null) {
            throw new CustomException(404, "Influencer not found");
        }
        // if the user type is not influencer then throw an exception
        if (user.getUsertype() != UserType.Influencer) {
            throw new CustomException(403, "User is not an influencer");
        }

        Criteria criteria =  Criteria.where("user._id").is(new ObjectId(user.getId()));

        // Create pageable with descending sort on "updated" field.
        final Pageable pageable = PageRequest.of(page, limit, Sort.Direction.DESC, "updated");
        // Retrieve the search aggregation pipeline based on query and classFilter.
        Aggregation aggregation = queryNormalazieService.getSearch(query, SearchEnum.story, pageable, criteria);
        // Execute the aggregation query against the "search" collection and map the results to the searchResultDto class.
        var result = mongoTemplate2.aggregate(aggregation, "search", searchResultDto.class);
        // Return a unique mapped result from the aggregation query.
        return result.getUniqueMappedResult();
    }

    @Override
    public void deleteHighLight(String groupId) {
        HighLight highLight = highLightRepository.findById(groupId)
                .orElseThrow(() -> new CustomException(404, "HighLight not found"));
        highLightRepository.delete(highLight);

    }


    private List<Post> getPostReferences(List<String> storyIds) {
        return storyIds.stream()
                .map(Post::new) // Wrap each storyId as a Reference
                .collect(Collectors.toList());
    }

    @Override
    public PageDto<HighLightWithStoryCountDTO> getHighLightsWithStoryCount(String influencerId, String keyword, int page, int size) {
        List<AggregationOperation> operations = new ArrayList<>();

        // Step 1: First lookup to get influencer details
        operations.add(Aggregation.lookup("user", "influencer.$id", "_id", "influencerDoc"));
        operations.add(Aggregation.unwind("influencerDoc", true)); // Preserve groups with no influencer

        // Build OR criteria for unified search
        Criteria criteria = new Criteria();
        if (StringUtils.hasText(keyword)) {
            String regexKeyword = ".*" + keyword + ".*";
            String caseInsensitiveKeyword = "(?i)" + keyword;

            criteria.orOperator(
                    // Search highlight name
                    Criteria.where("name").regex(regexKeyword, "i"),

                    // Search influencer first name
                    Criteria.where("influencerDoc.firstName").regex(caseInsensitiveKeyword),

                    // Search influencer last name
                    Criteria.where("influencerDoc.lastName").regex(caseInsensitiveKeyword)


            );
        }

        if (!criteria.getCriteriaObject().isEmpty()) {
            operations.add(Aggregation.match(criteria));
        }

        // Project required fields
        operations.add(Aggregation.project()
                .andInclude("id", "name", "image")
                .and("influencerDoc._id").as("influencerId")
                .and("influencerDoc.firstName").as("influencerFirstName")
                .and("influencerDoc.lastName").as("influencerLastName")
                .and("influencerDoc.profileImage").as("influencerProfileImage")
                .andExpression("size(stories)").as("storiesCount")
        );

        // Sort by creation date descending
        operations.add(Aggregation.sort(Sort.by(Sort.Direction.DESC, "created")));

        // Pagination
        operations.add(Aggregation.skip((long) page * size));
        operations.add(Aggregation.limit(size));

        Aggregation aggregation = Aggregation.newAggregation(operations);
        List<HighLightWithStoryCountDTO> items = mongoTemplate.aggregate(
                aggregation, "group", HighLightWithStoryCountDTO.class
        ).getMappedResults();

        // Total count aggregation (with same criteria)
        List<AggregationOperation> countOperations = new ArrayList<>();
        countOperations.add(Aggregation.lookup("user", "influencer.$id", "_id", "influencerDoc"));
        countOperations.add(Aggregation.unwind("influencerDoc", true));

        if (!criteria.getCriteriaObject().isEmpty()) {
            countOperations.add(Aggregation.match(criteria));
        }

        countOperations.add(Aggregation.count().as("total"));
        Aggregation countAggregation = Aggregation.newAggregation(countOperations);
        AggregationResults<BasicDBObject> countResults = mongoTemplate.aggregate(
                countAggregation, "group", BasicDBObject.class
        );

        long total = countResults.getUniqueMappedResult() != null ?
                countResults.getUniqueMappedResult().getLong("total") : 0;

        // Wrap into PageDto
        PageDto<HighLightWithStoryCountDTO> pageDto = new PageDto<>(size, page, "created");
        pageDto.setItems(items);
        pageDto.setTotalNoOfItems(total);
        return pageDto;
    }
    @Override
    public HighLightDetailDto getHighLightById(String groupId) {
        HighLight highLight = highLightRepository.findById(groupId)
                .orElseThrow(() -> new CustomException(404, "HighLight not found"));
        HighLightDetailDto dto = new HighLightDetailDto();
        dto.setId(highLight.getId());
        dto.setName(highLight.getName());
        dto.setImage(highLight.getImage());

        // Fetch influencer details
        UserDto influencer = userService.findById(highLight.getInfluencer().getId());
        dto.setInfluencerId(influencer.getId());
        dto.setInfluencerFirstName(influencer.getFirstName());
        dto.setInfluencerLastName(influencer.getLastName());
        dto.setInfluencerProfileImage(influencer.getProfileImage());

        List<Post> posts = postRepository.findAllById(
                highLight.getStories().stream().map(Post::getId).collect(Collectors.toList())
        );

        // Collect package IDs from all posts
        Set<String> packageIds = posts.stream()
                .filter(post -> post.getPackage() != null)
                .map(post -> post.getPackage().getId())
                .collect(Collectors.toSet());

        // Fetch all packages in bulk
        Map<String, com.hb.crm.core.beans.Package> packageMap = packageIds.isEmpty()
                ? Collections.emptyMap()
                : packageRepository.findAllById(packageIds).stream()
                .collect(Collectors.toMap(com.hb.crm.core.beans.Package::getId, Function.identity()));

        dto.setStories(posts.stream().map(post -> {
            PostDto postDto = new PostDto();
            postDto.setId(post.getId());
            postDto.setText(post.getText());
            postDto.setCreated(post.getCreated());
            postDto.setViewsCount(post.getViewsCount());
            //slug
            if (post.getSlug() != null && !post.getSlug().isEmpty()) {
                postDto.setSlug(post.getSlug());
            }


            // Tags
            if (post.getTags() != null && !post.getTags().isEmpty()) {
                postDto.setTags(post.getTags());
            }

            // Media
            if (post.getMedia() != null && !post.getMedia().isEmpty()) {
                postDto.setMedia(post.getMedia().stream().map(media -> {
                    MediaWrapperDto mediaWrapper = new MediaWrapperDto();
                    mediaWrapper.setUrl(media.getUrl());
                    mediaWrapper.setType(media.getType());
                    return mediaWrapper;
                }).collect(Collectors.toList()));
            }

            // Add package information if exists
            if (post.getPackage() != null) {
                com.hb.crm.core.beans.Package pkg = packageMap.get(post.getPackage().getId());
                if (pkg != null) {
                    ReferenceModelDto pkgDto = new ReferenceModelDto();
                    pkgDto.setId(pkg.getId());
                    pkgDto.setName(pkg.getName());
                    postDto.setPackage(pkgDto);
                }
            }

            return postDto;
        }).collect(Collectors.toList()));

        return dto;
    }
}