package com.hb.crm.admin.dto.packageDto;

import com.hb.crm.admin.dto.MoodDto.MoodDto;
import com.hb.crm.admin.dto.keyAndValue;
import com.hb.crm.admin.dto.users.UserInfoDto;
import com.hb.crm.core.Enums.PackageStatus;
import com.hb.crm.core.Enums.PackageType;
import com.hb.crm.core.Enums.State;
import com.hb.crm.core.beans.Flight.Flight;
import com.hb.crm.core.beans.FlightPackage.Airport;
import com.hb.crm.core.beans.Hotel.Hotel;
import com.hb.crm.core.beans.MediaWrapper;
import com.hb.crm.core.beans.PackagePlaces.PackageCountry;
import com.hb.crm.core.beans.Tag;
import com.hb.crm.core.dtos.PackageEditRequest;
import com.hb.crm.core.dtos.chat.response.SimpleGroupConversationResponseDto;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


public class PackageDto {
    @Id
    private String id;
    private String link;
    private PackageType packageType = PackageType.TravelWithMe;
    private AlphaPackage _package;

    private String name;
    private String description;
    private Object details;
    private List<MediaWrapperDto> medias;
    private LocalDateTime start;
    private LocalDateTime end;
    private LocalDateTime UpdateDate;
    //slug

    private String slug;
    private String followMeSlug;



    private UserInfoDto infulancer;
    private int capacity;
    private int subscribeCount;
    private List<Tag> tags;
    private boolean favouritebyme;
    private State state;
    private List<MoodDto> moods;
    private  List<ActivityDto> activities;
    private Object price;

    private LocalDateTime CreationDate;
    private PackageStatus packageStatus;
    private  String rejectionNote="";
    private BigDecimal totalPrice=BigDecimal.ZERO;
    private List<Flight> flights;
    private List<Hotel> hotels;
    private int numberOfRoom  ;
    private boolean availableForFollowMe;
    private  LocalDate availableFrom;
    private MediaWrapper brochure;
    private  MediaWrapper mainImage;
    private int followMeDiscount;
    private  boolean privateDate=false;
    private Airport fromAirport;
    private Airport toAirport;
    private List<PackageCountry> packagePlaces;
    private  boolean fromAirportInside;
    private  boolean toAirportInside;

    @Getter
    @Setter
    private SimpleGroupConversationResponseDto conversation;

    @Getter
    @Setter
    private List<PackageEditRequest> modifyRequest;


    private  boolean  exceedSeats;
    public LocalDateTime getUpdateDate() {
        return UpdateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        UpdateDate = updateDate;
    }


    public List<PackageCountry> getPackagePlaces() {
        return packagePlaces;
    }

    public void setPackagePlaces(List<PackageCountry> packagePlaces) {
        this.packagePlaces = packagePlaces;
    }

    public void setDuration(long duration) {
        this.duration = duration;
    }



    private  long duration;

    public long getDuration() {
        return ChronoUnit.DAYS.between(this.start, this.end) + 1;
    }

    public LocalDate getAvailableFrom() {
        return  get_package().getAvailableFrom();
    }

    public void setAvailableFrom(LocalDate availableFrom) {
        this.availableFrom = availableFrom;
    }

    public boolean isAvailableForFollowMe() {
        return _package.isAvailableForFollowMe();
    }

    public void setAvailableForFollowMe(boolean availableForFollowMe) {
        this._package.setAvailableForFollowMe(availableForFollowMe);
    }

    public List<Hotel> getHotels() {
        return hotels;
    }

    public void setHotels(List<Hotel> hotels) {
        this.hotels = hotels;
    }


    public AlphaPackage get_package() {
        return _package;
    }

    public void set_package(AlphaPackage _package) {
        this._package = _package;
    }

    public int getNumberOfRoom() {
        return numberOfRoom;
    }

    public void setNumberOfRoom(int numberOfRoom) {
        this.numberOfRoom = numberOfRoom;
    }

    public String getRejectionNote() {
        return rejectionNote;
    }

    public void setRejectionNote(String rejectionNote) {
        this.rejectionNote = rejectionNote;
    }

    public LocalDateTime getCreationDate() {
        return  CreationDate;
    }
    public void setCreationDate(LocalDateTime creationDate) {
        CreationDate = creationDate;
    }
    public List<ActivityDto> getActivities() {
        return activities;
    }
    public void setActivities(List<ActivityDto> activities) {
        this.activities = activities;
    }
    public State getState() {
        return state;
    }
    public void setState(State state) {
        this.state = state;
    }
    public boolean isFavouritebyme() {
        return favouritebyme;
    }
    public void setFavouritebyme(boolean favouritebyme) {
        this.favouritebyme = favouritebyme;
    }
    public List<Tag> getTags() {
        return _package.getTags() ;
    }
    public void setTags(List<Tag> tags) {
        _package.setTags(tags);
    }
    public UserInfoDto getInfulancer() {
        return _package.getInfulancer();
    }
    public void setInfulancer(UserInfoDto infulancer) {
        this.infulancer = infulancer;
    }
    public int getCapacity() {
        return capacity;
    }
    public void setCapacity(int capacity) {
        this.capacity = capacity;
    }
    public int getSubscribeCount() {
        return subscribeCount;
    }
    public void setSubscribeCount(int subscribeCount) {
        this.subscribeCount = subscribeCount;
    }
    public LocalDateTime getStart() {
        return start;
    }
    public void setStart(LocalDateTime start) {
        this.start = start;
    }
    public LocalDateTime getEnd() {
        return end;
    }
    public void setEnd(LocalDateTime end) {
        this.end = end;
    }
    public List<MediaWrapperDto> getMedias() {
        return _package.getMedias();
    }
    public void setMedias(List<MediaWrapperDto> medias) {
        _package.setMedias( medias);
    }
    public List<MoodDto> getMoods() {
        return _package.getMoods();
    }
    public void setMoods(List<MoodDto> moods) {
        _package.setMoods( moods);
    }
    public Object getPrice() {
        HashMap<String, String> map = (HashMap<String, String>) this.price;
        if (map == null) {
            return this.price;
        }
        return getingvalues(map);
    }
    public String getId() {
        return id;
    }
    public void setId(String id) {
        this.id = id;
    }
    public PackageType getPackageType() {
        return packageType;
    }
    public void setPackageType(PackageType packageType) {
        this.packageType = packageType;
    }
    public String getName() {
        return _package.getName();
    }
    public void setName(String name) {
        _package.setName(name);
    }
    public String getDescription() {
        return _package.getDescription();
    }
    public int getFollowMeDiscount() {
        return get_package().getFollowMeDiscount();
    }
    public void setFollowMeDiscount(int followMeDiscount) {
        this.followMeDiscount = followMeDiscount;
    }
    public void setDescription(String description) {
        _package.setDescription(description);
    }
    public Object getDetails() {
        HashMap<String, String> map = (HashMap<String, String>) this.details;
        if (map == null) {
            return this.details;
        }
        return getingvalues(map);
    }
    public MediaWrapperDto getBrochure() {
        return _package.getBrochure();
    }
    public void setBrochure(MediaWrapperDto brochure) {
        this._package.setBrochure(brochure);
    }

     public void setDetails(Object details) {
        this.details = details;
    }
    public void setPrice(Object price) {
        this.price = price;
    }
    public PackageStatus getPackageStatus() {
        return packageStatus;
    }
    public void setPackageStatus(PackageStatus packageStatus) {
        this.packageStatus = packageStatus;
    }
    public BigDecimal getTotalPrice() {
        return totalPrice;
    }
    public List<Flight> getFlights() {
        return flights;
    }
    public void setFlights(List<Flight> flights) {
        this.flights = flights;
    }
    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }
    private List<keyAndValue> getingvalues(HashMap<String, String> map) {
        List<keyAndValue> values = new ArrayList<keyAndValue>();
        for (HashMap.Entry<String, String> entry : map.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            values.add(new keyAndValue(key, value));
        }
        return values;
    }
    public boolean isPrivateDate() {
        return privateDate;
    }

    public void setPrivateDate(boolean privateDate) {
        this.privateDate = privateDate;
    }

    public Airport getFromAirport() {
        return this._package.getFromAirport();
    }

    public Airport getToAirport() {
        return this._package.getToAirport();
    }
    public boolean isFromAirportInside() {
        return this._package.isFromAirportInside();
    }
    public boolean isToAirportInside() {
        return this._package.isToAirportInside();
    }

    public boolean isExceedSeats() {
        return subscribeCount > capacity;
    }

    public String getSlug() {
        return slug;
    }

    public void setSlug(String slug) {
        this.slug = slug;
    }

    public String getFollowMeSlug() {
        return followMeSlug;
    }

    public void setFollowMeSlug(String followMeSlug) {
        this.followMeSlug = followMeSlug;
    }
}
