package com.hb.crm.admin.services;

import com.hb.crm.admin.dto.PageDto;
import com.hb.crm.admin.services.interfaces.TrashedSubPackageService;
import com.hb.crm.core.Enums.PackageType;
import com.hb.crm.core.beans.SubPackage;
import com.hb.crm.core.beans.Trash.TrashedSubPackage;
import com.hb.crm.core.exceptions.CustomException;
import com.hb.crm.core.repositories.SubPackageRepository;
import com.hb.crm.core.repositories.TrashedSubPackageRepository;
import com.hb.crm.core.searchBeans.searchPackage;
import com.hb.crm.core.searchBeans.simpleUserInfo;
import com.hb.crm.core.searchBeans.SearchMood;
import com.hb.crm.core.searchBeans.MediaWrapperSearch;
import com.hb.crm.core.searchBeans.SearchMedia;
import com.hb.crm.core.searchRepositories.SearchPackageRepository;
import com.hb.crm.core.beans.Mood;
import com.hb.crm.core.beans.MediaWrapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TrashedSubPackageServiceImpl implements TrashedSubPackageService {

    @Autowired
    private TrashedSubPackageRepository trashedSubPackageRepository;

    @Autowired
    private SubPackageRepository subPackageRepository;

    @Autowired
    private SearchPackageRepository searchPackageRepository;

    @Override
    @Transactional
    public TrashedSubPackage softDeleteSubPackage(String subPackageId, String deletionReason) {
        // Find the SubPackage to delete
        SubPackage subPackage = subPackageRepository.findById(subPackageId)
                .orElseThrow(() -> new CustomException(404, "SubPackage not found with ID: " + subPackageId));

        // Check if already in trash
        if (trashedSubPackageRepository.existsByOriginalSubPackageId(subPackageId)) {
            throw new CustomException(400, "SubPackage is already deleted!");
        }

        // Create TrashedSubPackage and copy all data
        TrashedSubPackage trashedSubPackage = new TrashedSubPackage();
        copySubPackageToTrashedSubPackage(subPackage, trashedSubPackage);
        
        // Set trash-specific metadata
        trashedSubPackage.setOriginalSubPackageId(subPackageId);
        trashedSubPackage.setDeletionReason(deletionReason);
        trashedSubPackage.setDeletedAt(LocalDateTime.now());

        // Save to trash
        trashedSubPackage = trashedSubPackageRepository.save(trashedSubPackage);

        // Remove from search index
        searchPackageRepository.deleteById(subPackageId);

        // Delete original SubPackage
        subPackageRepository.deleteById(subPackageId);

        return trashedSubPackage;
    }

    @Override
    @Transactional
    public SubPackage restoreSubPackage(String trashedSubPackageId) {
        // Find the TrashedSubPackage
        TrashedSubPackage trashedSubPackage = trashedSubPackageRepository.findById(trashedSubPackageId)
                .orElseThrow(() -> new CustomException(404, "TrashedSubPackage not found with ID: " + trashedSubPackageId));

        // Check if original ID already exists (conflict resolution)
        if (subPackageRepository.existsById(trashedSubPackage.getOriginalSubPackageId())) {
            throw new CustomException(400, "Cannot restore: A SubPackage with the original ID already exists");
        }

        // Create new SubPackage and copy data back
        SubPackage restoredSubPackage = new SubPackage();
        copyTrashedSubPackageToSubPackage(trashedSubPackage, restoredSubPackage);
        
        // Use original ID for restoration
        restoredSubPackage.setId(trashedSubPackage.getOriginalSubPackageId());

        // Save restored SubPackage
        restoredSubPackage = subPackageRepository.save(restoredSubPackage);

        // Rebuild search package index
        rebuildSearchPackage(restoredSubPackage);

        // Remove from trash
        trashedSubPackageRepository.deleteById(trashedSubPackageId);

        return restoredSubPackage;
    }

    @Override
    public PageDto<TrashedSubPackage> getTrashedPackages(int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "deletedAt"));
        Page<TrashedSubPackage> trashedPage = trashedSubPackageRepository.findAllByOrderByDeletedAtDesc(pageable);
        
        return new PageDto<>(
                trashedPage.getSize(),
                trashedPage.getTotalElements(),
                trashedPage.getNumber(),
                trashedPage.getContent()
        );
    }

    @Override
    public PageDto<TrashedSubPackage> getTrashedPackagesByType(PackageType packageType, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "deletedAt"));
        Page<TrashedSubPackage> trashedPage = trashedSubPackageRepository.findByPackageType(packageType, pageable);
        
        return new PageDto<>(
                trashedPage.getSize(),
                trashedPage.getTotalElements(),
                trashedPage.getNumber(),
                trashedPage.getContent()
        );
    }

    @Override
    public PageDto<TrashedSubPackage> searchTrashedPackages(String query, int page, int size) {
        if (query == null || query.trim().isEmpty()) {
            return getTrashedPackages(page, size);
        }

        List<TrashedSubPackage> searchResults = trashedSubPackageRepository.findBySearchTerm(query.trim());
        
        // Manual pagination for search results
        int start = page * size;
        int end = Math.min(start + size, searchResults.size());
        List<TrashedSubPackage> paginatedResults = searchResults.subList(start, end);
        
        return new PageDto<>(
                size,
                searchResults.size(),
                page,
                paginatedResults
        );
    }

    @Override
    public PageDto<TrashedSubPackage> searchTrashedPackages(String query, PackageType packageType, int page, int size) {
        if (query == null || query.trim().isEmpty()) {
            return getTrashedPackagesByType(packageType, page, size);
        }

        List<TrashedSubPackage> searchResults = trashedSubPackageRepository.findBySearchTermAndPackageType(query.trim(), packageType);
        
        // Manual pagination for search results
        int start = page * size;
        int end = Math.min(start + size, searchResults.size());
        List<TrashedSubPackage> paginatedResults = searchResults.subList(start, end);
        
        return new PageDto<>(
                size,
                searchResults.size(),
                page,
                paginatedResults
        );
    }

    @Override
    public PageDto<TrashedSubPackage> getTrashedPackagesByDateRange(LocalDateTime startDate, LocalDateTime endDate, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "deletedAt"));
        Page<TrashedSubPackage> trashedPage = trashedSubPackageRepository.findByDeletedAtBetween(startDate, endDate, pageable);
        
        return new PageDto<>(
                trashedPage.getSize(),
                trashedPage.getTotalElements(),
                trashedPage.getNumber(),
                trashedPage.getContent()
        );
    }

    @Override
    public TrashedSubPackage getTrashedPackageById(String trashedSubPackageId) {
        return trashedSubPackageRepository.findById(trashedSubPackageId)
                .orElseThrow(() -> new CustomException(404, "TrashedSubPackage not found with ID: " + trashedSubPackageId));
    }

    @Override
    @Transactional
    public void permanentlyDeleteTrashedPackage(String trashedSubPackageId) {
        if (!trashedSubPackageRepository.existsById(trashedSubPackageId)) {
            throw new CustomException(404, "TrashedSubPackage not found with ID: " + trashedSubPackageId);
        }
        trashedSubPackageRepository.deleteById(trashedSubPackageId);
    }

    @Override
    @Transactional
    public void emptyTrash() {
        trashedSubPackageRepository.deleteAll();
    }

    @Override
    public long getTrashedPackagesCount(PackageType packageType) {
        if (packageType == null) {
            return trashedSubPackageRepository.count();
        }
        return trashedSubPackageRepository.countByPackageType(packageType);
    }

    @Override
    public long getTotalTrashedPackagesCount() {
        return trashedSubPackageRepository.count();
    }

    @Override
    public boolean isSubPackageInTrash(String subPackageId) {
        return trashedSubPackageRepository.existsByOriginalSubPackageId(subPackageId);
    }

    /**
     * Copy all fields from SubPackage to TrashedSubPackage
     */
    private void copySubPackageToTrashedSubPackage(SubPackage source, TrashedSubPackage target) {
        // Use BeanUtils for basic field copying, then handle special cases
        BeanUtils.copyProperties(source, target, "id"); // Exclude ID to generate new one
        
        // Copy all fields manually to ensure completeness
        target.setName(source.getName());
        target.setTotalPrice(source.getTotalPrice());
        target.setPackageStatus(source.getPackageStatus());
        target.setConfirmFlightprices(source.getConfirmFlightprices());
        target.setPrivateDate(source.isPrivateDate());
        target.setCapacity(source.getCapacity());
        target.setStart(source.getStart());
        target.setEnd(source.getEnd());
        target.setDetails(source.getDetails());
        target.setPrice(source.getPrice());
        target.setRejectionNote(source.getRejectionNote());
        target.setPackageType(source.getPackageType());
        target.setCreationDate(source.getCreationDate());
        target.setUpdateDate(source.getUpdateDate());
        target.setFollowMeDiscount(source.getFollowMeDiscount());
        target.setNumberOfRoom(source.getNumberOfRoom());
        target.setFlights(source.getFlights());
        target.setSubscribeCount(source.getSubscribeCount());
        target.set_package(source.get_package());
        target.setPackagePlaces(source.getPackagePlaces());
        target.setHotels(source.getHotels());
        target.setActivities(source.getActivities());
        target.setSlug(source.getSlug());
        target.setFollowMeSlug(source.getFollowMeSlug());
        target.setRates(source.getRates());
        target.setState(source.getState());
        target.setModifyRequest(source.getModifyRequest());
    }

    /**
     * Copy all fields from TrashedSubPackage back to SubPackage
     */
    private void copyTrashedSubPackageToSubPackage(TrashedSubPackage source, SubPackage target) {
        // Copy all fields back, excluding trash-specific metadata
        target.setName(source.getName());
        target.setTotalPrice(source.getTotalPrice());
        target.setPackageStatus(source.getPackageStatus());
        target.setConfirmFlightprices(source.getConfirmFlightprices());
        target.setPrivateDate(source.isPrivateDate());
        target.setCapacity(source.getCapacity());
        target.setStart(source.getStart());
        target.setEnd(source.getEnd());
        target.setDetails(source.getDetails());
        target.setPrice(source.getPrice());
        target.setRejectionNote(source.getRejectionNote());
        target.setPackageType(source.getPackageType());
        target.setCreationDate(source.getCreationDate());
        target.setUpdateDate(LocalDateTime.now()); // Update the restoration time
        target.setFollowMeDiscount(source.getFollowMeDiscount());
        target.setNumberOfRoom(source.getNumberOfRoom());
        target.setFlights(source.getFlights());
        target.setSubscribeCount(source.getSubscribeCount());
        target.set_package(source.get_package());
        target.setPackagePlaces(source.getPackagePlaces());
        target.setHotels(source.getHotels());
        target.setActivities(source.getActivities());
        target.setSlug(source.getSlug());
        target.setFollowMeSlug(source.getFollowMeSlug());
        target.setRates(source.getRates());
        target.setState(source.getState());
        target.setModifyRequest(source.getModifyRequest());
    }

    /**
     * Rebuild search package index for restored SubPackage
     */
    private void rebuildSearchPackage(SubPackage subPackage) {
        try {
            // Create search package from restored SubPackage
            searchPackage searchPkg = new searchPackage();

            // Copy basic fields
            searchPkg.setId(subPackage.getId());
            searchPkg.setName(subPackage.getName());
            searchPkg.setPackageStatus(subPackage.getPackageStatus());
            searchPkg.setPackageType(subPackage.getPackageType());
            searchPkg.setState(subPackage.getState());
            searchPkg.setTotalPrice(subPackage.getTotalPrice());
            searchPkg.setCapacity(subPackage.getCapacity());
            searchPkg.setStart(subPackage.getStart());
            searchPkg.setEnd(subPackage.getEnd());
            searchPkg.setCreationDate(subPackage.getCreationDate());
            searchPkg.setUpdateDate(subPackage.getUpdateDate());
            searchPkg.setSubscribeCount(subPackage.getSubscribeCount());
            searchPkg.setSlug(subPackage.getSlug());
            searchPkg.setFollowMeSlug(subPackage.getFollowMeSlug());
            searchPkg.setPackagePlaces(subPackage.getPackagePlaces());

            // Set package reference
            if (subPackage.get_package() != null) {
                searchPkg.setPackageId(subPackage.get_package().getId());
                searchPkg.setDescription(subPackage.get_package().getDescription());
                searchPkg.setTags(subPackage.get_package().getTags());
                searchPkg.setFromAirport(subPackage.get_package().getFromAirport());
                searchPkg.setToAirport(subPackage.get_package().getToAirport());
                searchPkg.setFromAirportInside(subPackage.get_package().isFromAirportInside());
                searchPkg.setToAirportInside(subPackage.get_package().isToAirportInside());

                // Convert User to simpleUserInfo
                if (subPackage.get_package().getInfulancer() != null) {
                    simpleUserInfo userInfo = new simpleUserInfo();
                    userInfo.setId(subPackage.get_package().getInfulancer().getId());
                    userInfo.setUsername(subPackage.get_package().getInfulancer().getUsername());
                    userInfo.setFirstName(subPackage.get_package().getInfulancer().getFirstName());
                    userInfo.setLastName(subPackage.get_package().getInfulancer().getLastName());
                    userInfo.setUsertype(subPackage.get_package().getInfulancer().getUsertype());
                    userInfo.setCoverImage(subPackage.get_package().getInfulancer().getCoverImage());
                    userInfo.setProfileImage(subPackage.get_package().getInfulancer().getProfileImage());
                    searchPkg.setInfulancer(userInfo);
                }

                // Convert Moods to SearchMoods
                if (subPackage.get_package().getMoods() != null && !subPackage.get_package().getMoods().isEmpty()) {
                    List<SearchMood> searchMoods = subPackage.get_package().getMoods().stream()
                            .map(this::convertMoodToSearchMood)
                            .collect(Collectors.toList());
                    searchPkg.setMoods(searchMoods);
                }

                // Convert MediaWrapper to MediaWrapperSearch
                if (subPackage.get_package().getMedias() != null && !subPackage.get_package().getMedias().isEmpty()) {
                    List<MediaWrapperSearch> mediaSearchList = subPackage.get_package().getMedias().stream()
                            .map(this::convertMediaWrapperToSearch)
                            .collect(Collectors.toList());
                    searchPkg.setMedias(mediaSearchList);
                }
            }

            // Save to search repository
            searchPackageRepository.save(searchPkg);

        } catch (Exception e) {
            // Log error but don't fail the restore operation
            System.err.println("Warning: Failed to rebuild search index for restored package " + subPackage.getId() + ": " + e.getMessage());
        }
    }

    /**
     * Convert Mood to SearchMood
     */
    private SearchMood convertMoodToSearchMood(Mood mood) {
        SearchMood searchMood = new SearchMood();
        searchMood.setId(mood.getId());
        searchMood.setType(mood.getType());
        searchMood.setTitle(mood.getTitle());

        if (mood.getMedia() != null) {
            searchMood.setMedia(convertMediaWrapperToSearch(mood.getMedia()));
        }
        if (mood.getSelectedMedia() != null) {
            searchMood.setSelectedMedia(convertMediaWrapperToSearch(mood.getSelectedMedia()));
        }
        if (mood.getMediaIcon() != null) {
            searchMood.setMediaIcon(convertMediaWrapperToSearch(mood.getMediaIcon()));
        }

        return searchMood;
    }

    /**
     * Convert MediaWrapper to MediaWrapperSearch
     */
    private MediaWrapperSearch convertMediaWrapperToSearch(MediaWrapper wrapper) {
        MediaWrapperSearch search = new MediaWrapperSearch();
        search.setType(wrapper.getType());
        search.setCaption(wrapper.getCaption());
        search.setAsset(wrapper.getAsset());
        search.setUrl(wrapper.getUrl());
        search.setMainImage(wrapper.isMainImage());

        if (wrapper.getMedia() != null) {
            SearchMedia searchMedia = new SearchMedia();
            searchMedia.setId(wrapper.getMedia().getId());
            searchMedia.setTitle(wrapper.getMedia().getTitle());
            searchMedia.setCreationDate(wrapper.getMedia().getCreationDate());
            searchMedia.setLastUpdate(wrapper.getMedia().getLastUpdate());
            searchMedia.setSource(wrapper.getMedia().getSource());
            searchMedia.setDescription(wrapper.getMedia().getDescription());
            searchMedia.setVideoUrl(wrapper.getMedia().getVideoUrl());
            searchMedia.setImageCategory(wrapper.getMedia().getImageCategory());
            searchMedia.setVideoDuration(wrapper.getMedia().getVideoDuration());
            searchMedia.setVideoDurationMS(wrapper.getMedia().getVideoDurationMS());
            searchMedia.setThumbnailClipUrl(wrapper.getMedia().getThumbnailClipUrl());
            searchMedia.setThumbnailCaptureUrl(wrapper.getMedia().getThumbnailCaptureUrl());
            searchMedia.setMediaType(wrapper.getMedia().getMediaType());
            searchMedia.setOwnerId(wrapper.getMedia().getOwnerId());
            searchMedia.setVideoSize(wrapper.getMedia().getVideoSize());
            searchMedia.setLastUpdaterId(wrapper.getMedia().getLastUpdaterId());
            searchMedia.setEmployee(wrapper.getMedia().getEmployee());
            searchMedia.setNumberOfReactions(wrapper.getMedia().getNumberOfReactions());
            searchMedia.setNumberOfComments(wrapper.getMedia().getNumberOfComments());
            searchMedia.setUserId(wrapper.getMedia().getUser().getId());
            search.setMedia(searchMedia);
        }

        return search;
    }
}
