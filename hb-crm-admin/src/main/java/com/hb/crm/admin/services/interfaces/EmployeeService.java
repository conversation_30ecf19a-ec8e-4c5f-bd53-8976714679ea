package com.hb.crm.admin.services.interfaces;


import com.hb.crm.admin.beans.CustomUserDetails;
import com.hb.crm.admin.dto.PageDto;
import com.hb.crm.core.beans.Employee;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface EmployeeService extends UserDetailsService {

    Employee save(Employee userAccount) throws Exception;

    PageDto<Employee> getUsers();

    Optional<Employee> findById(String id);

    PageDto<Employee> search(Map<String, Object> obj, int page, int limit);

    Employee getEmployeeByUserName(String username);

    CustomUserDetails loadUserByUsername(String username) throws UsernameNotFoundException;
    void  delete(String id);

    void updateEmployeeConnectionStatus(String userId, boolean isConnected);

    /**
     * Retrieves all employees that are currently online/connected
     * @return List of connected employees
     */
    List<Employee> getOnlineEmployees();
}
