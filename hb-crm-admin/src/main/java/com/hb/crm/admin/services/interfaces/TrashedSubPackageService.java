package com.hb.crm.admin.services.interfaces;

import com.hb.crm.admin.dto.PageDto;
import com.hb.crm.core.Enums.PackageType;
import com.hb.crm.core.beans.SubPackage;
import com.hb.crm.core.beans.Trash.TrashedSubPackage;

import java.time.LocalDateTime;

public interface TrashedSubPackageService {
    
    /**
     * Soft delete a SubPackage by moving it to trash
     * 
     * @param subPackageId The ID of the SubPackage to delete
     * @param deletionReason The reason for deletion (optional)
     * @return The created TrashedSubPackage
     */
    TrashedSubPackage softDeleteSubPackage(String subPackageId, String deletionReason);
    
    /**
     * Restore a SubPackage from trash back to active state
     * 
     * @param trashedSubPackageId The ID of the TrashedSubPackage to restore
     * @return The restored SubPackage
     */
    SubPackage restoreSubPackage(String trashedSubPackageId);
    
    /**
     * Get all trashed packages with pagination
     * 
     * @param page Page number (zero-based)
     * @param size Number of items per page
     * @return PageDto containing the trashed packages
     */
    PageDto<TrashedSubPackage> getTrashedPackages(int page, int size);
    
    /**
     * Get trashed packages by package type with pagination
     * 
     * @param packageType The package type filter
     * @param page Page number (zero-based)
     * @param size Number of items per page
     * @return PageDto containing the filtered trashed packages
     */
    PageDto<TrashedSubPackage> getTrashedPackagesByType(PackageType packageType, int page, int size);
    
    /**
     * Search trashed packages with fuzzy search and pagination
     * 
     * @param query The search query for fuzzy search (optional)
     * @param page Page number (zero-based)
     * @param size Number of items per page
     * @return PageDto containing the search results
     */
    PageDto<TrashedSubPackage> searchTrashedPackages(String query, int page, int size);
    
    /**
     * Search trashed packages with fuzzy search, package type filter, and pagination
     * 
     * @param query The search query for fuzzy search (optional)
     * @param packageType The package type filter (optional)
     * @param page Page number (zero-based)
     * @param size Number of items per page
     * @return PageDto containing the search results
     */
    PageDto<TrashedSubPackage> searchTrashedPackages(String query, PackageType packageType, int page, int size);
    
    /**
     * Get trashed packages deleted within a date range
     * 
     * @param startDate Start date for the range
     * @param endDate End date for the range
     * @param page Page number (zero-based)
     * @param size Number of items per page
     * @return PageDto containing the filtered trashed packages
     */
    PageDto<TrashedSubPackage> getTrashedPackagesByDateRange(LocalDateTime startDate, LocalDateTime endDate, int page, int size);
    
    /**
     * Get a specific trashed package by ID
     * 
     * @param trashedSubPackageId The ID of the trashed package
     * @return The TrashedSubPackage if found
     */
    TrashedSubPackage getTrashedPackageById(String trashedSubPackageId);
    
    /**
     * Permanently delete a trashed package (cannot be undone)
     * 
     * @param trashedSubPackageId The ID of the trashed package to permanently delete
     */
    void permanentlyDeleteTrashedPackage(String trashedSubPackageId);
    
    /**
     * Empty the entire trash (permanently delete all trashed packages)
     */
    void emptyTrash();
    
    /**
     * Get count of trashed packages by type
     * 
     * @param packageType The package type (optional)
     * @return The count of trashed packages
     */
    long getTrashedPackagesCount(PackageType packageType);
    
    /**
     * Get total count of all trashed packages
     * 
     * @return The total count of trashed packages
     */
    long getTotalTrashedPackagesCount();
    
    /**
     * Check if a SubPackage is already in trash
     * 
     * @param subPackageId The original SubPackage ID
     * @return true if the package is in trash, false otherwise
     */
    boolean isSubPackageInTrash(String subPackageId);
}
