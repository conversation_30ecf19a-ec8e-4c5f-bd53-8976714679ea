package com.hb.crm.admin.controllers;

import com.hb.crm.admin.dto.PageDto;
import com.hb.crm.admin.services.interfaces.EmployeeService;
import com.hb.crm.core.beans.Employee;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import io.swagger.v3.oas.annotations.Operation;

@RestController
@RequestMapping(value = "/v1/employee", produces = {MediaType.APPLICATION_JSON_VALUE})
class EmployeeController {

    private Logger logger = LoggerFactory.getLogger(EmployeeController.class);

    private final EmployeeService employeeService;


    @Autowired
    public EmployeeController( EmployeeService employeeService) {
        this.employeeService = employeeService;
    }


    @PreAuthorize("hasAnyRole('admin')")
    @GetMapping("getAdminUsers")
    @ResponseBody
    public PageDto<Employee> getUsers() {
        return employeeService.getUsers();
    }


    @GetMapping("get/{id}")
    @ResponseBody
    public Employee getAdminUserById(@PathVariable("id") String id) {
        final Optional<Employee> byId = employeeService.findById(id);
        final Employee user = byId.get();
        return user;
    }


    @PostMapping("search")
    @ResponseBody
    public PageDto<Employee> searchAdminUsers(@RequestBody Map<String, Object> obj, @RequestParam  int page,  @RequestParam int limit) {

        //TODO Add specification
        return employeeService.search(obj, page, limit);
    }


    @GetMapping("forgetPassword/{userName}/{email}")
    @ResponseBody
    public void forgetPassword(@PathVariable("email") String email, @PathVariable("userName") String userName) {
        //TODO resend reset password email
        logger.debug("forgetPassword request" + userName + "  " + email);
    }


    @GetMapping("resetPassword/{password}/{resetkey}")
    @ResponseBody
    public void resetPassword(@PathVariable("password") String password, @PathVariable("resetkey") String resetkey) {
        //TODO resend reset password email
        logger.debug("forgetPassword request" + password + "  " + resetkey);
    }

    @DeleteMapping("delete/{id}")
    @ResponseBody
    public void delete(@PathVariable("id") String Id ) {
        employeeService.delete(Id);
    }
    @PreAuthorize("permitAll()")
    @PostMapping("cupdate")
    @ResponseBody
    public Employee updateAdminUser(@RequestBody  Employee user) throws Exception {
        return employeeService.save(user);
    }

    @GetMapping("online")
    @Operation(
        summary = "Get online employees",
        description = "Retrieves all employees that are currently connected/online"
    )
    @ResponseBody
    public List<Employee> getOnlineEmployees() {
        return employeeService.getOnlineEmployees();
    }
}
