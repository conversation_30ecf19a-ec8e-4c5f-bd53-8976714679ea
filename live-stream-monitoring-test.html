<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Stream Monitoring Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .controls {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .input-highLight {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .input-highLight label {
            font-weight: 600;
            color: #495057;
        }

        .input-highLight input, .input-highLight select {
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .input-highLight input:focus, .input-highLight select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: #e53e3e;
            color: white;
        }

        .btn-danger:hover {
            background: #c53030;
        }

        .status {
            padding: 20px 30px;
            border-bottom: 1px solid #e9ecef;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            padding: 8px 15px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 14px;
        }

        .status-live {
            background: #c6f6d5;
            color: #22543d;
        }

        .status-offline {
            background: #fed7d7;
            color: #742a2a;
        }

        .status-connecting {
            background: #fef5e7;
            color: #744210;
        }

        .pulse {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .pulse-green {
            background: #38a169;
        }

        .pulse-red {
            background: #e53e3e;
        }

        .pulse-yellow {
            background: #d69e2e;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.3; }
            100% { opacity: 1; }
        }

        .dashboard {
            padding: 30px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .card h3 {
            color: #2d3748;
            margin-bottom: 20px;
            font-size: 1.3em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .stat-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f7fafc;
        }

        .stat-row:last-child {
            border-bottom: none;
        }

        .stat-label {
            font-weight: 500;
            color: #4a5568;
        }

        .stat-value {
            font-weight: 700;
            font-size: 1.2em;
            color: #2d3748;
        }

        .activity-indicator {
            background: #ff6b6b;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin-left: 10px;
            animation: flash 1s ease-in-out;
        }

        @keyframes flash {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }

        .error {
            background: #fed7d7;
            color: #742a2a;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 30px;
            border-left: 4px solid #e53e3e;
        }

        .log {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 30px;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-timestamp {
            color: #718096;
            margin-right: 10px;
        }

        .charts {
            grid-column: 1 / -1;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
        }

        .chart-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-top: 20px;
        }

        .mini-chart {
            height: 100px;
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #e2e8f0;
        }

        .responsive {
            width: 100%;
            overflow-x: auto;
        }

        .video-container {
            position: relative;
            background: #000;
            border-radius: 8px;
            overflow: hidden;
        }

        .btn-warning {
            background: #f6ad55;
            color: white;
        }

        .btn-warning:hover {
            background: #ed8936;
        }

        .force-stop-confirm {
            background: #fed7d7;
            border: 2px solid #e53e3e;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }

        .force-stop-input {
            width: 100%;
            margin: 10px 0;
            padding: 8px 12px;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-live {
            background: #fed7d7;
            color: #742a2a;
        }

        .status-ended {
            background: #e2e8f0;
            color: #4a5568;
        }

        .status-failed {
            background: #fed7d7;
            color: #742a2a;
        }

        .status-stopped {
            background: #fef5e7;
            color: #744210;
        }

        .mp4-not-started {
            background: #e2e8f0;
            color: #4a5568;
        }

        .mp4-in-progress {
            background: #fef5e7;
            color: #744210;
        }

        .mp4-completed {
            background: #c6f6d5;
            color: #22543d;
        }

        .mp4-failed {
            background: #fed7d7;
            color: #742a2a;
        }

        .comment-item {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 10px;
            background: #f8f9fa;
        }

        .comment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .comment-user {
            font-weight: 600;
            color: #2d3748;
        }

        .comment-time {
            font-size: 0.8em;
            color: #718096;
        }

        .comment-text {
            color: #4a5568;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .no-comments-message {
            font-style: italic;
        }

        @media (max-width: 768px) {
            .input-highLight {
                flex-direction: column;
                align-items: stretch;
            }

            .dashboard {
                grid-template-columns: 1fr;
            }

            .chart-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎥 Live Stream Monitor</h1>
            <p>Real-time monitoring dashboard for live stream statistics</p>
        </div>

        <div class="controls">
            <div class="input-highLight" style="margin-bottom: 15px;">
                <label for="streamId">Stream ID:</label>
                <input type="text" id="streamId" placeholder="Enter stream ID to monitor" style="flex: 1; min-width: 200px;">
                
                <label for="apiUrl">API Base URL:</label>
                <input type="text" id="apiUrl" value="http://localhost:8080" style="min-width: 200px;">
                
                <label for="authToken">JWT Token:</label>
                <input type="password" id="authToken" placeholder="Bearer token for authentication" style="flex: 1; min-width: 300px;">
            </div>
            
            <div class="input-highLight">
                <label for="pollInterval">Poll Interval:</label>
                <select id="pollInterval">
                    <option value="3">3 seconds</option>
                    <option value="5" selected>5 seconds</option>
                    <option value="10">10 seconds</option>
                    <option value="15">15 seconds</option>
                    <option value="30">30 seconds</option>
                </select>
                
                <label for="timeWindow">Time Window:</label>
                <select id="timeWindow">
                    <option value="5" selected>5 seconds</option>
                    <option value="10">10 seconds</option>
                    <option value="15">15 seconds</option>
                    <option value="30">30 seconds</option>
                    <option value="60">60 seconds</option>
                </select>
                
                <button class="btn btn-primary" onclick="startMonitoring()">Start Monitoring</button>
                <button class="btn btn-danger" onclick="stopMonitoring()">Stop</button>
                <button class="btn btn-danger" onclick="forceStopStream()" id="forceStopBtn" style="display: none;">Force Stop Stream</button>
            </div>
        </div>

        <div class="status" id="statusBar">
            <div class="status-indicator status-offline">
                <div class="pulse pulse-red"></div>
                <span>Offline</span>
            </div>
        </div>

        <div id="errorContainer"></div>

        <div class="dashboard" id="dashboard" style="display: none;">
            <!-- Live Video Player -->
            <div class="card" style="grid-column: 1 / -1;">
                <h3>📺 Live Stream Video</h3>
                <div style="position: relative; width: 100%; max-width: 800px; margin: 0 auto;">
                    <video 
                        id="liveVideo" 
                        controls 
                        muted 
                        style="width: 100%; height: auto; border-radius: 8px; background: #000;"
                        poster="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='800' height='450' viewBox='0 0 800 450'%3E%3Crect width='800' height='450' fill='%23f8f9fa'/%3E%3Ctext x='400' y='225' text-anchor='middle' fill='%23495057' font-family='Arial' font-size='20'%3EVideo will load when monitoring starts%3C/text%3E%3C/svg%3E">
                        Your browser does not support the video tag.
                    </video>
                    <div id="videoStatus" style="position: absolute; top: 10px; left: 10px; background: rgba(0,0,0,0.7); color: white; padding: 5px 10px; border-radius: 4px; font-size: 12px;">
                        Video Offline
                    </div>
                </div>
            </div>
            
            <!-- Current Statistics Card -->
            <div class="card">
                <h3>📊 Current Statistics</h3>
                <div class="stat-row">
                    <span class="stat-label">👥 Current Viewers</span>
                    <span class="stat-value" id="currentViewers">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">❤️ Total Reactions</span>
                    <span class="stat-value" id="totalReactions">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">💬 Total Comments</span>
                    <span class="stat-value" id="totalComments">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">⏱️ Duration</span>
                    <span class="stat-value" id="duration">-</span>
                </div>
            </div>

            <!-- Recent Activity Card -->
            <div class="card">
                <h3>🔥 Recent Activity</h3>
                <div class="stat-row">
                    <span class="stat-label">New Reactions</span>
                    <span class="stat-value" id="newReactions">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">New Comments</span>
                    <span class="stat-value" id="newComments">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">Reactions/sec</span>
                    <span class="stat-value" id="reactionsPerSec">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">Comments/sec</span>
                    <span class="stat-value" id="commentsPerSec">-</span>
                </div>
            </div>

            <!-- New Comments Feed Card -->
            <div class="card" style="grid-column: 1 / -1;">
                <h3>💬 New Comments in Time Window</h3>
                <div id="newCommentsFeed" style="max-height: 300px; overflow-y: auto;">
                    <div class="no-comments-message" style="text-align: center; color: #718096; padding: 20px;">
                        No new comments in the current time window
                    </div>
                </div>
            </div>

            <!-- Stream Info Card -->
            <div class="card">
                <h3>📺 Stream Information</h3>
                <div class="stat-row">
                    <span class="stat-label">Title</span>
                    <span class="stat-value" id="streamTitle">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">👤 Streamer</span>
                    <span class="stat-value" id="influencer">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">📊 Status</span>
                    <span class="stat-value" id="streamStatus">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">🎬 Created</span>
                    <span class="stat-value" id="createdAt">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">🏁 Ended</span>
                    <span class="stat-value" id="endedAt">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">Last Updated</span>
                    <span class="stat-value" id="lastUpdated">-</span>
                </div>
            </div>

            <!-- Technical Info Card -->
            <div class="card">
                <h3>🔧 Technical Information</h3>
                <div class="stat-row">
                    <span class="stat-label">📺 Playback URL</span>
                    <span class="stat-value" id="playbackUrl" style="font-size: 0.9em; word-break: break-all;">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">🎥 MP4 Status</span>
                    <span class="stat-value" id="mp4Status">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">🗂️ MP4 Key</span>
                    <span class="stat-value" id="mp4Key" style="font-size: 0.9em; word-break: break-all;">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">📡 Channel ARN</span>
                    <span class="stat-value" id="channelArn" style="font-size: 0.8em; word-break: break-all;">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">🔑 Stream Key</span>
                    <span class="stat-value" id="streamKey" style="font-size: 0.8em;">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">📤 Ingest Endpoint</span>
                    <span class="stat-value" id="ingestEndpoint" style="font-size: 0.8em; word-break: break-all;">-</span>
                </div>
            </div>

            <!-- Activity Charts -->
            <div class="card charts">
                <h3>📈 Activity Trends</h3>
                <div class="chart-container">
                    <div class="mini-chart">
                        <canvas id="viewersChart" width="250" height="70"></canvas>
                        <div style="text-align: center; margin-top: 10px; font-weight: 600;">Viewers Over Time</div>
                    </div>
                    <div class="mini-chart">
                        <canvas id="activityChart" width="250" height="70"></canvas>
                        <div style="text-align: center; margin-top: 10px; font-weight: 600;">Activity Rate</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="log" id="logContainer">
            <div class="log-entry">
                <span class="log-timestamp">[Ready]</span>
                <span>Enter a stream ID and click "Start Monitoring" to begin...</span>
            </div>
        </div>
    </div>

    <script>
        let monitoringInterval;
        let isMonitoring = false;
        let chartData = {
            viewers: [],
            reactions: [],
            comments: [],
            timestamps: []
        };

        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                <span style="color: ${type === 'error' ? '#e53e3e' : type === 'success' ? '#38a169' : '#4a5568'}">${message}</span>
            `;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function showError(message) {
            const errorContainer = document.getElementById('errorContainer');
            errorContainer.innerHTML = `<div class="error">❌ ${message}</div>`;
            setTimeout(() => {
                errorContainer.innerHTML = '';
            }, 5000);
        }

        function updateStatus(status, text) {
            const statusBar = document.getElementById('statusBar');
            const statusClasses = {
                'offline': 'status-offline',
                'connecting': 'status-connecting',
                'live': 'status-live'
            };
            const pulseClasses = {
                'offline': 'pulse-red',
                'connecting': 'pulse-yellow',
                'live': 'pulse-green'
            };
            
            statusBar.innerHTML = `
                <div class="status-indicator ${statusClasses[status]}">
                    <div class="pulse ${pulseClasses[status]}"></div>
                    <span>${text}</span>
                </div>
            `;
        }

        function formatDuration(seconds) {
            if (!seconds) return '-';
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            return `${hours}h ${minutes}m ${secs}s`;
        }

        function formatDateTime(dateTimeString) {
            if (!dateTimeString) return '-';
            try {
                const date = new Date(dateTimeString);
                return date.toLocaleString();
            } catch (e) {
                return dateTimeString;
            }
        }

        function createStatusBadge(status) {
            const statusClass = {
                'LIVE': 'status-live',
                'ENDED': 'status-ended',
                'FAILED': 'status-failed',
                'STOPPED': 'status-stopped'
            };
            return `<span class="status-badge ${statusClass[status] || 'status-ended'}">${status}</span>`;
        }

        function createMp4StatusBadge(status) {
            const statusClass = {
                'NOT_STARTED': 'mp4-not-started',
                'IN_PROGRESS': 'mp4-in-progress',
                'COMPLETED': 'mp4-completed',
                'FAILED': 'mp4-failed'
            };
            return `<span class="status-badge ${statusClass[status] || 'mp4-not-started'}">${status || 'NOT_STARTED'}</span>`;
        }

        function truncateText(text, maxLength = 50) {
            if (!text || text === '-') return '-';
            if (text.length <= maxLength) return text;
            return text.substring(0, maxLength) + '...';
        }

        function formatTimeAgo(dateString) {
            if (!dateString) return '';
            try {
                const date = new Date(dateString);
                const now = new Date();
                const diffMs = now - date;
                const diffSeconds = Math.floor(diffMs / 1000);
                const diffMinutes = Math.floor(diffSeconds / 60);
                const diffHours = Math.floor(diffMinutes / 60);
                
                if (diffSeconds < 60) {
                    return `${diffSeconds}s ago`;
                } else if (diffMinutes < 60) {
                    return `${diffMinutes}m ago`;
                } else if (diffHours < 24) {
                    return `${diffHours}h ago`;
                } else {
                    return date.toLocaleDateString();
                }
            } catch (e) {
                return '';
            }
        }

        function updateNewCommentsFeed(commentsDetails) {
            const feed = document.getElementById('newCommentsFeed');
            
            if (!commentsDetails || commentsDetails.length === 0) {
                feed.innerHTML = '<div class="no-comments-message" style="text-align: center; color: #718096; padding: 20px;">No new comments in the current time window</div>';
                return;
            }
            
            let html = '';
            commentsDetails.forEach(comment => {
                const timeAgo = formatTimeAgo(comment.createdDate);
                html += `
                    <div class="comment-item">
                        <div class="comment-header">
                            <div class="comment-user">
                                ${comment.userFullName || comment.username || 'Anonymous User'}
                                <span style="font-weight: 400; color: #718096; margin-left: 5px;">@${comment.username || 'anonymous'}</span>
                            </div>
                            <div class="comment-time">${timeAgo}</div>
                        </div>
                        <div class="comment-text">${comment.commentText || ''}</div>
                    </div>
                `;
            });
            
            feed.innerHTML = html;
        }

        function addActivityIndicator(elementId, count) {
            const element = document.getElementById(elementId);
            const existing = element.querySelector('.activity-indicator');
            if (existing) {
                existing.remove();
            }
            
            if (count > 0) {
                const indicator = document.createElement('span');
                indicator.className = 'activity-indicator';
                indicator.textContent = `+${count}`;
                element.appendChild(indicator);
                
                setTimeout(() => {
                    indicator.remove();
                }, 2000);
            }
        }

        function updateCharts(data) {
            // Keep only last 20 data points
            const maxPoints = 20;
            
            chartData.viewers.push(data.currentViewersCount || 0);
            chartData.reactions.push(data.newReactionsInWindow || 0);
            chartData.comments.push(data.newCommentsInWindow || 0);
            chartData.timestamps.push(new Date());
            
            if (chartData.viewers.length > maxPoints) {
                chartData.viewers.shift();
                chartData.reactions.shift();
                chartData.comments.shift();
                chartData.timestamps.shift();
            }
            
            drawSimpleChart('viewersChart', chartData.viewers, '#667eea');
            drawSimpleChart('activityChart', [...chartData.reactions, ...chartData.comments], '#ff6b6b');
        }

        function drawSimpleChart(canvasId, data, color) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;
            
            ctx.clearRect(0, 0, width, height);
            
            if (data.length < 2) return;
            
            const max = Math.max(...data, 1);
            const step = width / (data.length - 1);
            
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            data.forEach((value, index) => {
                const x = index * step;
                const y = height - (value / max) * height;
                
                if (index === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            });
            
            ctx.stroke();
            
            // Fill area under curve
            ctx.fillStyle = color + '20';
            ctx.lineTo(width, height);
            ctx.lineTo(0, height);
            ctx.closePath();
            ctx.fill();
        }

        function getAuthHeaders() {
            const token = document.getElementById('authToken').value.trim();
            const headers = {
                'Content-Type': 'application/json'
            };
            
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }
            
            return headers;
        }

        async function fetchStreamStats() {
            const streamId = document.getElementById('streamId').value.trim();
            const baseUrl = document.getElementById('apiUrl').value.trim();
            const timeWindow = document.getElementById('timeWindow').value;
            
            if (!streamId) {
                showError('Please enter a stream ID');
                return null;
            }
            
            try {
                const url = `${baseUrl}/v1/live-stream-management/${streamId}/realtime-stats?secondsWindow=${timeWindow}`;
                log(`Fetching: ${url}`);
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: getAuthHeaders()
                });
                
                if (!response.ok) {
                    if (response.status === 401) {
                        throw new Error('Unauthorized - Please check your JWT token');
                    } else if (response.status === 404) {
                        throw new Error('Stream not found');
                    } else if (response.status === 400) {
                        const error = await response.json();
                        throw new Error(error.error || 'Bad request');
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                }
                
                const data = await response.json();
                const commentCount = data.newCommentsInWindow || 0;
                const commentsDetailCount = data.newCommentsDetails ? data.newCommentsDetails.length : 0;
                log(`✓ Data received: ${data.currentViewersCount} viewers, ${data.newReactionsInWindow} new reactions, ${commentCount} new comments (${commentsDetailCount} with details)`, 'success');
                return data;
                
            } catch (error) {
                log(`✗ Error: ${error.message}`, 'error');
                showError(`Failed to fetch stream data: ${error.message}`);
                return null;
            }
        }

        async function loadStreamBasicInfo() {
            const streamId = document.getElementById('streamId').value.trim();
            const baseUrl = document.getElementById('apiUrl').value.trim();
            
            try {
                const url = `${baseUrl}/v1/live-stream-management/${streamId}/monitor`;
                const response = await fetch(url, {
                    method: 'GET',
                    headers: getAuthHeaders()
                });
                
                if (response.ok) {
                    const basicInfo = await response.json();
                    
                    // Load video if playback URL is available
                    if (basicInfo.playbackUrl) {
                        const video = document.getElementById('liveVideo');
                        const videoStatus = document.getElementById('videoStatus');
                        
                        video.src = basicInfo.playbackUrl;
                        video.load();
                        
                        video.onloadstart = () => {
                            videoStatus.textContent = 'Loading...';
                            videoStatus.style.background = 'rgba(212, 178, 24, 0.8)';
                        };
                        
                        video.oncanplay = () => {
                            const status = basicInfo.status || 'UNKNOWN';
                            if (status === 'LIVE') {
                                videoStatus.textContent = '🔴 Video Ready (LIVE)';
                                videoStatus.style.background = 'rgba(56, 161, 105, 0.8)';
                            } else {
                                videoStatus.textContent = `📹 Video Ready (${status})`;
                                videoStatus.style.background = 'rgba(56, 161, 105, 0.8)';
                            }
                            log('✓ Video stream loaded successfully', 'success');
                        };
                        
                        video.onerror = () => {
                            videoStatus.textContent = 'Video Error';
                            videoStatus.style.background = 'rgba(229, 62, 62, 0.8)';
                            log('✗ Error loading video stream', 'error');
                        };
                        
                        // Auto-play only if it's a live stream
                        if (basicInfo.isLive) {
                            setTimeout(() => {
                                video.play().catch(e => {
                                    log('Note: Auto-play blocked by browser. Click play button to start video.', 'info');
                                });
                            }, 1000);
                        } else {
                            log('📹 Video loaded for ended/recorded stream. Click play to watch.', 'info');
                        }
                    }
                    
                    log('✓ Basic stream info loaded', 'success');
                    return basicInfo;
                }
            } catch (error) {
                log(`Warning: Could not load basic stream info: ${error.message}`, 'error');
            }
            return null;
        }

        function updateDashboard(data) {
            // Current Statistics
            document.getElementById('currentViewers').textContent = data.currentViewersCount || 0;
            document.getElementById('totalReactions').textContent = data.totalReactions || 0;
            document.getElementById('totalComments').textContent = data.totalComments || 0;
            document.getElementById('duration').textContent = formatDuration(data.durationSeconds);
            
            // Recent Activity with indicators
            const newReactions = data.newReactionsInWindow || 0;
            const newComments = data.newCommentsInWindow || 0;
            
            document.getElementById('newReactions').textContent = newReactions;
            document.getElementById('newComments').textContent = newComments;
            document.getElementById('reactionsPerSec').textContent = data.reactionsPerSecond || '0.0';
            document.getElementById('commentsPerSec').textContent = data.commentsPerSecond || '0.0';
            
            // Add activity indicators
            addActivityIndicator('newReactions', newReactions);
            addActivityIndicator('newComments', newComments);
            
            // Update new comments feed
            updateNewCommentsFeed(data.newCommentsDetails);
            
            // Stream Information
            document.getElementById('streamTitle').textContent = data.title || 'Unknown';
            document.getElementById('influencer').textContent = data.influencerFullName || data.influencerUsername || 'Unknown';
            document.getElementById('streamStatus').innerHTML = createStatusBadge(data.status || 'UNKNOWN');
            document.getElementById('createdAt').textContent = formatDateTime(data.createdAt);
            document.getElementById('endedAt').textContent = formatDateTime(data.endedAt);
            document.getElementById('lastUpdated').textContent = new Date().toLocaleTimeString();

            // Technical Information
            document.getElementById('playbackUrl').textContent = truncateText(data.playbackUrl, 60);
            document.getElementById('playbackUrl').title = data.playbackUrl || '-'; // Full URL on hover
            document.getElementById('mp4Status').innerHTML = createMp4StatusBadge(data.mp4ConversionStatus);
            document.getElementById('mp4Key').textContent = truncateText(data.mp4Key, 40);
            document.getElementById('mp4Key').title = data.mp4Key || '-'; // Full key on hover
            document.getElementById('channelArn').textContent = truncateText(data.channelArn, 50);
            document.getElementById('channelArn').title = data.channelArn || '-';
            document.getElementById('streamKey').textContent = data.streamKey ? '••••••••' : '-'; // Hide for security
            document.getElementById('streamKey').title = 'Stream key hidden for security';
            document.getElementById('ingestEndpoint').textContent = truncateText(data.ingestEndpoint, 50);
            document.getElementById('ingestEndpoint').title = data.ingestEndpoint || '-';
            
            // Update status
            const isLive = data.isLive;
            updateStatus(isLive ? 'live' : 'offline', 
                        isLive ? `Live • ${data.currentViewersCount} viewers` : `${data.status} • Last check: ${new Date().toLocaleTimeString()}`);
            
            // Show/hide force stop button based on canForceStop flag
            const forceStopBtn = document.getElementById('forceStopBtn');
            if (data.canForceStop || isLive) {
                forceStopBtn.style.display = 'inline-block';
            } else {
                forceStopBtn.style.display = 'none';
            }
            
            // Update video status based on actual stream status
            const videoStatus = document.getElementById('videoStatus');
            const status = data.status || 'UNKNOWN';
            
            if (status === 'LIVE') {
                videoStatus.textContent = '🔴 LIVE';
                videoStatus.style.background = 'rgba(229, 62, 62, 0.8)';
            } else if (status === 'ENDED') {
                videoStatus.textContent = '⏹️ ENDED';
                videoStatus.style.background = 'rgba(113, 128, 150, 0.8)';
            } else if (status === 'FAILED') {
                videoStatus.textContent = '❌ FAILED';
                videoStatus.style.background = 'rgba(229, 62, 62, 0.8)';
            } else if (status === 'STOPPED') {
                videoStatus.textContent = '⏸️ STOPPED';
                videoStatus.style.background = 'rgba(237, 137, 54, 0.8)';
            } else {
                videoStatus.textContent = `📡 ${status}`;
                videoStatus.style.background = 'rgba(0, 0, 0, 0.7)';
            }
            
            // Show MP4 availability for ended streams
            if (data.hasRecording && !isLive) {
                videoStatus.textContent += ' (MP4 Available)';
            }
            
            // Update charts
            updateCharts(data);
            
            // Show dashboard
            document.getElementById('dashboard').style.display = 'grid';
        }

        async function monitorStream() {
            if (!isMonitoring) return;
            
            const data = await fetchStreamStats();
            if (data) {
                updateDashboard(data);
            }
        }

        async function forceStopStream() {
            const streamId = document.getElementById('streamId').value.trim();
            const baseUrl = document.getElementById('apiUrl').value.trim();
            
            if (!streamId) {
                showError('Please enter a stream ID');
                return;
            }
            
            // Show confirmation dialog
            const reason = prompt('Enter reason for force stopping the stream:', 'Admin forced stop');
            if (!reason) {
                return; // User cancelled
            }
            
            try {
                const url = `${baseUrl}/v1/live-stream-management/force-stop/${streamId}?reason=${encodeURIComponent(reason)}`;
                log(`Force stopping stream: ${streamId} with reason: ${reason}`);
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: getAuthHeaders()
                });
                
                if (!response.ok) {
                    if (response.status === 401) {
                        throw new Error('Unauthorized - Please check your JWT token');
                    } else if (response.status === 404) {
                        throw new Error('Stream not found');
                    } else if (response.status === 400) {
                        throw new Error('Bad request - Stream may already be stopped');
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                }
                
                const result = await response.json();
                log(`✓ Stream force stopped successfully`, 'success');
                showSuccess('Stream has been force stopped successfully!');
                
                // Hide force stop button
                document.getElementById('forceStopBtn').style.display = 'none';
                
                // Update video status
                const video = document.getElementById('liveVideo');
                const videoStatus = document.getElementById('videoStatus');
                video.pause();
                videoStatus.textContent = 'Stream Stopped';
                videoStatus.style.background = 'rgba(0, 0, 0, 0.7)';
                
                // Refresh data
                setTimeout(() => {
                    if (isMonitoring) {
                        monitorStream();
                    }
                }, 2000);
                
            } catch (error) {
                log(`✗ Error force stopping stream: ${error.message}`, 'error');
                showError(`Failed to force stop stream: ${error.message}`);
            }
        }

        function showSuccess(message) {
            const errorContainer = document.getElementById('errorContainer');
            errorContainer.innerHTML = `<div class="error" style="background: #c6f6d5; color: #22543d; border-left-color: #38a169;">✅ ${message}</div>`;
            setTimeout(() => {
                errorContainer.innerHTML = '';
            }, 5000);
        }

        async function startMonitoring() {
            const streamId = document.getElementById('streamId').value.trim();
            const token = document.getElementById('authToken').value.trim();
            
            if (!streamId) {
                showError('Please enter a stream ID');
                return;
            }
            
            if (!token) {
                showError('Please enter a JWT token for authentication');
                return;
            }
            
            if (isMonitoring) {
                stopMonitoring();
            }
            
            isMonitoring = true;
            const interval = parseInt(document.getElementById('pollInterval').value) * 1000;
            
            log(`🚀 Starting monitoring for stream: ${streamId}`, 'success');
            log(`📊 Poll interval: ${interval/1000}s, Time window: ${document.getElementById('timeWindow').value}s`);
            log(`🔐 Using JWT authentication`);
            
            updateStatus('connecting', 'Connecting...');
            
            // Load basic stream info and video first
            await loadStreamBasicInfo();
            
            // Initial fetch
            monitorStream();
            
            // Set up interval
            monitoringInterval = setInterval(monitorStream, interval);
            
            // Update button states
            document.querySelector('.btn-primary').textContent = 'Monitoring...';
            document.querySelector('.btn-primary').disabled = true;
        }

        function stopMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
            }
            
            isMonitoring = false;
            updateStatus('offline', 'Monitoring stopped');
            log('⏹️ Monitoring stopped', 'info');
            
            // Reset button states
            document.querySelector('.btn-primary').textContent = 'Start Monitoring';
            document.querySelector('.btn-primary').disabled = false;
        }

        // Handle Enter key in inputs
        document.getElementById('streamId').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                startMonitoring();
            }
        });

        document.getElementById('authToken').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                startMonitoring();
            }
        });

        // Auto-focus stream ID input
        document.getElementById('streamId').focus();

        log('🎥 Live Stream Monitor with Authentication initialized');
        log('🔐 Don\'t forget to enter your JWT token for authentication');
        log('📺 Now supports both LIVE and ENDED streams with full technical details');
        log('💡 Tip: Try polling intervals of 3-5 seconds for active streams, 10+ seconds for monitoring multiple streams');
    </script>
</body>
</html>